AIRFLOW__CORE__DAGS_FOLDER=/opt/dags
AIRFLOW__CORE__EXECUTOR=SequentialExecutor
AIRFLOW__CORE__FERNET_KEY=UlzhwZ3SvK5wd-xeWImk6dstF5PO0JyMd3HQNzBJLy0=
AIRFLOW__CORE__HOSTNAME_CALLABLE=airflow.utils.net.get_host_ip_address

AIRFLOW__CORE__LOAD_EXAMPLES='False'
AIRFLOW__CORE__LOAD_DEFAULT_CONNECTIONS='False'

AIRFLOW__LOGGING__BASE_LOG_FOLDER=/opt/logs

AIRFLOW__EMAIL__DEFAULT_EMAIL_ON_RETRY='False'
AIRFLOW__EMAIL__DEFAULT_EMAIL_ON_FAILURE='False'

_AIRFLOW_WWW_USER_CREATE='True'
_AIRFLOW_WWW_USER_USERNAME=${_AIRFLOW_WWW_USER_USERNAME:-user}
_AIRFLOW_WWW_USER_PASSWORD=${_AIRFLOW_WWW_USER_PASSWORD:-pass}

AIRFLOW__SCHEDULER__DAG_DIR_LIST_INTERVAL=30
AIRFLOW__SCHEDULER__PARSING_PROCESSES=2
AIRFLOW__WEBSERVER__LOG_FETCH_TIMEOUT_SEC=30
AIRFLOW__WEBSERVER__LOG_FETCH_DELAY_SEC=10
AIRFLOW__WEBSERVER__BASE_URL=http://localhost:8081