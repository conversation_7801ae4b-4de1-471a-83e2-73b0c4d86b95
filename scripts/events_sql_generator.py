import sys
import dags.events.config.default as dag_config

events = None
try:
    events = sys.argv[1]
except IndexError:
    raise SystemExit(f'Usage: {sys.argv[0]} event_type1[,event_type2,...]')

events = events.split(',')
for event in events:
    event = event.strip()
    if event not in dag_config.EVENTS:
        print(f'Event with type \'{event}\' not found')
        continue
    params = dag_config.EVENTS_BASE_PARAMS + dag_config.EVENTS[event]
    fields_config = dag_config.EVENTS_PARAMS
    fields = []
    fields_line = []
    length = 0
    exclude = ['eventtype', '_server_date']
    for param in params:
        field_config = fields_config[param] if 'ch_field' in fields_config[param] else None
        if field_config is None:
            # Значит одному и тому же названию параметра на клиенте соответствует несколько колонок в базе
            for config in fields_config[param]:
                if event in config['events']:
                    field_config = config
                    break
        if field_config['ch_field'] in exclude:
            continue
        fields_line.append(field_config['ch_field'])
        length += len(field_config['ch_field'])
        if length >= 60:
            length = 0
            fields.append(', '.join(fields_line))
            fields_line = []
    if len(fields_line) > 0:
        fields.append(', '.join(fields_line))
    fields = ',\n    '.join(fields)

    print(f'\nSELECT {fields}\n'
          f'FROM elka2023_dev.events\n'
          f'WHERE eventtype = \'{event}\'\n'
          f'ORDER BY date DESC\n'
          f'LIMIT 1000\n')
