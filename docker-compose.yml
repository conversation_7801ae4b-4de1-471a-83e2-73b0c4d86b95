version: '3.8'

services:

  airflow:
    build:
      context: ./docker/airflow
      dockerfile: Dockerfile
    restart: always
    networks:
      - stark_personal_network
    volumes:
      - ./dags:/opt/dags/dags
      - ./logs:/opt/logs
      - ./tmp:/tmp
      - ./docker/google-cloud-platform/account.json:/opt/airflow/google-cloud-platform/account.json:ro
      - airflow_data:/opt/airflow
    env_file:
      - ./envs/airflow.config.env
      - ./envs/airflow.env
    command: >
      -c " sleep 10 &&
           /entrypoint db init &&
          (/entrypoint connections add $${CLICKHOUSE_ID} --conn-type 'http' --conn-login $${CLICKHOUSE_USER} --conn-password $${CLICKHOUSE_PASSWORD} --conn-host $${CLICKHOUSE_HOST} --conn-port $${CLICKHOUSE_PORT} --conn-schema $${CLICKHOUSE_SCHEMA} &) &&
          (/entrypoint connections add $${CLICKHOUSE_ETL_ID} --conn-type 'http' --conn-login $${CLICKHOUSE_ETL_USER} --conn-password $${CLICKHOUSE_ETL_PASSWORD} --conn-host $${CLICKHOUSE_ETL_HOST} --conn-port $${CLICKHOUSE_ETL_PORT} --conn-schema $${CLICKHOUSE_ETL_SCHEMA} &) &&
          (/entrypoint connections add $${CLICKHOUSE_2023_ID} --conn-type 'http' --conn-login $${CLICKHOUSE_2023_USER} --conn-password $${CLICKHOUSE_2023_PASSWORD} --conn-host $${CLICKHOUSE_2023_HOST} --conn-port $${CLICKHOUSE_2023_PORT} --conn-schema $${CLICKHOUSE_2023_SCHEMA} &) &&
          (/entrypoint connections add $${CLICKHOUSE_DATAMARTS_ID} --conn-type 'http' --conn-login $${CLICKHOUSE_DATAMARTS_USER} --conn-password $${CLICKHOUSE_DATAMARTS_PASSWORD} --conn-host $${CLICKHOUSE_DATAMARTS_HOST} --conn-port $${CLICKHOUSE_DATAMARTS_PORT} --conn-schema $${CLICKHOUSE_DATAMARTS_SCHEMA} &) &&
          (/entrypoint connections add $${CLICKHOUSE_DATAMARTS_ETL_ID} --conn-type 'http' --conn-login $${CLICKHOUSE_DATAMARTS_ETL_USER} --conn-password $${CLICKHOUSE_DATAMARTS_ETL_PASSWORD} --conn-host $${CLICKHOUSE_DATAMARTS_ETL_HOST} --conn-port $${CLICKHOUSE_DATAMARTS_ETL_PORT} --conn-schema $${CLICKHOUSE_DATAMARTS_ETL_SCHEMA} &) &&
          (/entrypoint connections add $${MYSQL_MASTER_ID} --conn-type 'mysql' --conn-login $${MYSQL_MASTER_USER} --conn-password $${MYSQL_MASTER_PASSWORD} --conn-host $${MYSQL_MASTER_HOST} --conn-port $${MYSQL_MASTER_PORT} --conn-schema $${MYSQL_MASTER_SCHEMA} &) &&
          (/entrypoint connections add $${MYSQL_SHARD1_ID} --conn-type 'mysql' --conn-login $${MYSQL_SHARD1_USER} --conn-password $${MYSQL_SHARD1_PASSWORD} --conn-host $${MYSQL_SHARD1_HOST} --conn-port $${MYSQL_SHARD1_PORT} --conn-schema $${MYSQL_SHARD1_SCHEMA} &) &&
          (/entrypoint connections add $${MYSQL_SHARD2_ID} --conn-type 'mysql' --conn-login $${MYSQL_SHARD2_USER} --conn-password $${MYSQL_SHARD2_PASSWORD} --conn-host $${MYSQL_SHARD2_HOST} --conn-port $${MYSQL_SHARD2_PORT} --conn-schema $${MYSQL_SHARD2_SCHEMA} &) &&
          (/entrypoint connections add $${MYSQL_OPLOGS_ID} --conn-type 'mysql' --conn-login $${MYSQL_OPLOGS_USER} --conn-password $${MYSQL_OPLOGS_PASSWORD} --conn-host $${MYSQL_OPLOGS_HOST} --conn-port $${MYSQL_OPLOGS_PORT} --conn-schema $${MYSQL_OPLOGS_SCHEMA} &) &&
          (/entrypoint connections add $${MONGO_MASTER_ID} --conn-type 'mongo' --conn-host $${MONGO_MASTER_HOST} --conn-port $${MONGO_MASTER_PORT} --conn-schema $${MONGO_MASTER_SCHEMA} &) &&
          (/entrypoint connections add $${MONGO_SHARD1_ID} --conn-type 'mongo' --conn-host $${MONGO_SHARD1_HOST} --conn-port $${MONGO_SHARD1_PORT} --conn-schema $${MONGO_SHARD1_SCHEMA} &) &&
          (/entrypoint connections add $${MONGO_SHARD2_ID} --conn-type 'mongo' --conn-host $${MONGO_SHARD2_HOST} --conn-port $${MONGO_SHARD2_PORT} --conn-schema $${MONGO_SHARD2_SCHEMA} &) &&
          (/entrypoint connections add $${REDIS_MASTER_ID} --conn-type 'http' --conn-host $${REDIS_MASTER_HOST} --conn-port $${REDIS_MASTER_PORT} --conn-schema $${REDIS_MASTER_SCHEMA} &) &&
          (/entrypoint connections add $${AWS_ID} --conn-type 'aws' --conn-login $${AWS_USER} --conn-password $${AWS_PASSWORD} &) &&
          (/entrypoint connections add $${GOOGLE_CLOUD_ID} --conn-type 'google_cloud_platform' --conn-extra $${GOOGLE_CLOUD_EXTRA} &) &&
          (/entrypoint connections add $${SLACK_CONNECTION_ID} --conn-type 'http' --conn-host $${SLACK_CONNECTION_HOST} --conn-password $${SLACK_CONNECTION_PASSWORD} &) &&
          (/entrypoint webserver &) &&
           /entrypoint scheduler"
    healthcheck:
      test: [ "CMD", "curl", "--fail", "http://localhost:8080/health" ]
      interval: 10s
      timeout: 10s
      retries: 5
    ports:
      - "8081:8080"

  clickhouse:
    image: clickhouse/clickhouse-server:********
    volumes:
      - ./docker/clickhouse/users.xml:/etc/clickhouse-server/users.xml:ro
      - clickhouse_data:/var/lib/clickhouse
    ports:
      - "9001:9000"
      - "8123:8123"
    networks:
      - stark_personal_network

  clickhouse-init:
    image: clickhouse/clickhouse-server:********
    env_file:
      - ./envs/clickhouse.env
    volumes:
      - ./database/schema:/var/clickhouse/schema
      - ./database/tables:/var/clickhouse/tables
      - ./database/changes.sql:/var/clickhouse/changes/changes.sql:ro
      - ./docker/clickhouse/init.sh:/tmp/init.sh
    depends_on:
      - clickhouse
    networks:
      - stark_personal_network
    entrypoint: ['/bin/sh', '-c', '/tmp/init.sh']

  redis:
    image: redis:7
    command: [ "redis-server", "--appendonly no", "--save \"\"" ]
    volumes:
      - redis_data:/data
    networks:
      - stark_personal_network
    ports:
      - "6380:6379"
volumes:
    clickhouse_data:
    airflow_data:
    redis_data:

# Общая сеть с приложением
networks:
  stark_personal_network:
    name: stark_personal_network
    external: true