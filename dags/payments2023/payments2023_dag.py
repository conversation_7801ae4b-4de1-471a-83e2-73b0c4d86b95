from airflow import DAG
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
import dags.payments2023.config.default as dag_config
import jinja2
from airflow.exceptions import AirflowSkipException

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)


def load_payments():
    environment = jinja2.Environment()
    template = environment.from_string(dag_config.SQL_EXTRACT_PLATFORMS_AND_CONNECTION_IDS)
    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_PREV_YEAR))
    sql = template.render(
        host=conn.host,
        schema=conn.schema,
        table=dag_config.CH_PAYMENTS_TABLE,
        user=conn.login,
        password=conn.password,
    )
    LoggerService.logger.info(sql)
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    platforms_connections = {}
    for row in ch.stream(sql):
        platform, connection_id = row
        if platform not in platforms_connections:
            platforms_connections[platform] = {}
        if connection_id not in platforms_connections[platform]:
            platforms_connections[platform][connection_id] = 0

    maxs = ch.get_max_column_values_flat(
        dag_config.CH_PAYMENTS_PREV_YEAR_TABLE,
        'id',
        ['_platform', '_connection_id']
    )
    for data in maxs:
        platforms_connections[data['_platform']][data['_connection_id']] = data['value']

    LoggerService.logger.info(platforms_connections)
    if len(platforms_connections) == 0:
        raise AirflowSkipException

    environment = jinja2.Environment()
    template = environment.from_string(dag_config.SQL_EXTRACT_PAYMENTS_PREV_YEAR)
    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_PREV_YEAR))
    last_platform_key = list(platforms_connections.keys())[-1]
    last_connection_id_key = list(platforms_connections[last_platform_key].keys())[-1]
    sql = template.render(
        host=conn.host,
        schema=conn.schema,
        table=dag_config.CH_PAYMENTS_TABLE,
        table_target=dag_config.CH_PAYMENTS_PREV_YEAR_TABLE,
        user=conn.login,
        password=conn.password,
        maxs=platforms_connections,
        last_platform=last_platform_key,
        last_connection_id=last_connection_id_key
    )
    LoggerService.logger.info(sql)
    ch.execute(sql)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    load_payments_task = PythonOperator(
        task_id=f'load_payments',
        python_callable=load_payments,
    )

if __name__ == '__main__':
    dag.cli()
