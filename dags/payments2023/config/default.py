from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for loading payments data from Elka 2023 Clickhouse',
}

SETTINGS = {

}

CH_PAYMENTS_TABLE = 'payments'
CH_PAYMENTS_PREV_YEAR_TABLE = 'payments2023'

SQL_EXTRACT_PLATFORMS_AND_CONNECTION_IDS = '''
SELECT _platform, _connection_id 
FROM remote('{{ host }}', {{ schema }}.{{ table }}, '{{ user }}', '{{ password }}')
GROUP BY _platform, _connection_id
'''

SQL_EXTRACT_PAYMENTS_PREV_YEAR = 'INSERT INTO {{ table_target }}\n' \
    '{% for platform, connection_id_maxs in maxs.items() %}' \
        '{% for connection_id, id in connection_id_maxs.items() %}' \
            'SELECT *\n' \
            'FROM remote(\'{{ host }}\', {{ schema }}.{{ table }}, \'{{ user }}\', \'{{ password }}\')\n' \
            'WHERE _platform = \'{{ platform }}\' AND _connection_id = \'{{ connection_id }}\' AND id > {{ id }}\n' \
            '{% if not (platform == last_platform and connection_id == last_connection_id) %}' \
            'UNION ALL\n' \
            '{% endif %}' \
        '{% endfor %}' \
    '{% endfor %}'
