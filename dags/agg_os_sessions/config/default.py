from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for calculating aggregated retention data',
}

SETTINGS = {
}

SQL_OS_SESSIONS = '''
    INSERT INTO agg_os_sessions (platform_type, dt, os, sessions)    
    SELECT platform_type         
     , toDate('{date}') AS dt
     , CASE WHEN device_vendor = 'Apple' THEN 'iOS'
            WHEN device_vendor != 'Apple' OR platform_type = 'android' THEN 'Android'
            ELSE 'PC' 
            END AS os
     , COUNT(*) AS sessions
    FROM user_sessions 
    WHERE toDate(date_start, 'Europe/Moscow') = toDate('{date}')
    GROUP BY 1, 2, 3
'''