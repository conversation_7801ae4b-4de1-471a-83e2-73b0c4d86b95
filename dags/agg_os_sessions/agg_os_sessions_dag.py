from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService
from airflow.operators.python import PythonOperator
from airflow.hooks.base import BaseHook
from datetime import datetime, timedelta, timezone
import dags.agg_os_sessions.config.default as dag_config
from dags.lib.helpers.date_helper import str_to_date_time, get_current_date
from airflow.exceptions import AirflowSkipException

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)


def calculate():

    date_yesterday = get_current_date() - timedelta(days=1)

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    max = ch.get_max_column_value(
        'agg_os_sessions',
        'dt',
    )

    LoggerService.logger.info(f'Yesterday: {date_yesterday} ({type(date_yesterday)}), max: {max} ({type(max)})')
    if max >= date_yesterday:
        raise AirflowSkipException

    sql = dag_config.SQL_OS_SESSIONS.format(
        date=date_yesterday
    )
    LoggerService.logger.info(sql)
    ch.execute(sql)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    calculate_dau_task = PythonOperator(
        task_id=f'calculate',
        python_callable=calculate,
    )

if __name__ == '__main__':
    dag.cli()
