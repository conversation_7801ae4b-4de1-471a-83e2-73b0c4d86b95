from typing import Dict
import os
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from airflow import DAG
from airflow.operators.python import PythonOperator
from dags.lib.services.mysql_service import MySQLService
from dags.lib.services.clickhouse_service import ClickhouseService, PARQUET_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
from dags.payments.payments_row_transformer import PaymentsRowTransformer
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
import dags.payments.config.default as dag_config
from dags.lib.services.file_path_service import FilePathService

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_PAYMENTS_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_SHARDS),
    }
)

# Переопределение константы, но в ConfigService это добавлять кажется оверкиллом, можно подумать еще
if ENV == EnvironmentService.DEVELOP:
    dag_config.MYSQL_PAYMENTS_TABLE = 'payments_test'


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_PAYMENTS_EXTRACTED_TABLE)

    for path in file_paths.get_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def stream_payments(mysql_connection_config: Dict):
    mysql_connection_id = mysql_connection_config['id']
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    max_payments_update_time = ch.get_max_column_value(
        dag_config.CH_PAYMENTS_TABLE,
        '_update_time',
        {'_connection_id': mysql_connection_id}
    )
    ms = MySQLService(mysql_connection_id)
    table_name = config.get_setting('mysql_table_name')
    sql = f'''
            SELECT 
                id, 
                user_id, 
                session_id, 
                clan_id,
                payment_time as date,
                platform as platform_type,
                app_type,
                level,
                factory_level,
                merge_level,
                progress,
                cash,
                energy,
                award_status as status,
                platform_payment_id as transaction_id,
                price,
                local_price,
                local_currency as currency,
                item_type,
                item_id,
                award,
                payment_number,
                item_extra_id,
                gift_id,
                bonus_id,
                passing_mode_level,
                no_ads,
                tokens,
                update_time as _update_time,
                payment_system
            FROM {table_name}  
            WHERE update_time > {max_payments_update_time} 
        '''
    for row in ms.stream(sql):
        yield row


def extract_payments(mysql_connection_config: Dict):

    table_name = config.get_setting('mysql_table_name')

    LoggerService.start_extracting(
        mysql_connection_config['platform'],
        mysql_connection_config['id'],
        table_name
    )

    file_path = file_paths.get_path(mysql_connection_config['id'])
    counter = 0
    data_payments = []
    for row in stream_payments(mysql_connection_config):
        data_payments.append(PaymentsRowTransformer.process(row, mysql_connection_config))
        counter += 1
    data_frame = pd.DataFrame(data_payments)
    table = pa.Table.from_pandas(data_frame)
    pq.write_table(table, file_path)
    LoggerService.finish_extracting(counter)


def move_payments():
    try:
        from_table = dag_config.CH_PAYMENTS_EXTRACTED_TABLE
        to_table = dag_config.CH_PAYMENTS_TABLE
        LoggerService.start_move_data(from_table, to_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {to_table} SELECT * FROM {from_table}')
    except Exception as e:
        LoggerService.error_move_data(from_table, to_table, str(e))
        raise


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    extract_payments_task = [
        PythonOperator(
            task_id=f'extract_payments_{row["id"]}',
            python_callable=extract_payments,
            op_kwargs={
                'mysql_connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_SHARDS)
    ]

    load_payments_task = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(row['id']),
            table_name=dag_config.CH_PAYMENTS_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'load_payments_{row["id"]}'
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_SHARDS)
    ]

    move_payments_extracted_table = PythonOperator(
        task_id='move_payments',
        python_callable=move_payments
    )

    load_task = load_payments_task.pop(0)
    clear_temp_data_task >> extract_payments_task >> load_task

    for task in load_payments_task:
        load_task >> task
        load_task = task
    load_task >> move_payments_extracted_table

if __name__ == '__main__':
    dag.cli()
