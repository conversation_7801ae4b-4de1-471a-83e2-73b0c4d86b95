from typing import Dict
from datetime import datetime
from dags.lib.services.platform_service import PlatformService
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data


class PaymentsRowTransformer:
    def process(self: Dict, connection_config: Dict) -> Dict:
        self['_connection_id'] = connection_config['id']
        self['_platform'] = connection_config['platform']
        self['platform_type'] = PlatformService.get_name(self['platform_type'])

        try:
            self['date'] = get_converted_value(val=self['date'], type_val=datetime)
        except Exception as e:
            self['date'] = get_default_value(type_val=datetime)

        self['xsolla'] = self['payment_system'] if self['payment_system'] == 1 else 0
        del self['payment_system']

        return prepare_parquet_data(self)
