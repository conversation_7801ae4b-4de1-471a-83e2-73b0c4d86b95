from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load appsflyer raw data',
}

SETTINGS = {
    'token_v2': 'eyJhbGciOiJBMjU2S1ciLCJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0.RcSFllyFKWRekUXl0D-mHZcakeThR1cGme-UEqU6YE_pT4LjF4NFwA.80gIZPJQKjeQbq1q.TzSE_TSCeLqvzsLJA4R3B8mWG442aFy_U610d4b0-9DkTmNpY0O0-gOfWkWfHrIiiOdbvglPYn0BNfU9BJacsIrgzhwvdUOVVAGl7OJeXQdIDEw9Beciz7FKmEl1tm4D8GqTLpF1mEN-W7Xg7MvEmGUm2mOHB-upR5oH5gVyfeRq_22W-JwZ2bpxSvLzp0mDDTs5drwRz69fFLV6vKkg_qlmjZBjwDCI22NbNtPiFq6eVs5UnaS7U707g31z1ZU3MnucPr8d63696NS4zLwPONLoMuRSSU8qdCOSZfHtqMvgqZDLuPG74P_3XeuZL_aur0PnuFRnCp7ZysZAO4zGfWoJBUaCaEKeh8-lbIg2rWf-0OLEdKy71uCptriQ0eGGePTmaKdnj2Tx0Fp4BVa0LAOebJsjtju3k0P0IOvRpsbvpnbDFM49QKF5ze7eMd7dFGTimVz0EPkR835HmU0vTDnDfgoImyiD5tuKAJrP7wOYQ2c6Ltks1h9FUlShqSmIqolXLrh-JEpSPJlD5tCt.pk7r26gVen8wp-eVm7eK9A',
    'api_url': 'https://hq1.appsflyer.com/api/raw-data/export/app/{app_id}/{report_type}/v5',
    'applications': [
        {
            'app_id': 'id6446766326',
            'app_id_human': 'Goblins Wood iOS',
        },
        {
            'app_id': 'idle.goblins.wood.tycoon',
            'app_id_human': 'Goblins Wood Android',
        },
        {
            'app_id': 'com.progameslab.magic.seasons2024.farm.match.collect',
            'app_id_human': 'Magic Seasons 2024 Android',
        },
        {
            'app_id': 'id6463635391',
            'app_id_human': 'Magic Seasons 2024 iOS',
        },
        {
            'app_id': 'com.progameslab.my.cooking.merge.kitchen.madness.love.fever.travel.town.idle',
            'app_id_human': 'My Cooking Merge Android',
        },
        {
            'app_id': 'com.progameslab.tile.master.triple.puzzle.match.travel.farm.adventure',
            'app_id_human': 'Magic Seasons 2025 Android',
        },
        {
            'app_id': 'id6504757972',
            'app_id_human': 'Magic Seasons 2025 iOS',
        },
    ],
    'reports': [
        {
            'type': 'installs_report',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                                 'match_type,device_category,keyword_match_type,att,campaign_type,is_lat'
        },
        {
            'type': 'in_app_events_report',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                                 'match_type,device_category,keyword_match_type,att,campaign_type,is_lat'
        },
        {
            'type': 'uninstall_events_report',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,keyword_match_type,is_lat'
        },
        {
            'type': 'organic_installs_report',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                                 'keyword_match_type,att,campaign_type,is_lat'
        },
        {
            'type': 'organic_in_app_events_report',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,'
                                 'keyword_match_type,att,campaign_type'
        },
        {
            'type': 'organic_uninstall_events_report',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,keyword_match_type,is_lat'
        },
        {
            'type': 'ad_revenue_organic_raw',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                                 'match_type,device_category,keyword_match_type,is_lat'
        },
        {
            'type': 'ad_revenue_raw',
            'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                                 'match_type,device_category,keyword_match_type,is_lat'
        },
    ]
}

EXTRACT_STATE_CONNECTION_ID = 'appsflyer_raw_data_api'
TOKEN_V2_KEY = 'token_v2'
API_URL_KEY = 'api_url'
REPORTS_KEY = 'reports'
APPLICATIONS_KEY = 'applications'

CH_RAW_DATA_TABLE = 'appsflyer_raw_data'
CH_RAW_DATA_EXTRACTED_TABLE = 'appsflyer_raw_data_extracted'

DATE_START = '2024-06-01'

TMP_RAW_DATA_FILE_PATH = 'datamarts_appsflyer_raw_data_[connection].csv'
TMP_RAW_DATA_TRANSFORMED_FILE_PATH = 'datamarts_appsflyer_raw_data_td_[connection].csv'

TMP_RAW_DATA_EXTRACT_STATE_FILE_PATH = 'datamarts_appsflyer_raw_data_es_[connection].csv'

