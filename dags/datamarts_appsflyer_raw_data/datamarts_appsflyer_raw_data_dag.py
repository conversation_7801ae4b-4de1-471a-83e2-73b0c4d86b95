from typing import Dict
import io
import os
import re
import csv
import pandas as pd
import requests
from airflow.operators.empty import EmptyOperator
from airflow.exceptions import AirflowSkipException
from datetime import datetime, timezone, timedelta, time
from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.extract_state_service import ExtractStateService
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.hooks.base import BaseHook
import dags.datamarts_appsflyer_raw_data.config.default as dag_config
from dags.lib.helpers.date_helper import str_to_date_time, get_current_date, date_time_to_str
from dags.lib.services.file_path_service import FilePathService
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator, CSV_DATA_TYPE
from dags.config.default import CH_EXTRACT_STATE_TABLE
from dags.lib.services.load_data_http_service import HTTP_METHOD_GET
from dags.lib.helpers.csv_helper import csv_stream
from dags.datamarts_appsflyer_raw_data.report_row_transformer import ReportRowTransformer
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)


def get_source_name(app_id: str, report_type: str) -> str:
    return f'{app_id}_{report_type}'


sources = [{'app_id': app['app_id'], 'app_id_human': app['app_id_human'], 'report_type': report['type'],
            'additional_fields': report['additional_fields'],
            'days_per_run': app.get('days_per_run', 0)}
           for app in config.get_setting(dag_config.APPLICATIONS_KEY)
           for report in config.get_setting(dag_config.REPORTS_KEY)]

source_names = [get_source_name(source['app_id'], source['report_type']) for source in sources]

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_RAW_DATA_FILE_PATH: source_names,
        dag_config.TMP_RAW_DATA_TRANSFORMED_FILE_PATH: source_names,
        dag_config.TMP_RAW_DATA_EXTRACT_STATE_FILE_PATH: source_names,
    }
)


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    ch.truncate(dag_config.CH_RAW_DATA_EXTRACTED_TABLE)

    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def get_period(connection_id: str, source_name: str, days_per_run: int) -> (str, list):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    extract_state = ExtractStateService(ch)
    date_start = str_to_date_time(dag_config.DATE_START).replace(tzinfo=timezone.utc)

    date_max = extract_state.get_last_value(
        connection_id,
        source_name,
        'date',
        ExtractStateService.DATE_TYPE
    ).replace(tzinfo=timezone.utc)

    if date_max < date_start:
        date_max = date_start

    period = None
    date_to = date_max

    delta = 1
    # У отчетов по revenue задержка в поступление данных в сырые отчеты - 2 дня
    if source_name.find('ad_revenue') >= 0:
        delta = 2

    date_yesterday = datetime.combine((datetime.now() - timedelta(days=delta)).date(), time.min).replace(
        tzinfo=timezone.utc)

    date_max += timedelta(days=1)
    if date_max > date_yesterday:
        date_max = date_to
    else:
        date_to = date_max
        if days_per_run > 0:
            date_to += timedelta(days=days_per_run)
        if date_to > date_yesterday:
            date_to = date_yesterday
        period = {'from': date_time_to_str(date_max),
                  'to': date_time_to_str(datetime.combine(date_to.date(), time.max))}
        date_max = date_to

    LoggerService.logger.info(f'Date max is {date_max}, period is {period}')
    return (date_time_to_str(date_max), period)


def extract_report_data(app_id: str, report_type: str, additional_fields: str, from_date: str, to_date: str):
    token = config.get_setting(dag_config.TOKEN_V2_KEY)
    params = {
        'from': from_date,
        'to': to_date,
        'additional_fields': additional_fields,
        'maximum_rows': 1000000,
        'currency': 'preferred',
    }
    headers = {
        'accept': 'text/csv',
        'authorization': f'Bearer {token}'
    }
    request_url = (config.get_setting(dag_config.API_URL_KEY)).format(app_id=app_id, report_type=report_type)

    LoggerService.logger.info(f'Try get {request_url}, params: {params}')
    res = requests.request(HTTP_METHOD_GET, request_url, params=params, headers=headers)

    if res.status_code == 200:
        with open(file_paths.get_path(get_source_name(app_id, report_type), dag_config.TMP_RAW_DATA_FILE_PATH), 'w',
                  newline='', encoding='utf-8') as f:
            f.write(res.text)
    else:
        raise Exception(f'Wrong response code ({res.status_code}), text: {res.text}')


def extract_data(app_id: str, report_type: str, additional_fields: str, days_per_run: int):
    connection_id = dag_config.EXTRACT_STATE_CONNECTION_ID
    source_name = get_source_name(app_id, report_type)
    (date_max, period) = get_period(connection_id, source_name, days_per_run)

    if period is None:
        raise AirflowSkipException

    extract_report_data(app_id, report_type, additional_fields, period['from'], period['to'])
    extract_state = {
        'connection_id': connection_id,
        'source_name': source_name,
        'key_name': 'date',
        'last_value_date': date_max
    }

    file_path_state_header = file_paths.get_header_path(dag_config.TMP_RAW_DATA_EXTRACT_STATE_FILE_PATH)
    if not os.path.exists(file_path_state_header):
        LoggerService.logger.info(f'Write to file path state header: {file_path_state_header}')
        with open(file_path_state_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, extract_state.keys(), delimiter=';')
            writer.writeheader()

    file_path_state = file_paths.get_path(source_name, dag_config.TMP_RAW_DATA_EXTRACT_STATE_FILE_PATH)
    with open(file_path_state, 'w', newline='', encoding='utf-8') as file:
        header = extract_state.keys()
        writer = csv.DictWriter(file, header, delimiter=';')
        writer.writerow(extract_state)


def transform_data(source_name: str, app_id_human: str):
    path = file_paths.get_path(source_name, dag_config.TMP_RAW_DATA_TRANSFORMED_FILE_PATH)
    with open(path, 'w', newline='', encoding='utf-8') as f:
        writer = None
        file_path = file_paths.get_path(source_name, dag_config.TMP_RAW_DATA_FILE_PATH)
        LoggerService.logger.info(f'Processing {file_path}...')
        counter = 0
        for row in csv_stream(file_path):
            row = ReportRowTransformer.process(row, source_name, app_id_human)
            if counter == 0:
                fieldnames = list(row.keys())
                writer = csv.DictWriter(f, delimiter=';', fieldnames=fieldnames)
                writer.writeheader()
            writer.writerow(row)
            counter += 1
        if counter > 999900:
            raise Exception(f'Almost 1 mln rows in report {path}, counter: {counter}')
        LoggerService.logger.info(f'Rows: {counter}')


def move_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    ch.execute(
        f'INSERT INTO {dag_config.CH_RAW_DATA_TABLE} SELECT * FROM {conn_etl.schema}.{dag_config.CH_RAW_DATA_EXTRACTED_TABLE}')


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_files_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    move_data_task = PythonOperator(
        task_id='move_data',
        python_callable=move_data
    )

    for source in sources:
        source_name = get_source_name(source['app_id'], source['report_type'])

        extract_data_task = PythonOperator(
            task_id=f'extract_{source_name}',
            python_callable=extract_data,
            op_kwargs={
                'app_id': source['app_id'],
                'report_type': source['report_type'],
                'additional_fields': source['additional_fields'],
                'days_per_run': source['days_per_run']
            },
        )

        transform_data_task = PythonOperator(
            task_id=f'transform_{source_name}',
            python_callable=transform_data,
            op_kwargs={
                'source_name': get_source_name(source['app_id'], source['report_type']),
                'app_id_human': source['app_id_human']
            },
        )

        import_data_task = DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL),
            file_path=file_paths.get_path(source_name, dag_config.TMP_RAW_DATA_TRANSFORMED_FILE_PATH),
            table_name=dag_config.CH_RAW_DATA_EXTRACTED_TABLE,
            data_type=CSV_DATA_TYPE,
            task_id=f'import_{source_name}'
        )

        clear_temp_files_task >> extract_data_task >> transform_data_task >> import_data_task >> move_data_task

    concat_extract_state_task = ConcatCSVOperator(
        task_id='concat_es',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_RAW_DATA_EXTRACT_STATE_FILE_PATH,
        connection_ids=source_names
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_RAW_DATA_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_es'
    )

    move_data_task >> concat_extract_state_task >> import_extract_state_task

if __name__ == '__main__':
    dag.cli()
