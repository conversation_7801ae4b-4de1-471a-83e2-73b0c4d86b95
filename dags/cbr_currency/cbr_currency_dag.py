from airflow import DAG
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
import dags.cbr_currency.config.default as dag_config

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)


def extract_load_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    max = ch.get_max_column_value(
        dag_config.CH_TABLE,
        'date'
    )
    LoggerService.logger.info(f'Max date: {max}')

    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
    sql = f'INSERT INTO {dag_config.CH_TABLE} (num_code, char_code, nominal, name, value, date) ' \
          f'SELECT num_code, char_code, nominal, name, value, date ' \
          f'FROM remote(\'{conn.host}\', {conn.schema}.{dag_config.CH_SOURCE_TABLE}, \'{conn.login}\', \'{conn.password}\') ' \
          f'WHERE date > toDate(\'{max}\')'
    ch.execute(sql)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    extract_load_data_task = PythonOperator(
        task_id='extract_load_data',
        python_callable=extract_load_data)

if __name__ == '__main__':
    dag.cli()
