from typing import Dict
from datetime import datetime
from dags.lib.services.platform_service import PlatformService
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data


class PromoStartRowTransformer:
    @staticmethod
    def process(self: Dict, connection_config: Dict) -> Dict:
        self['_connection_id'] = connection_config['id']
        self['platform_type'] = PlatformService.get_name(self['platform_type'])

        for date_filed in ['start_time', 'expire_time']:
            try:
                self[date_filed] = get_converted_value(val=self[date_filed], type_val=datetime)
            except Exception as e:
                self[date_filed] = get_default_value(type_val=datetime)

        return prepare_parquet_data(self)
