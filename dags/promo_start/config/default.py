from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load promo start information',
}

SETTINGS = {}

CH_PROMO_START_EXTRACTED_TABLE = 'mysql_promo_start_extracted'
CH_PROMO_START_TABLE = 'promo_start'

MYSQL_PROMO_START_TABLE = 'promo_start'

TMP_PROMO_START_FILE_PATH = 'promo_start_[connection].parquet'

TMP_PROMO_START_EXTRACT_STATE_FILE_PATH = 'promo_start_es_[connection].csv'
