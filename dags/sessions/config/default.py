from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load user sessions',
}

SETTINGS = {

}

CH_SESSISON_EXTRACTED_TABLE = 'user_sessions_extracted'
CH_SESSIONS_TABLE = 'user_sessions'

MYSQL_SESSIONS_TABLE = 'users_session'

TMP_SESSIONS_FILE_PATH = 'sessions_[connection].csv'

TMP_SESSIONS_EXTRACT_STATE_FILE_PATH = 'sessions_es_[connection].csv'

SESSIONS_TABLE_FIELDS = {
    'id': {'ch_field': '_id'},
    'platform': {'ch_field': 'platform_type'},
    'num': {'ch_field': 'session_number'},
    'os_name': {'ch_field': 'os'},
}
