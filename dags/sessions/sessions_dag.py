from typing import Dict
import os
import csv
from airflow import DAG
from airflow.operators.python import PythonOperator
from dags.lib.services.mysql_service import MySQLService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.operators.data_to_clickhouse_operator import DataTo<PERSON>lickHouseOperator
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
import dags.sessions.config.default as dag_config
from dags.sessions.sessions_row_transformer import SessionsRowTransformer
from dags.lib.services.extract_state_service import ExtractStateService
from dags.config.default import CH_EXTRACT_STATE_TABLE
from airflow.hooks.base import BaseHook
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.lib.services.file_path_service import FilePathService

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_SESSIONS_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_SHARDS),
        dag_config.TMP_SESSIONS_EXTRACT_STATE_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_SHARDS)
    }
)


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_SESSISON_EXTRACTED_TABLE)

    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def stream_sessions(connection_config: Dict):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    extract_state = ExtractStateService(ch)
    max = extract_state.get_last_value(connection_config['id'], dag_config.MYSQL_SESSIONS_TABLE, 'id')
    sql = f'''
        SELECT *
        FROM {dag_config.MYSQL_SESSIONS_TABLE}  
        WHERE id > {max}
        ORDER BY id
        LIMIT 500000
    '''
    ms = MySQLService(connection_config['id'])
    for row in ms.stream(sql):
        yield row


def extract_sessions(connection_config: Dict):
    path = file_paths.get_path(connection_config['id'], dag_config.TMP_SESSIONS_FILE_PATH, )
    path_header = file_paths.get_header_path(dag_config.TMP_SESSIONS_FILE_PATH)

    path_state = file_paths.get_path(connection_config['id'], dag_config.TMP_SESSIONS_EXTRACT_STATE_FILE_PATH)
    path_state_header = file_paths.get_header_path(dag_config.TMP_SESSIONS_EXTRACT_STATE_FILE_PATH)

    gen = stream_sessions(connection_config)
    try:
        row = SessionsRowTransformer.process(next(gen), connection_config)
    except StopIteration:
        return

    # TODO: Скорей всего будем использовать формат Parquet
    if not os.path.exists(path_header):
        with open(path_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, row.keys(), delimiter=';')
            writer.writeheader()

    counter = 0
    max_value = 0
    with open(path, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, row.keys(), delimiter=';')
        writer.writerow(row)
        counter += 1
        for row in gen:
            row = SessionsRowTransformer.process(row, connection_config)
            max_value = max(max_value, row['_id'])
            writer.writerow(row)
            counter += 1

    state = {
        'connection_id': connection_config['id'],
        'source_name': dag_config.MYSQL_SESSIONS_TABLE,
        'key_name': 'id',
        'last_value_int': max_value
    }
    if not os.path.exists(path_state_header):
        with open(path_state_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, state.keys(), delimiter=';')
            writer.writeheader()
    with open(path_state, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, state.keys(), delimiter=';')
        writer.writerow(state)

    # Писать статистику в базу
    LoggerService.finish_extracting(counter)


def load_sessions():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.execute(f'''
        INSERT INTO {dag_config.CH_SESSIONS_TABLE} (
            _id, _connection_id, _platform, platform_type, app_type, user_id, clan_id, session_id, 
            session_number, test_id, test_group, date_start, browser_name, device_vendor, device_model, 
            referrer_source, google_services, level, factory_level, 
            merge_level, progress, cash, energy, app_build, server_static, geo_country, os, ip, device_id,
            no_ads, tokens, passing_mode_level, standard_mode_collection, tournament_mode_collection,
            passing_mode_collection, standard_mode_collection_number, tournament_mode_collection_number,
            passing_mode_collection_number, progress_level)
        SELECT 
            _id, _connection_id, _platform, platform_type, app_type, user_id, clan_id, session_id, 
            session_number, test_id, test_group, date_start, browser_name, device_vendor, device_model, 
            referrer_source, google_services, level, factory_level, 
            merge_level, progress, cash, energy, app_build, server_static, geo_country, os, ip, device_id,
            no_ads, tokens, passing_mode_level, standard_mode_collection, tournament_mode_collection,
            passing_mode_collection, standard_mode_collection_number, tournament_mode_collection_number,
            passing_mode_collection_number, progress_level
        FROM {dag_config.CH_SESSISON_EXTRACTED_TABLE}
    ''')


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data)

    extract_sessions_tasks = [
        PythonOperator(
            task_id=f'extract_sessions_{row["id"]}',
            python_callable=extract_sessions,
            op_kwargs={
                'connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_SHARDS)
    ]

    concat_sessions_files_task = ConcatCSVOperator(
        task_id='concat_perf_users_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_SESSIONS_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MYSQL_SHARDS)
    )

    import_sessions_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_SESSIONS_FILE_PATH),
        table_name=dag_config.CH_SESSISON_EXTRACTED_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_perf_users'
    )

    load_sessions_task = PythonOperator(
        task_id=f'load_sessions',
        python_callable=load_sessions,
    )

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_SESSIONS_EXTRACT_STATE_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MYSQL_SHARDS)
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_SESSIONS_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_extract_state'
    )

    clear_temp_data_task >> extract_sessions_tasks >> concat_sessions_files_task >> import_sessions_task >> \
    load_sessions_task >> concat_extract_state_files_task >> import_extract_state_task

if __name__ == '__main__':
    dag.cli()
