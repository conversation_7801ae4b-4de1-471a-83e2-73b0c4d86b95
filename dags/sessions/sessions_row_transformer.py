from typing import Dict
import dags.sessions.config.default as dag_config
from dags.lib.services.platform_service import PlatformService


class SessionsRowTransformer:

    @staticmethod
    def __set_fields(fields_config: Dict, document: Dict):
        transformed = {}
        for key, value in document.items():
            if key in fields_config:
                transformed[fields_config[key]['ch_field']] = value
            else:
                transformed[key] = document[key]
        return transformed

    @staticmethod
    def process(row: Dict, connection_config: Dict) -> Dict:
        transformed = SessionsRowTransformer.__set_fields(dag_config.SESSIONS_TABLE_FIELDS, row)
        transformed['_connection_id'] = connection_config['id']
        transformed['_platform'] = connection_config['platform']
        transformed['platform_type'] = (
            PlatformService.get_name(transformed['platform_type'])
            if connection_config['platform'] == 'mobile'
            else connection_config['platform']
        )
        return transformed
