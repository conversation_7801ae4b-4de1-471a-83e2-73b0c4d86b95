from typing import Dict
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data


class GladesPairsRowTransformer:
    @staticmethod
    def process(row: Dict, connection_config: Dict, table_name: str, tables_template: str) -> Dict:
        row['_connection_id'] = connection_config['id']
        row['_source_name'] = table_name
        row['platform_type'] = connection_config['platform']
        row['screen'] = GladesPairsRowTransformer.get_screen_id_by_table_name(tables_template, table_name)

        return prepare_parquet_data(row)

    @staticmethod
    def get_screen_id_by_table_name(tables_template: str, table_name: str) -> int:
        return int(table_name[len(tables_template):])
