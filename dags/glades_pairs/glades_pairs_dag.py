import os
import pandas as pd
import csv
from typing import Dict
from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE, PARQUET_DATA_TYPE
from dags.lib.services.mysql_service import MySQLService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.file_path_service import FilePathService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.services.logger_service import LoggerService
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.exceptions import AirflowSkipException
from dags.lib.helpers.mysql_helper import stream_tables_by_template
from dags.glades_pairs.glades_pairs_row_transformer import GladesPairsRowTransformer
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.lib.services.redis_service import RedisService
import dags.glades_pairs.config.default as dag_config
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
LAST_REFRESH_TIME = 'last_refresh'
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_GLADES_PAIRS_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_MASTERS),
        dag_config.TMP_GLADES_PAIRS_EXTRACT_STATE_FILE_PATH:
            config.get_connection_ids(ConfigService.CONN_REDIS_MASTERS)
    }
)


def clear_temp_data():
    for path in file_paths.get_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def check_redis_change(redis_connection_config: Dict):
    LoggerService.logger.info('Start check refresh time glades_pairs')
    rs = RedisService(redis_connection_config['id'])
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch_main_connection_id = config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN)
    extract_state = ExtractStateService(ch)

    last_redis_refresh_time = 0
    mysql_connection_config = None
    for cfg in config.get_connections(ConfigService.CONN_MYSQL_MASTERS):
        if cfg['platform'] == redis_connection_config['platform']:
            mysql_connection_config = cfg

    for table in stream_tables_by_template(dag_config.MYSQL_TABLES_TEMPLATE, mysql_connection_config):
        screen_id = GladesPairsRowTransformer.get_screen_id_by_table_name(dag_config.MYSQL_TABLES_TEMPLATE, table)
        redis_key = f'{dag_config.REDIS_REFRESH_KEY_PREFIX}{screen_id}'
        LoggerService.logger.info(f'Get redis data by key "{redis_key}"')
        last_screen_calc_time_src = rs.get_val(redis_key)
        if last_screen_calc_time_src is not None:
            last_screen_calc_time = int(rs.get_val(redis_key))
        else:
            last_screen_calc_time = 0

        if last_screen_calc_time > last_redis_refresh_time:
            last_redis_refresh_time = last_screen_calc_time

    last_ch_refresh_time = extract_state.get_last_value(
        ch_main_connection_id,
        dag_config.CH_GLADES_PAIRS_TABLE,
        f'{LAST_REFRESH_TIME}_{redis_connection_config["platform"]}'
    )

    if last_ch_refresh_time >= last_redis_refresh_time:
        raise AirflowSkipException

    path_state = file_paths.get_path(
        redis_connection_config['id'],
        dag_config.TMP_GLADES_PAIRS_EXTRACT_STATE_FILE_PATH
    )
    path_state_header = file_paths.get_header_path(dag_config.TMP_GLADES_PAIRS_EXTRACT_STATE_FILE_PATH)

    states = [{
        'connection_id': ch_main_connection_id,
        'source_name': dag_config.CH_GLADES_PAIRS_TABLE,
        'key_name': f'{LAST_REFRESH_TIME}_{redis_connection_config["platform"]}',
        'last_value_int': last_redis_refresh_time
    }]

    header = states[0].keys()

    if not os.path.exists(path_state_header):
        with open(path_state_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, header, delimiter=';')
            writer.writeheader()

    with open(path_state, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, header, delimiter=';')
        for state in states:
            writer.writerow(state)

    LoggerService.logger.info('Finish check refresh time glades_pairs')


def clear_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    ch.truncate(dag_config.CH_GLADES_PAIRS_TABLE)


def stream_glades_pairs(table_name: str, mysql_connection_config: Dict):
    sql = f'''
        SELECT 
            user_id_1, user_id_2,
            level_1 AS level_id_1,
            level_2 AS level_id_2,
            progress_1 AS progress_score_1,
            progress_2 AS progress_score_2,
            stages_sum_1, stages_sum_2,
            toys_count_1 AS toys_sum_1,
            toys_count_2 AS toys_sum_2,
            votes_1, votes_2
        FROM {table_name}
    '''

    ms = MySQLService(mysql_connection_config['id'])
    LoggerService.logger.info(sql)
    for row in ms.stream(sql):
        yield row


def extract_glades_pairs(mysql_connection_config: Dict):
    LoggerService.start_extracting(
        mysql_connection_config['platform'],
        mysql_connection_config['id'],
        f'{dag_config.MYSQL_TABLES_TEMPLATE}*'
    )

    file_path = file_paths.get_path(mysql_connection_config['id'])

    counter = 0
    glades_pairs_data = []
    for table in stream_tables_by_template(dag_config.MYSQL_TABLES_TEMPLATE, mysql_connection_config):
        for row in stream_glades_pairs(table, mysql_connection_config):
            counter += 1
            glades_pairs_data.append(
                GladesPairsRowTransformer.process(
                    row,
                    mysql_connection_config,
                    table,
                    dag_config.MYSQL_TABLES_TEMPLATE
                )
            )

    pd.DataFrame(glades_pairs_data).to_parquet(file_path)
    LoggerService.finish_extracting(counter)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    check_redis_change_time_tasks = [
        PythonOperator(
            task_id=f'check_change_glades_pairs_{row["id"]}',
            python_callable=check_redis_change,
            op_kwargs={
                'redis_connection_config': row
            },
        ) for row in config.get_connections(ConfigService.CONN_REDIS_MASTERS)
    ]

    clear_data_task = PythonOperator(
        task_id='clear_data',
        python_callable=clear_data
    )

    extract_glades_pairs_tasks = [
        PythonOperator(
            task_id=f'extract_glades_pairs_{row["id"]}',
            python_callable=extract_glades_pairs,
            op_kwargs={
                'mysql_connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_MASTERS)
    ]

    start_load_glades_pairs_tasks = EmptyOperator(
        task_id='start_load_glades_pairs'
    )

    load_glades_pairs_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN),
            file_path=file_paths.get_path(row['id']),
            table_name=dag_config.CH_GLADES_PAIRS_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'load_glades_pairs_{row["id"]}'
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_MASTERS)
    ]

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_GLADES_PAIRS_EXTRACT_STATE_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_REDIS_MASTERS)
    )

    load_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_GLADES_PAIRS_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='load_extract_state'
    )

    clear_temp_data_task >> check_redis_change_time_tasks >> clear_data_task >> extract_glades_pairs_tasks \
        >> start_load_glades_pairs_tasks >> load_glades_pairs_tasks >> concat_extract_state_files_task \
        >> load_extract_state_task

if __name__ == '__main__':
    dag.cli()
