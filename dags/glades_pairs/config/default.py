from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load glades pairs information',
}

SETTINGS = {}

CH_GLADES_PAIRS_TABLE = 'glades_pairs'

TMP_GLADES_PAIRS_FILE_PATH = 'glades_pairs_[connection].parquet'
TMP_GLADES_PAIRS_EXTRACT_STATE_FILE_PATH = 'glades_pairs_es_[connection].csv'

MYSQL_TABLES_TEMPLATE = 'glade_duel_screen'
REDIS_REFRESH_KEY_PREFIX = 'miniG:cg:lastUpd:'
