from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load CDN logs',
}

SETTINGS = {
    'bucket': 'sg-edge-raw-logs',
    'directories': {
        'elka2024-mobile-static': {
            'date_start': '2024-06-28 10:00:00',
            'hours_process_at_once': 6,
        },
        'elka2024-client-global-mobile': {
            'date_start': '2024-06-27 20:00:00',
            'hours_process_at_once': 6,
        },
        'elka2025-mobile-static': {
            'date_start': '2024-09-15 16:00:00',
            'hours_process_at_once': 12,
        }
    }
}

BUCKET_PATH_TEMPLATE = '{directory}/{year}/{month}/{day}/{hour}'

HOURS_DELAY_COUNT = 2

AWS_BUCKET_KEY = 'bucket'
AWS_DIRECTORIES_KEY = 'directories'

PAGE_SIZE = 1024

CH_EXTRACTED_TABLE = 'cdn_logs_extracted'
CH_TABLE = 'cdn_logs'

TMP_FILE_PATH = 'cdn_logs_[connection].csv'
TMP_EXTRACT_STATE_FILE_PATH = 'cdn_logs_es_[connection].csv'

ROW_MAPPING = {
    0: {'ch_field': 'remote_addr', 'ch_type': 'String'},
    1: {'ch_field': None, 'ch_type': None},
    2: {'ch_field': 'remote_user', 'ch_type': 'String'},
    # 21/Jun/2024:08:01:58 +0000
    # 'validation': {'format': '%Y-%m-%d %H:%M:%S %z'}},
    3: {'ch_field': 'time_local', 'ch_type': 'DateTime', 'format': '%d/%b/%Y:%H:%M:%S %z'},
    4: {'ch_field': 'request', 'ch_type': 'String'},
    5: {'ch_field': 'status', 'ch_type': 'UInt16'},
    6: {'ch_field': 'body_bytes_sent', 'ch_type': 'UInt32'},
    7: {'ch_field': 'http_referer', 'ch_type': 'String'},
    8: {'ch_field': 'http_user_agent', 'ch_type': 'String'},
    9: {'ch_field': 'bytes_sent', 'ch_type': 'UInt32'},
    10: {'ch_field': 'edgename', 'ch_type': 'LCString'},
    11: {'ch_field': 'scheme', 'ch_type': 'LCString'},
    12: {'ch_field': 'host', 'ch_type': 'LCString'},
    13: {'ch_field': 'request_time', 'ch_type': 'Decimal'},
    14: {'ch_field': 'upstream_response_time_raw', 'ch_type': 'String'},
    15: {'ch_field': 'request_length', 'ch_type': 'UInt32'},
    16: {'ch_field': 'http_range', 'ch_type': 'String'},
    17: {'ch_field': 'responding_node', 'ch_type': 'String'},
    18: {'ch_field': 'upstream_cache_status', 'ch_type': 'LCString'},
    19: {'ch_field': 'upstream_response_length_raw', 'ch_type': 'String'},
    20: {'ch_field': 'upstream_addr', 'ch_type': 'String'},
    21: {'ch_field': 'gcdn_api_client_id', 'ch_type': 'UInt32'},
    22: {'ch_field': 'gcdn_api_resource_id', 'ch_type': 'UInt32'},
    23: {'ch_field': 'uid_got', 'ch_type': 'String'},
    24: {'ch_field': 'uid_set', 'ch_type': 'String'},
    25: {'ch_field': 'geoip_country_code', 'ch_type': 'LCString'},
    26: {'ch_field': 'geoip_city', 'ch_type': 'String'},
    27: {'ch_field': 'shield_type', 'ch_type': 'LCString'},
    28: {'ch_field': 'server_addr', 'ch_type': 'String'},
    29: {'ch_field': 'server_port', 'ch_type': 'UInt16'},
    30: {'ch_field': 'upstream_status_raw', 'ch_type': 'String'},
    31: {'ch_field': None, 'ch_type': None},
    32: {'ch_field': 'upstream_connect_time_raw', 'ch_type': 'String'},
    33: {'ch_field': 'upstream_header_time_raw', 'ch_type': 'String'},
    34: {'ch_field': 'shard_addr', 'ch_type': 'String'},
    35: {'ch_field': 'geoip2_data_asnumber', 'ch_type': 'UInt64'},
    36: {'ch_field': 'connection', 'ch_type': 'UInt64'},
    37: {'ch_field': 'connection_requests', 'ch_type': 'UInt32'},
    38: {'ch_field': 'request_id', 'ch_type': 'String'},
    39: {'ch_field': 'http_x_forwarded_proto', 'ch_type': 'LCString'},
    40: {'ch_field': 'http_x_forwarded_request_id', 'ch_type': 'String'},
    41: {'ch_field': 'ssl_cipher', 'ch_type': 'String'},
    42: {'ch_field': 'ssl_session_id', 'ch_type': 'String'},
    43: {'ch_field': 'ssl_session_reused', 'ch_type': 'LCString'},
    44: {'ch_field': 'sent_http_content_type', 'ch_type': 'LCString'},
    45: {'ch_field': 'real_tcpinfo_rtt', 'ch_type': 'UInt32'},
    46: {'ch_field': 'http_x_forwarded_http_ver', 'ch_type': 'LCString'},
    47: {'ch_field': 'vp_enabled', 'ch_type': 'UInt8'},
}
