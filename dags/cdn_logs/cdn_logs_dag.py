import os
import csv
from datetime import datetime, timedelta, timezone, time
from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import dags.cdn_logs.config.default as dag_config
from dags.lib.helpers.date_helper import str_to_date_time, date_time_to_str
from dags.lib.services.file_path_service import FilePathService
from airflow.exceptions import AirflowSkipException
import gzip
import shutil
from dags.cdn_logs.row_transformer import RowTransformer
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.services.extract_state_service import ExtractStateService, CH_EXTRACT_STATE_TABLE
from airflow.hooks.base import BaseHook

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)

AWS_BUCKET = config.get_setting(dag_config.AWS_BUCKET_KEY)

directories_config = config.get_setting(dag_config.AWS_DIRECTORIES_KEY)
directories = directories_config.keys()
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_FILE_PATH: directories,
        dag_config.TMP_EXTRACT_STATE_FILE_PATH: directories,
    }
)

dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}


def clear_temp_data():
    LoggerService.logger.info(f'Env: {ENV}')
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_EXTRACTED_TABLE)
    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def get_extract_period(directory: str) -> (datetime, datetime):
    extract_state = ExtractStateService(ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL)))
    bucket = config.get_setting(dag_config.AWS_BUCKET_KEY)
    date_max = extract_state.get_last_value(
        bucket,
        directory,
        'date',
        ExtractStateService.DATE_TYPE
    ).replace(tzinfo=timezone.utc)

    date_start = str_to_date_time(
        directories_config[directory]['date_start'],
        '%Y-%m-%d %H:%M:%S').replace(tzinfo=timezone.utc)
    date_start = max(date_start, date_max)

    date_max = datetime.now() - timedelta(hours=dag_config.HOURS_DELAY_COUNT)
    date_max = datetime.combine(date_max, time(date_max.hour, 0, 0)).replace(tzinfo=timezone.utc)
    LoggerService.logger.info(f'Period from {date_start} to {date_max}')
    return (date_start, date_max)


def extract_and_transform(directory: str):
    (date_from, date_to) = get_extract_period(directory)
    if date_from > date_to:
        raise AirflowSkipException

    s3_hook = S3Hook(aws_conn_id=config.get_connection(ConfigService.CONN_AWS_S3))
    bucket = config.get_setting(dag_config.AWS_BUCKET_KEY)

    writer = None
    file_path_transformed = file_paths.get_path(directory, dag_config.TMP_FILE_PATH)

    with open(file_path_transformed, mode='w') as file:
        for i in range(directories_config[directory]['hours_process_at_once']):
            (year, month, day, hour) = date_time_to_str(date_from, '%Y-%m-%d-%H').split('-')
            prefix = dag_config.BUCKET_PATH_TEMPLATE.format(
                directory=directory,
                year=year,
                month=month,
                day=day,
                hour=hour
            )
            LoggerService.logger.info(f'Bucket {bucket}, prefix {prefix}')

            keys = s3_hook.list_keys(bucket, prefix=prefix, page_size=dag_config.PAGE_SIZE)
            for key in keys:
                LoggerService.logger.info(key)
                if key.find('.log.gz') == 0:
                    continue

                filename = key.replace(prefix + '/', '')
                prefix_datetime = date_time_to_str(date_from)
                file_path = BASE_PATH + '/' + filename

                if not os.path.exists(file_path):
                    obj = s3_hook.get_key(bucket_name=bucket, key=key)
                    obj.download_file(file_path)

                file_path_unzipped = file_path.replace('.gz', '')
                with gzip.open(file_path, 'rb') as f_in:
                    with open(file_path_unzipped, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

                with open(file_path_unzipped) as file_raw:
                    for row in file_raw:
                        row = RowTransformer.process(row, prefix, prefix_datetime, filename)
                        if writer is None:
                            writer = csv.DictWriter(file, delimiter=',', lineterminator='\n', fieldnames=row.keys())
                            writer.writeheader()
                        writer.writerow(row)
                    os.remove(file_path_unzipped)
                    os.remove(file_path)

            date_from += timedelta(hours=1)
            if date_from > date_to:
                break

    path_state = file_paths.get_path(directory, dag_config.TMP_EXTRACT_STATE_FILE_PATH)
    with open(path_state, 'w', newline='', encoding='utf-8') as file:
        state = {
            'connection_id': bucket,
            'source_name': directory,
            'key_name': 'date',
            'last_value_date': date_time_to_str(date_from)
        }
        writer = csv.DictWriter(file, state.keys(), delimiter=';')
        writer.writeheader()
        writer.writerow(state)


def move():
    from_table = dag_config.CH_EXTRACTED_TABLE
    to_table = dag_config.CH_TABLE
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    sql = f'INSERT INTO {to_table} SELECT * FROM {conn_etl.schema}.{from_table}'
    LoggerService.logger.info(sql)
    ch.execute(sql)


with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id=f'clear_temp_data',
        python_callable=clear_temp_data,
    )

    move_task = PythonOperator(
        task_id=f'move',
        python_callable=move,
    )

    for directory in directories:
        extract_and_transform_task = PythonOperator(
            task_id=f'extract_and_transform_{directory}',
            python_callable=extract_and_transform,
            op_kwargs={
                'directory': directory
            }
        )
        load_task = DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(directory, dag_config.TMP_FILE_PATH),
            table_name=dag_config.CH_EXTRACTED_TABLE,
            data_type=CSV_DATA_TYPE,
            delimiter=',',
            task_id=f'load_{directory}'
        )

        clear_temp_data_task >> extract_and_transform_task >> load_task >> move_task

    import_extract_state_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(directory, dag_config.TMP_EXTRACT_STATE_FILE_PATH),
            table_name=CH_EXTRACT_STATE_TABLE,
            data_type=CSV_DATA_TYPE,
            task_id=f'import_es_{directory}'
        )
        for directory in directories
    ]

    prev_task = import_extract_state_tasks.pop()
    move_task >> prev_task
    for task in import_extract_state_tasks:
        prev_task >> task
        prev_task = task

if __name__ == '__main__':
    dag.cli()
