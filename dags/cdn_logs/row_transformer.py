import dags.cdn_logs.config.default as dag_config
from datetime import datetime
from dags.lib.services.logger_service import LoggerService
from dags.lib.helpers.date_helper import str_to_date_time, date_time_to_str

class RowTransformer:

    @staticmethod
    def __check_value(param_config, value):
        if value is None:
            return
        if param_config['ch_type'] == 'DateTime':
            datetime.strptime(value, param_config['format'])
        elif param_config['ch_type'] in ['UInt8', 'UInt16', 'UInt32', 'UInt64']:
            try:
                value = int(value)
            except ValueError as e:
                LoggerService.logger.error(f'param {param_config["ch_field"]}: {value}')
                raise e
        elif param_config['ch_type'] in ['Decimal']:
            try:
                value = float(value)
            except ValueError as e:
                LoggerService.logger.error(f'param {param_config["ch_field"]}: {value}')
                raise e
        return None

    @staticmethod
    def process(row: str, prefix: str, prefix_datetime: str, filename: str):

        transformed = {
            '_prefix': prefix,
            '_prefix_datetime': prefix_datetime,
            '_filename': filename
        }
        row = row.lstrip('"').rstrip('"\n').split('" "')
        for index, value in enumerate(row):
            # TODO: Появился кейс 49-й колонки в данных
            if index not in dag_config.ROW_MAPPING:
                continue
            param_config = dag_config.ROW_MAPPING[index]
            if param_config['ch_field'] is None:
                continue
            param_name = param_config['ch_field']
            if param_name in ['time_local', 'edgename', 'responding_node']:
                value = value.lstrip('[').rstrip(']')
            elif param_name in ['geoip2_data_asnumber'] and value == '-':
                value = None
            elif param_config['ch_type'] in ['String', 'LCString'] and value == '-':
                value = None

            RowTransformer.__check_value(param_config, value)

            if param_name == 'time_local':
                value = date_time_to_str(datetime.strptime(value, param_config['format']))
            transformed[param_config['ch_field']] = value

        for param in ['upstream_response_length_raw', 'upstream_response_time_raw', 'upstream_status_raw', 'upstream_connect_time_raw']:
            value = transformed[param]
            if value is not None:
                if value.find(',') >= 0:
                    value = value.split(',')[0]
                elif value.find(';') >= 0:
                    value = value.split(';')[0]
                elif value.find(' : ') >= 0:
                    value = value.split(' : ')[0]
            if value == '-':
                value = None
            param_transformed = param.replace('_raw', '')
            transformed[param_transformed] = value

        if transformed['http_range'] is not None:
            value = transformed['http_range'].replace('bytes=', '').split('-')
            transformed['http_range_bytes_from'] = int(value[0])
            try:
                transformed['http_range_bytes_to'] = int(value[1]) if len(value[1]) > 0 else None
            except ValueError as e:
                LoggerService.logger.error(f'value "{value}", "{transformed["http_range"]}"')
                raise e
        else:
            transformed['http_range_bytes_from'] = None
            transformed['http_range_bytes_to'] = None

        return transformed

