from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load cbr currency information',
}

CH_USERS_TABLE = 'users_info'
CH_DATAMARTS_USERS_TABLE = 'elka2024_users_info_load'

CH_PAYMENTS_TABLE = 'payments'
CH_DATAMARTS_PAYMENTS_TABLE = 'elka2024_payments_load'

# Эмпирически считаем, что если с даты вставки прошло столько секунд, значит запрос на вставку уже завершился целиком
DELAY_SECONDS = 180

SETTINGS = {

}
