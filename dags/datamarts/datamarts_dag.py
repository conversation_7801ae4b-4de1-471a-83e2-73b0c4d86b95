from airflow import DAG
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
import dags.datamarts.config.default as dag_config

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)


def extract_load_users():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))

    sql = f'SELECT toUnixTimestamp(MAX(_insert_datetime))' \
          f'FROM remote(\'{conn.host}\', {conn.schema}.{dag_config.CH_DATAMARTS_USERS_TABLE}, \'{conn.login}\', \'{conn.password}\')'
    max_destination_date = ch.get_first(sql)[0]
    LoggerService.logger.info(f'Max destination timestamp: {max_destination_date}')

    max_source_date = ch.get_max_column_value(
        dag_config.CH_USERS_TABLE,
        '_insert_datetime',
        None,
        True
    )
    LoggerService.logger.info(f'Max source timestamp: {max_source_date}')

    if max_source_date == max_destination_date:
        LoggerService.logger.info(f'No new data found at source')
    elif max_source_date - max_destination_date < dag_config.DELAY_SECONDS:
        # Чтобы избежать коллизий двух запущенных рядом по времени DAG, отложим миграцию данных на следующий запуск
        LoggerService.logger.info(f'Skip data migration to avoid collisions')
        return

    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
    # WHERE-условие здесь приведет походу к фулл-скану, из-за относительно небольшого объема данных это приемлемо
    # Альтернативным решением было бы иметь копию таблицы с первичным ключом по _insert_datetime
    sql = f'INSERT INTO FUNCTION remote(\'{conn.host}\', {conn.schema}.{dag_config.CH_DATAMARTS_USERS_TABLE}, \'{conn.login}\', \'{conn.password}\') ' \
          f'(_insert_datetime, platform_type, user_id, user_id_platform, level, login_time, install_time, referrer, referrer_data, referrer_user_id) ' \
          f'SELECT _insert_datetime, platform_type, user_id, user_id_platform, level, login_time, install_time, referrer, referrer_data, referrer_user_id ' \
          f'FROM {dag_config.CH_USERS_TABLE} ' \
          f'WHERE _insert_datetime > toDateTime({max_destination_date}) AND _insert_datetime <= toDateTime({max_source_date})'
    ch.execute(sql)


def extract_load_payments():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))

    sql = f'SELECT toUnixTimestamp(MAX(_insert_datetime))' \
          f'FROM remote(\'{conn.host}\', {conn.schema}.{dag_config.CH_DATAMARTS_PAYMENTS_TABLE}, \'{conn.login}\', \'{conn.password}\')'
    max_destination_date = ch.get_first(sql)[0]
    LoggerService.logger.info(f'Max destination timestamp: {max_destination_date}')

    max_source_date = ch.get_max_column_value(
        dag_config.CH_PAYMENTS_TABLE,
        '_insert_datetime',
        None,
        True
    )
    LoggerService.logger.info(f'Max source timestamp: {max_source_date}')

    if max_source_date == max_destination_date:
        LoggerService.logger.info(f'No new data found at source')
    elif max_source_date - max_destination_date < dag_config.DELAY_SECONDS:
        LoggerService.logger.info(f'Skip data migration to avoid collisions')
        return

    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
    sql = f'INSERT INTO FUNCTION remote(\'{conn.host}\', {conn.schema}.{dag_config.CH_DATAMARTS_PAYMENTS_TABLE}, \'{conn.login}\', \'{conn.password}\') ' \
          f'(_connection_id, _insert_datetime, platform_type, id, user_id, user_id_platform, payment_date, price, currency, local_price, status) ' \
          f'SELECT p._connection_id, p._insert_datetime, p.platform_type, id, p.user_id, u.user_id_platform, p.date, p.price, p.currency, p.local_price, p.status ' \
          f'FROM {dag_config.CH_PAYMENTS_TABLE} p, {dag_config.CH_USERS_TABLE} u ' \
          f'WHERE p._insert_datetime > toDateTime({max_destination_date}) ' \
          f'AND p._insert_datetime <= toDateTime({max_source_date}) ' \
          f'AND p.user_id = u.user_id'
    ch.execute(sql)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    extract_load_users_task = PythonOperator(
        task_id='extract_load_users',
        python_callable=extract_load_users)

    extract_load_payments_task = PythonOperator(
        task_id='extract_load_payments',
        python_callable=extract_load_payments)

    # Можно и параллельно запускать, в данном случае вообще без разницы,
    # задачи достаточно легковесные, данных не много
    extract_load_users_task >> extract_load_payments_task

if __name__ == '__main__':
    dag.cli()
