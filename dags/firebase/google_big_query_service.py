from airflow.providers.google.cloud.hooks.bigquery import <PERSON><PERSON><PERSON>y<PERSON><PERSON>
from dags.lib.services.logger_service import LoggerService
from dags.lib.helpers.date_helper import get_current_datetime, date_time_to_str
from dags.lib.services.environment_service import EnvironmentService
import dags.firebase.config.default as dag_config
from typing import List, Dict
from datetime import date, timedelta
import json


class BigQueryService:
    FIREBASE_CRASHLYTICS_SUB_TABLE_PREFIX = 'com_progameslab_magic_seasons2024_farm_match_collect_'
    FIREBASE_ANALYTICS_BASE_TABLE = 'analytics_404763353'
    FIREBASE_MESSAGING_SUB_TABLE = 'data'

    @staticmethod
    def extract(table: str, day: date, platform: str, limit: int, offset: int, connection_id: str) -> List[Dict]:
        try:
            documents = []
            sql = BigQueryService.__get_sql_by_day(
                table=table,
                day=day,
                platform=platform,
                limit=limit,
                offset=offset,
                connection_id=connection_id
            )
            LoggerService.logger.info(f'Start extract data for {table}, using sql: {sql}')
            big_query_hook = BigQueryHook(gcp_conn_id=connection_id, use_legacy_sql=False)
            client = big_query_hook.get_client(project_id=dag_config.FIREBASE_PROJECT_ID)
            try:
                result = client.query(sql).result()
            except Exception as ge:
                LoggerService.logger.warning(ge)
                return documents
            for row in result:
                documents.append(BigQueryService.__row_process(table=table, day=day, row=row))
            return documents
        except Exception as e:
            LoggerService.logger.error(f'Cannot extract data for {table} from {offset} limit {limit}, '
                                       f'see detailed information: {str(e)}')
            raise

    @staticmethod
    def __row_process(table: str, day: date, row: List) -> Dict:
        document = {}
        if table == dag_config.FIREBASE_CRASHLYTICS_TABLE:
            document = BigQueryService.__row_crashlytics_process(row=row)
        elif table == dag_config.FIREBASE_ANALYTICS_TABLE:
            document = BigQueryService.__row_analytics_process(table=table, day=day, row=row)
        elif table == dag_config.FIREBASE_MESSAGING:
            document = BigQueryService.__row_messaging_process(row=row)
        return document

    @staticmethod
    def __get_env_from_field_data(data: str):
        env = None
        for item in json.loads(data):
            if item['key'] == 'env':
                string_value = item['value']['string_value']
                if len(string_value) > 0:
                    if string_value == EnvironmentService.DEVELOP:
                        env = 1
                    elif string_value == EnvironmentService.PRODUCTION:
                        env = 2
                break
        return env

    @staticmethod
    def __row_messaging_process(row: List):
        return {
            '_insert_datetime': get_current_datetime(),
            'event_timestamp': row[0],
            'project_number': row[1],
            'message_id': row[2],
            'instance_id': row[3],
            'message_type': row[4],
            'sdk_platform': row[5],
            'collapse_key': row[6],
            'priority': row[7],
            'ttl': row[8],
            'bulk_id': row[9],
            'event': row[10],
            'analytics_label': row[11]
        }

    @staticmethod
    def __row_analytics_process(table: str, day: date, row: List) -> Dict:
        event_params = row[3]
        document = {
            '_insert_datetime': get_current_datetime(),
            '_source_name': f'{table}.events_{date_time_to_str(dt=day, fmt="%Y%m%d")}',
            'event_date': row[0],
            'event_timestamp': row[1],
            'event_name': row[2],
            'event_params': event_params,
            'event_previous_timestamp': row[4],
            'event_value_in_usd': row[5],
            'event_bundle_sequence_id': row[6],
            'user_pseudo_id': row[8],
            'user_properties': row[9],
            'user_first_touch_timestamp': row[10],
            'user_ltv': row[11],
            'device': row[12],
            'geo': row[13],
            'app_info': row[14],
            'traffic_source': row[15],
            'platform': row[16],
            'event_dimensions': row[17],
            'ecommerce': row[18],
            'items': row[19],
            'environment': BigQueryService.__get_env_from_field_data(event_params)
        }

        try:
            document['user_id'] = str(row[7])
        except Exception as e:
            document['user_id'] = None

        return document

    @staticmethod
    def __row_crashlytics_process(row: List) -> Dict:
        document = {
            '_insert_datetime': get_current_datetime(),
            'platform': row[0],
            'event_id': row[1],
            'issue_id': row[2],
            'issue_title': row[3],
            'issue_subtitle': row[4],
            'event_timestamp': row[5],
            'received_timestamp': row[6],
            'device': row[7],
            'memory': row[8],
            'storage': row[9],
            'operating_system': row[10],
            'application': row[11],
            'user': row[12],
            'installation_uuid': row[13],
            'crashlytics_sdk_version': row[14],
            'app_orientation': row[15],
            'device_orientation': row[16],
            'process_state': row[17],
            'logs': row[18],
            'breadcrumbs': row[19],
            'blame_frames': row[20],
            'exceptions': row[21],
            'errors': row[22],
            'threads': row[23]
        }

        return document

    @staticmethod
    def __get_sql_by_day(table: str, day: date, platform: str, limit: int, offset: int, connection_id: str):
        result = None
        date_start = day
        date_end = day + timedelta(days=1)
        big_query_table_name = BigQueryService.__get_big_query_table(
            table=table,
            day=day,
            platform=platform,
            connection_id=connection_id
        )
        for row in dag_config.GOOGLE_BIG_QUERY_CONF:
            row_big_query_table = row['bigquery_table']
            query = row['query']
            if row_big_query_table == table:
                if table == dag_config.FIREBASE_CRASHLYTICS_TABLE or table == dag_config.FIREBASE_MESSAGING:
                    result = f"{query} FROM `{big_query_table_name}` WHERE event_timestamp " \
                             f">= PARSE_TIMESTAMP('%Y-%m-%d %H:%M:%S', '{date_time_to_str(date_start)}') " \
                             f"AND event_timestamp " \
                             f"< PARSE_TIMESTAMP('%Y-%m-%d %H:%M:%S', '{date_time_to_str(date_end)}') " \
                             f"LIMIT {limit} OFFSET {offset}"
                elif table == dag_config.FIREBASE_ANALYTICS_TABLE:
                    result = f"{query} FROM `{big_query_table_name}` LIMIT {limit} OFFSET {offset}"
        return result

    @staticmethod
    def __get_big_query_table(table: str, day: date, platform: str, connection_id: str) -> str:
        try:
            if table == dag_config.FIREBASE_CRASHLYTICS_TABLE:
                big_query_table_name = f'{dag_config.FIREBASE_CRASHLYTICS_TABLE}.' \
                                       f'{BigQueryService.FIREBASE_CRASHLYTICS_SUB_TABLE_PREFIX}{str.upper(platform)}'
                LoggerService.logger.info(f'Source table in Google BigQuery: {big_query_table_name}')
                return big_query_table_name
            elif table == dag_config.FIREBASE_MESSAGING:
                big_query_table_name = f'{dag_config.FIREBASE_MESSAGING}.{BigQueryService.FIREBASE_MESSAGING_SUB_TABLE}'
                LoggerService.logger.info(f'Source table in Google BigQuery: {big_query_table_name}')
                return big_query_table_name
            elif table == dag_config.FIREBASE_ANALYTICS_TABLE:
                day_str = day.strftime('%Y%m%d')
                big_query_table_name = f'{BigQueryService.FIREBASE_ANALYTICS_BASE_TABLE}' \
                                       f'.events_intraday_{day_str}'
                big_query_second_table_name = f'{BigQueryService.FIREBASE_ANALYTICS_BASE_TABLE}' \
                                              f'.events_{day_str}'
                big_query_hook = BigQueryHook(gcp_conn_id=connection_id, use_legacy_sql=False)
                client = big_query_hook.get_client(project_id=dag_config.FIREBASE_PROJECT_ID)
                try:
                    client.query(f'select count(1) from {big_query_table_name}').result()
                    LoggerService.logger.info(f'Source table in Google BigQuery: {big_query_table_name}')
                    return big_query_table_name
                except Exception as e:
                    try:
                        client.query(f'select count(1) from {big_query_second_table_name}').result()
                        LoggerService.logger.info(f'Source table in Google BigQuery: {big_query_second_table_name}')
                        return big_query_second_table_name
                    except Exception as es:
                        raise
        except Exception as e:
            raise
