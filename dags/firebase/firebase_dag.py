import os
import csv
from typing import List, Dict
from datetime import date, datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.exceptions import AirflowSkipException
from airflow.hooks.base import BaseHook
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.file_path_service import FilePathService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.helpers.date_helper import get_max_date, str_to_date_time, date_time_to_str
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.firebase.google_big_query_service import BigQueryService
import dags.firebase.config.default as dag_config
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_FIREBASE_EXTRACT_STATE_FILE_PATH: [config.get_connection(ConfigService.CONN_GOOGLE_BIG_QUERY)]
    }
)


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    for table in [
        dag_config.FIREBASE_CRASHLYTICS_TABLE,
        dag_config.FIREBASE_ANALYTICS_TABLE,
        dag_config.FIREBASE_MESSAGING
    ]:
        extracted_table = f'{table}_extracted'
        ch.truncate(extracted_table)
        LoggerService.logger.info(f'Truncated table "{extracted_table}"')

    for path in file_paths.get_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def get_start_date(big_query_table: str) -> datetime:
    try:
        LoggerService.logger.info('Get start date from ClickHouse database')
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        extract_state = ExtractStateService(ch)
        min_start_date = dag_config.MIN_START_DATE
        last_start_date = extract_state.get_last_value(
            config.get_connection(ConfigService.CONN_GOOGLE_BIG_QUERY),
            big_query_table,
            'start_date',
            ExtractStateService.DATE_TYPE
        )
        last_start_date += timedelta(seconds=1)
        date_format = '%Y-%m-%d %H:%M:%S'
        start_date = get_max_date(
            last_start_date.strftime(date_format),
            min_start_date,
            date_format
        )
        LoggerService.logger.info({'StartDate': start_date})
        return str_to_date_time(start_date, '%Y-%m-%d %H:%M:%S')
    except Exception as e:
        LoggerService.logger.error(f'Error get start date from ClickHouse database, see detailed information: {str(e)}')
        raise


def get_end_date() -> datetime:
    return datetime.combine(datetime.now().date() - timedelta(days=2), datetime.max.time())


def load_to_clickhouse(table: str, data: List[Dict]):
    LoggerService.logger.info(f'Start load data to ClickHouse table "{table}"')
    try:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.insert(f'INSERT INTO {table} VALUES', data)
        LoggerService.logger.info(f'Loaded {len(data)} rows to ClickHouse table "{table}"')
    except Exception as e:
        LoggerService.logger.error(f'Cannot load data to ClickHouse table "{table}"')
        raise


def load_data_day(ch_table: str, big_query_table: str, platform: str, day: date):
    LoggerService.logger.info(f'Start load data from BigQuery for day {day.strftime("%Y-%m-%d")} to {ch_table}')
    clickhouse_documents = []
    big_query_documents = []
    total_count_rows = 0
    count_rows = 0
    try:
        big_query_offset = 0
        LoggerService.logger.info(f'Table name with data from BigQuery: {big_query_table}')
        LoggerService.logger.info(f'Table name with data from BigQuery which save in ClickHouse: {ch_table}')
        big_query_limit = config.get_setting(dag_config.LIMIT_ROWS_BIG_QUERY_KEY)
        ch_batch_size = config.get_setting(ConfigService.BATCH_SIZE)
        while True:
            big_query_documents = BigQueryService.extract(
                table=big_query_table,
                day=day,
                platform=platform,
                limit=big_query_limit,
                offset=big_query_offset,
                connection_id=config.get_connection(ConfigService.CONN_GOOGLE_BIG_QUERY)
            )
            big_query_offset += big_query_limit
            if len(big_query_documents) != big_query_limit:
                LoggerService.logger.info(f'Read {len(big_query_documents)} rows, less than {big_query_limit}')
                break
            for doc in big_query_documents:
                clickhouse_documents.append(doc)
                count_rows += 1
                if count_rows == ch_batch_size:
                    load_to_clickhouse(
                        table=ch_table,
                        data=clickhouse_documents
                    )
                    total_count_rows += count_rows
                    count_rows = 0
                    clickhouse_documents.clear()
        for doc in big_query_documents:
            clickhouse_documents.append(doc)
            count_rows += 1
        big_query_documents.clear()
        if len(clickhouse_documents) > 0:
            load_to_clickhouse(
                table=ch_table,
                data=clickhouse_documents
            )
        total_count_rows += len(clickhouse_documents)
        LoggerService.logger.info(f'Load {total_count_rows} rows to ClickHouse table "{ch_table}" successful')
        clickhouse_documents.clear()
    except Exception as e:
        LoggerService.logger.error(f'Cannot loading data from BigQuery to {ch_table}, '
                                   f'see detailed information: {str(e)}')
        total_count_rows = 0
        raise
    finally:
        LoggerService.logger.info(f'Finish loading data from BigQuery to {ch_table}, '
                                  f'successful processed {total_count_rows} rows')
        clickhouse_documents.clear()
        big_query_documents.clear()


def extract_data(ch_table: str, big_query_table: str, platform: str):
    google_big_query_connection_id = config.get_connection(ConfigService.CONN_GOOGLE_BIG_QUERY)
    try:
        big_query_table_extract_source = big_query_table if platform is None else '_'.join([big_query_table, platform])
        start_date = get_start_date(big_query_table_extract_source)
        end_date = get_end_date()
        LoggerService.logger.info(f'Start load data from {start_date.strftime("%Y-%m-%d %H:%M:%S")} '
                                  f'to {end_date.strftime("%Y-%m-%d %H:%M:%S")}')
        if start_date >= end_date:
            raise AirflowSkipException

        path_state = file_paths.get_path(google_big_query_connection_id,
                                         dag_config.TMP_FIREBASE_EXTRACT_STATE_FILE_PATH)
        path_state_header = file_paths.get_header_path(dag_config.TMP_FIREBASE_EXTRACT_STATE_FILE_PATH)
        while True:
            load_data_day(
                ch_table=ch_table,
                big_query_table=big_query_table,
                platform=platform,
                day=start_date.date()
            )
            start_date += timedelta(days=1)
            if start_date > end_date:
                break

        state = {
            'connection_id': google_big_query_connection_id,
            'source_name': big_query_table_extract_source,
            'key_name': 'start_date',
            'last_value_date': date_time_to_str(end_date)
        }

        if not os.path.exists(path_state_header):
            with open(path_state_header, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, state.keys(), delimiter=';')
                writer.writeheader()
        with open(path_state, 'a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, state.keys(), delimiter=';')
            writer.writerow(state)

    except Exception as e:
        LoggerService.logger.error(f'Error load data, see detailed information: {str(e)}')
        raise


def move_data(table: str):
    try:
        from_table = f'{table}_extracted'
        to_table = table
        LoggerService.start_move_data(from_table, to_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {to_table} SELECT * FROM {conn_etl.schema}.{from_table}')
    except Exception as e:
        LoggerService.error_move_data(from_table, to_table, str(e))
        raise


def __get_task_name(base_name: str, platform: str) -> str:
    return base_name if platform is None else '_'.join([base_name, platform])


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    extract_data_tasks = [
        PythonOperator(
            task_id=__get_task_name(f'extract_{cfg["bigquery_table"]}', cfg['platform']),
            python_callable=extract_data,
            op_kwargs={
                'ch_table': f'{cfg["bigquery_table"]}_extracted',
                'big_query_table': cfg['bigquery_table'],
                'platform': cfg['platform']
            }
        ) for cfg in dag_config.GOOGLE_BIG_QUERY_CONF
    ]

    finish_extract_data_task = EmptyOperator(
        task_id='finish_extract_data_task'
    )

    move_data_task = [
        PythonOperator(
            task_id=__get_task_name(f'move_data_{cfg["bigquery_table"]}', cfg['platform']),
            python_callable=move_data,
            op_kwargs={
                'table': cfg['bigquery_table']
            }
        ) for cfg in dag_config.GOOGLE_BIG_QUERY_CONF
    ]

    finish_move_data_task = EmptyOperator(
        task_id='finish_move_data_task'
    )

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_FIREBASE_EXTRACT_STATE_FILE_PATH,
        connection_ids=[config.get_connection(ConfigService.CONN_GOOGLE_BIG_QUERY)]
    )

    load_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_FIREBASE_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='load_extract_state'
    )

    clear_temp_data_task >> extract_data_tasks >> finish_extract_data_task >> move_data_task >> finish_move_data_task \
        >> concat_extract_state_files_task >> load_extract_state_task

if __name__ == '__main__':
    dag.cli()
