from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load Firebase data',
}

MIN_START_DATE = '2023-10-28 00:00:00'

TMP_FIREBASE_EXTRACT_STATE_FILE_PATH = 'firebase_es_[connection].csv'
FIREBASE_CRASHLYTICS_TABLE = 'firebase_crashlytics'
FIREBASE_ANALYTICS_TABLE = 'firebase_analytics'
FIREBASE_MESSAGING = 'firebase_messaging'

FIREBASE_PROJECT_ID = 'magic-seasons-2024'
ANDROID_PLATFORM = 'android'
IOS_PLATFORM = 'ios'

LIMIT_ROWS_BIG_QUERY_KEY = 'limit_rows_big_query'

SETTINGS = {
    'limit_rows_big_query': 100000
}

GOOGLE_BIG_QUERY_CONF = [
    {
        'bigquery_table': FIREBASE_CRASHLYTICS_TABLE,
        'platform': ANDROID_PLATFORM,
        'query': 'SELECT platform, event_id, issue_id, issue_title, issue_subtitle, event_timestamp, '
                 'received_timestamp, TO_JSON_STRING(device) AS device, TO_JSON_STRING(memory) AS memory, '
                 'TO_JSON_STRING(storage) AS storage, TO_JSON_STRING(operating_system) AS operating_system, '
                 'TO_JSON_STRING(application) AS application, TO_JSON_STRING(user) AS user, '
                 'installation_uuid, crashlytics_sdk_version, app_orientation, device_orientation, process_state, '
                 'TO_JSON_STRING(logs) AS logs, TO_JSON_STRING(breadcrumbs) AS breadcrumbs, '
                 'TO_JSON_STRING(blame_frame) AS blame_frames, TO_JSON_STRING(exceptions) AS exceptions, '
                 'TO_JSON_STRING(errors) AS errors, TO_JSON_STRING(threads) AS threads'
    },
    {
        'bigquery_table': FIREBASE_CRASHLYTICS_TABLE,
        'platform': IOS_PLATFORM,
        'query': 'SELECT platform, event_id, issue_id, issue_title, issue_subtitle, event_timestamp, '
                 'received_timestamp, TO_JSON_STRING(device) AS device, TO_JSON_STRING(memory) AS memory, '
                 'TO_JSON_STRING(storage) AS storage, TO_JSON_STRING(operating_system) AS operating_system, '
                 'TO_JSON_STRING(application) AS application, TO_JSON_STRING(user) AS user, '
                 'installation_uuid, crashlytics_sdk_version, app_orientation, device_orientation, process_state, '
                 'TO_JSON_STRING(logs) AS logs, TO_JSON_STRING(breadcrumbs) AS breadcrumbs, '
                 'TO_JSON_STRING(blame_frame) AS blame_frames, TO_JSON_STRING(exceptions) AS exceptions, '
                 'TO_JSON_STRING(errors) AS errors, TO_JSON_STRING(threads) AS threads'
    },
    {
        'bigquery_table': FIREBASE_ANALYTICS_TABLE,
        'platform': None,
        'query': 'SELECT event_date, event_timestamp, event_name, TO_JSON_STRING(event_params) AS event_params, '
                 'event_previous_timestamp, event_value_in_usd, event_bundle_sequence_id, user_id, user_pseudo_id, '
                 'TO_JSON_STRING(user_properties) AS user_properties, user_first_touch_timestamp, '
                 'TO_JSON_STRING(user_ltv) AS user_ltv, TO_JSON_STRING(device) AS device, TO_JSON_STRING(geo) AS geo, '
                 'TO_JSON_STRING(app_info) AS app_info, TO_JSON_STRING(traffic_source) AS traffic_source, platform, '
                 'TO_JSON_STRING(event_dimensions) AS event_dimensions, TO_JSON_STRING(ecommerce) AS ecommerce, '
                 'TO_JSON_STRING(items) AS items'
    },
    {
        'bigquery_table': FIREBASE_MESSAGING,
        'platform': None,
        'query': 'SELECT event_timestamp, project_number, message_id, instance_id, message_type, sdk_platform, '
                 'collapse_key, priority, ttl, bulk_id, event, analytics_label'
    }
]


