from typing import Dict
import dags.clans_matchmaking.config.default as dag_config
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data


class ClansMatchmakingRowTransformer:
    @staticmethod
    def process(row: Dict, connection_config: Dict) -> Dict:
        row_transform = {
            '_connection_id': connection_config['id'],
            'platform_type': connection_config['platform'],
            'event_id': row['event_id']
        }
        for mongo_field, ch_config in dag_config.MONGO_CLANS_COLLECTION_FIELDS.items():
            mongo_filed_parts = mongo_field.split('.')
            field_val = row[mongo_filed_parts.pop(0)] if mongo_filed_parts[0] in row else None
            if field_val is not None:
                for field_part in mongo_filed_parts:
                    if field_part not in field_val:
                        field_val = None
                        break
                    field_val = field_val[field_part]
            row_transform[ch_config['ch_field']] = field_val
        return prepare_parquet_data(row_transform)
