from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load clans matchmaking information',
}

SETTINGS = {}

# ID союзного ивента
EVENT_MINE_ID = 1
EVENT_REGATTA_ID = 4

CH_CLANS_MATCHMAKING_EXTRACTED_TABLE = 'clans_matchmaking_extracted'
CH_CLANS_MATCHMAKING_TABLE = 'clans_matchmaking'

MONGO_CLANS_COLLECTION = 'clans'

TMP_CLANS_MATCHMAKING_COLLECTION_FILE_PATH = 'clans_matchmaking_collection_[connection].parquet'
TMP_CLANS_MATCHMAKING_EXTRACT_STATE_FILE_PATH = 'clans_matchmaking_es_[connection].csv'

MONGO_CLANS_COLLECTION_FIELDS = {
    '_id': {'ch_field': 'clan_id'},
    'name': {'ch_field': 'name'},
    'usersCount': {'ch_field': 'member_count'},

    'events.mine.initTime': {'ch_field': 'date'},
    'events.mine.rating.clans': {'ch_field': 'clans'},
    'events.mine.seasonId': {'ch_field': 'season_id'},

    'events.regatta.initTime': {'ch_field': 'date'},
    'events.regatta.rating.clans': {'ch_field': 'clans'},
    'events.regatta.seasonId': {'ch_field': 'season_id'},
}
