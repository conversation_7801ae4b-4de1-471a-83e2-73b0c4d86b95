from typing import List, Dict
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data


class GladesPopularityRowTransformer:
    @staticmethod
    def process(
            row: Dict,
            connection_config: Dict,
            table_name: str,
            tables_template: str,
            max_period: int
    ) -> List[Dict]:
        users_data = []
        for i in range(1, 3):
            users_data.append(
                prepare_parquet_data(
                    {
                        '_connection_id': connection_config['id'],
                        '_source_name': table_name,
                        'user_id': row[f'user_id_{i}'],
                        'platform_type': connection_config['platform'],
                        'screen': GladesPopularityRowTransformer.get_screen_id_by_table_name(tables_template,
                                                                                             table_name),
                        'period_id': max_period,
                        'stages_sum': row[f'stages_sum_{i}'],
                        'toys_sum': row[f'toys_sum_{i}'],
                        'votes': row[f'votes_{i}'],
                        'shows': row['shows']
                    }
                )
            )

        return users_data

    @staticmethod
    def get_screen_id_by_table_name(tables_template: str, table_name: str) -> int:
        v = table_name[len(tables_template):]
        return int(v[:v.find('_')])

    @staticmethod
    def get_period_id_by_table_name(table_name: str) -> int:
        return int(str.replace(table_name[table_name.find('period_'):], 'period_', ''))
