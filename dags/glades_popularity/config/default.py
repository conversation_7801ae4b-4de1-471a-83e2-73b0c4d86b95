from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load glades popularity information',
}

SETTINGS = {}

CH_GLADES_POPULARITY_TABLE = 'glades_popularity'
CH_GLADES_POPULARITY_EXTRACTED_TABLE = 'glades_popularity_extracted'

TMP_GLADES_POPULARITY_FILE_PATH = 'glades_popularity_[connection].parquet'
TMP_GLADES_POPULARITY_EXTRACT_STATE_FILE_PATH = 'glades_popularity_es_[connection].csv'

MYSQL_TABLES_TEMPLATE = 'glade_duel_popularity_screen_'
REDIS_REFRESH_KEY_PREFIX = 'miniG:cg:lastUpd:'
LAST_PERIODS_COUNT = 5

