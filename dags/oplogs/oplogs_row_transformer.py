from typing import Dict
from datetime import datetime
from dags.lib.services.platform_service import PlatformService
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value
import pytz


class OpLogsRowTransformer:

    def __init__(self, timezone_name: str):
        # Чтобы не делать этого при каждом вызове process()
        self.timezone = pytz.timezone(timezone_name)

    def convert_time_to_utc(self, date_value: datetime) -> datetime:
        dt_localized = self.timezone.localize(date_value)
        dt_utc = dt_localized.astimezone(pytz.utc)
        return dt_utc

    def process(self, row: Dict, connection_id: str, op_log_type: str, source_name: str) -> dict:
        row['_connection_id'] = connection_id
        row['_source_name'] = source_name
        row['platform_type'] = PlatformService.get_name(row['platform_type'])
        row['oplog_type'] = op_log_type
        row['date'] = self.convert_time_to_utc(row['date'])

        return prepare_parquet_data(row)
