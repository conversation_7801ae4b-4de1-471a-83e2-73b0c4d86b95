from datetime import timedelta
from dags.lib.services.platform_service import PlatformService

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load oplogs information',
}

SETTINGS = {
    'timezones': {
        PlatformService.PLATFORM_MOBILE: 'Europe/Moscow',
        PlatformService.PLATFORM_VK: 'Europe/Moscow',
        PlatformService.PLATFORM_OK: 'Europe/Moscow',
    }
}

SETTING_TIMEZONES = 'timezones'

CH_OP_LOGS_EXTRACTED_TABLE = 'mysql_oplogs_extracted'
CH_OP_LOGS_TABLE = 'oplogs'
OP_LOGS_FILE_PREFIX = 'oplogs'

# число проверяемых дневных таблиц
COUNT_CHECKING_DAYS = 5
# число проверяемых месячных таблиц
COUNT_CHECKING_MONTHS = 3

DEFAULT_MAX_ROWS_FOR_PROCESSING_COUNT = 400000
MAX_ROWS_FOR_PROCESSING_COUNT_KEY = 'max_rows_for_processing'

TMP_OPLOGS_EXTRACT_STATE_FILE_PATH = 'oplogs_es_[connection].csv'

OP_LOGS_TYPES = [
    {'type': 'cash', 'period': 'month'},
    {'type': 'energy', 'period': 'day', MAX_ROWS_FOR_PROCESSING_COUNT_KEY: 700000},
    {'type': 'resource', 'period': 'day', MAX_ROWS_FOR_PROCESSING_COUNT_KEY: 2000000},
    {'type': 'coin', 'period': 'day'},
    {'type': 'money1', 'period': 'day'},
    {'type': 'money2', 'period': 'day'},
    {'type': 'money3', 'period': 'day'},
    {'type': 'money4', 'period': 'day'},
    {'type': 'money5', 'period': 'day'},
    {'type': 'progress', 'period': 'day'},
    {'type': 'roulette', 'period': 'month'},
    {'type': 'mahjong', 'period': 'day'},
    {'type': 'toy', 'period': 'day'},
]
