import os
import csv
from typing import Dict
from datetime import date, timedelta
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.hooks.base import BaseHook
from dags.lib.services.clickhouse_service import ClickhouseService, PARQUET_DATA_TYPE, CSV_DATA_TYPE
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.mysql_service import MySQLService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.oplogs.oplogs_row_transformer import OpLogsRowTransformer
from dags.lib.helpers.date_helper import get_current_date, get_year_str, get_month_str, get_day_str, add_date_months
from dags.lib.helpers.directory_helper import directory_scan_cursor
from dags.lib.services.extract_state_service import ExtractStateService
from dags.config.default import CH_EXTRACT_STATE_TABLE
import dags.oplogs.config.default as dag_config
from dags.lib.services.file_path_service import FilePathService
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
start_run_date = get_current_date()

file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_OPLOGS_EXTRACT_STATE_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_OP_LOGS)
    }
)


def truncate_op_logs_extracted_table():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_OP_LOGS_EXTRACTED_TABLE)


def get_result_directory() -> str:
    return config.get_setting(ConfigService.TMP_FILES_FOLDER)


def get_result_filename(platform: str, table_name: str, part: int) -> str:
    return f'{get_result_directory()}/' \
           f'{dag_config.OP_LOGS_FILE_PREFIX}_{platform}_{table_name}_{part}.{PARQUET_DATA_TYPE}'


def get_oplog_table_by_date_type(op_log: Dict, dt: date) -> str:
    name = f'oplog_{op_log["type"]}'
    if op_log['period'] == 'month':
        name = f'{name}_month_{get_year_str(dt)}_{get_month_str(dt)}'
    else:
        name = f'{name}_day_{get_year_str(dt)}_{get_month_str(dt)}_{get_day_str(dt)}'

    return name


def get_op_log_limit(op_log: Dict) -> int:
    return op_log[dag_config.MAX_ROWS_FOR_PROCESSING_COUNT_KEY] \
        if dag_config.MAX_ROWS_FOR_PROCESSING_COUNT_KEY in op_log \
        else dag_config.DEFAULT_MAX_ROWS_FOR_PROCESSING_COUNT


def stream_op_logs_by_table(mysql_connection_config: Dict, table_name: str, limit: int):
    mysql_connection_id = mysql_connection_config['id']

    ms = MySQLService(mysql_connection_id)
    if ms.table_exist(table_name) == 0:
        return {}

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    extract_state = ExtractStateService(ch)
    max_op_log_id = extract_state.get_last_value(mysql_connection_id, f'{table_name}', 'id')

    if max_op_log_id < 0:
        # Для миграции со старой версии на новую, позже этот блок кода можно будет удалить
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        max_op_log_id = ch.get_max_column_value(
            dag_config.CH_OP_LOGS_TABLE,
            '_id',
            {
                '_connection_id': mysql_connection_id,
                '_source_name': table_name
            }
        )

    LoggerService.logger.info(f'Extract {mysql_connection_id}, {table_name} from id: {max_op_log_id}')

    sql = f'''
        SELECT
            id as _id,
            platform as platform_type,
            user_id,
            clan_id,
            session_id,
            session_number,
            created_at as date,
            app_type,
            screen,
            level,
            progress,
            cash_current as cash,
            energy_current as energy,
            item_id,
            action,
            extra,
            value as amount_change,
            current as amount_current,
            campaign_level as passing_mode_level,
            tokens,
            no_ad as no_ads,
            progress_level
        FROM {table_name}
        WHERE id > {max_op_log_id}
        LIMIT {limit}
    '''
    for row in ms.stream(sql):
        yield row


def extract_op_logs_by_day(mysql_connection_config: Dict, start_query_date: date, op_log: Dict):
    mysql_table_name = get_oplog_table_by_date_type(op_log=op_log, dt=start_query_date)
    op_log_type = op_log['type']
    mysql_platform = mysql_connection_config['platform']
    LoggerService.start_extracting(
        mysql_platform,
        mysql_connection_config['id'],
        mysql_table_name
    )

    counter = 0
    max_value = 0
    part_number = 0
    part_counter = 0
    op_log_data = []

    def write_data(data: list, platform: str, table_name: str, part: int):
        if len(data) > 0:
            data_frame = pd.DataFrame(data)
            table = pa.Table.from_pandas(data_frame)
            file_path = get_result_filename(platform, table_name, part)
            pq.write_table(table, file_path)

    timezones = config.get_setting(dag_config.SETTING_TIMEZONES)
    transformer = OpLogsRowTransformer(timezones[mysql_platform])

    for row in stream_op_logs_by_table(
            mysql_connection_config=mysql_connection_config,
            table_name=mysql_table_name,
            limit=get_op_log_limit(op_log)
    ):
        counter += 1
        part_counter += 1
        op_log_data.append(transformer.process(
            row,
            mysql_connection_config['id'],
            op_log_type,
            mysql_table_name
        ))
        max_value = max(max_value, row['_id'])
        if part_counter >= config.get_setting(ConfigService.BATCH_SIZE):
            write_data(op_log_data, mysql_platform, mysql_table_name, part_number)
            part_number += 1
            part_counter = 0
            op_log_data.clear()

    write_data(op_log_data, mysql_platform, mysql_table_name, part_number)

    if max_value > 0:
        state = {
            'connection_id': mysql_connection_config['id'],
            'source_name': f'{mysql_table_name}',
            'key_name': 'id',
            'last_value_int': max_value
        }
        path_state = file_paths.get_path(mysql_connection_config['id'], dag_config.TMP_OPLOGS_EXTRACT_STATE_FILE_PATH)
        path_state_header = file_paths.get_header_path(dag_config.TMP_OPLOGS_EXTRACT_STATE_FILE_PATH)
        if not os.path.exists(path_state_header):
            with open(path_state_header, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, state.keys(), delimiter=';')
                writer.writeheader()
        # Используем тот факт, что в рамках одного коннекта таски выполняются последовательно, поэтому можно писать в один
        # и тот же файл, не опасаясь race conditions
        with open(path_state, 'a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, state.keys(), delimiter=';')
            writer.writerow(state)

    LoggerService.finish_extracting(counter)


def extract_op_logs(mysql_connection_config: Dict, op_log: Dict):
    try:
        if op_log['period'] == 'day':
            load_data_interval = dag_config.COUNT_CHECKING_DAYS

            def calc_start_date(delta: int) -> date:
                return start_run_date - timedelta(delta)
        else:
            load_data_interval = dag_config.COUNT_CHECKING_MONTHS

            def calc_start_date(delta: int) -> date:
                return add_date_months(start_run_date, delta * (-1))
        for i in range(load_data_interval):
            extract_op_logs_by_day(
                mysql_connection_config=mysql_connection_config,
                start_query_date=calc_start_date(i),
                op_log=op_log
            )
    except Exception as e:
        raise


def load_op_logs():
    for file in directory_scan_cursor(
            path=get_result_directory(),
            pattern=f'{dag_config.OP_LOGS_FILE_PREFIX}_*'
    ):
        file_name = file.split('/').pop()
        file_name = file_name.split('.')[0]
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file,
            table_name=dag_config.CH_OP_LOGS_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'load_{file_name}'
        ).execute(dict())


def move_op_logs():
    try:
        from_table = dag_config.CH_OP_LOGS_EXTRACTED_TABLE
        to_table = dag_config.CH_OP_LOGS_TABLE
        LoggerService.start_move_data(from_table, to_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {to_table} SELECT * FROM {conn_etl.schema}.{from_table}')
    except Exception as e:
        LoggerService.error_move_data(from_table, to_table, str(e))
        raise


def clear_temp_data():
    for file_path in directory_scan_cursor(
            path=get_result_directory(),
            pattern=f'{dag_config.OP_LOGS_FILE_PREFIX}_*'
    ):
        LoggerService.logger.info(f'Removing: {file_path}')
        os.remove(file_path)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    truncate_op_logs_extracted_table_task = PythonOperator(
        task_id='truncate_op_logs_extracted_table',
        python_callable=truncate_op_logs_extracted_table
    )

    clear_tmp_data_task = PythonOperator(
        task_id='clear_tmp_data',
        python_callable=clear_temp_data
    )

    extract_op_logs_tasks = {}
    for connect in config.get_connections(ConfigService.CONN_MYSQL_OP_LOGS):
        extract_op_logs_tasks[connect['id']] = []
        for op_log_settings in dag_config.OP_LOGS_TYPES:
            extract_op_logs_tasks[connect['id']].append(
                PythonOperator(
                    task_id=f'extract_data_{connect["platform"]}_{op_log_settings["type"]}',
                    python_callable=extract_op_logs,
                    op_kwargs={
                        'mysql_connection_config': connect,
                        'op_log': op_log_settings
                    },
                )
            )

    load_op_logs_task = PythonOperator(
        task_id='load_op_logs',
        python_callable=load_op_logs
    )

    move_op_logs_extracted_table_task = PythonOperator(
        task_id='move_op_logs',
        python_callable=move_op_logs
    )

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_OPLOGS_EXTRACT_STATE_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MYSQL_OP_LOGS)
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_OPLOGS_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_extract_state'
    )

    truncate_op_logs_extracted_table_task >> clear_tmp_data_task

    for connection_id, tasks in extract_op_logs_tasks.items():
        extract_task = tasks.pop(0)
        clear_tmp_data_task >> extract_task
        for task in tasks:
            extract_task >> task
            extract_task = task
        extract_task >> load_op_logs_task

    load_op_logs_task >> move_op_logs_extracted_table_task >> concat_extract_state_files_task >> import_extract_state_task

if __name__ == '__main__':
    dag.cli()
