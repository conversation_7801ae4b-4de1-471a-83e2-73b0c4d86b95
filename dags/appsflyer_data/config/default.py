from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load appsflyer raw data',
}

SETTINGS = {
    'apps_flyer_app_id_android': 'com.progameslab.magic.seasons2024.farm.match.collect',
    'apps_flyer_app_id_ios': 'id6463635391',
    'apps_flyer_token_v2': 'eyJhbGciOiJBMjU2S1ciLCJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0.RcSFllyFKWRekUXl0D-mHZcakeThR1cGme-UEqU6YE_pT4LjF4NFwA.80gIZPJQKjeQbq1q.TzSE_TSCeLqvzsLJA4R3B8mWG442aFy_U610d4b0-9DkTmNpY0O0-gOfWkWfHrIiiOdbvglPYn0BNfU9BJacsIrgzhwvdUOVVAGl7OJeXQdIDEw9Beciz7FKmEl1tm4D8GqTLpF1mEN-W7Xg7MvEmGUm2mOHB-upR5oH5gVyfeRq_22W-JwZ2bpxSvLzp0mDDTs5drwRz69fFLV6vKkg_qlmjZBjwDCI22NbNtPiFq6eVs5UnaS7U707g31z1ZU3MnucPr8d63696NS4zLwPONLoMuRSSU8qdCOSZfHtqMvgqZDLuPG74P_3XeuZL_aur0PnuFRnCp7ZysZAO4zGfWoJBUaCaEKeh8-lbIg2rWf-0OLEdKy71uCptriQ0eGGePTmaKdnj2Tx0Fp4BVa0LAOebJsjtju3k0P0IOvRpsbvpnbDFM49QKF5ze7eMd7dFGTimVz0EPkR835HmU0vTDnDfgoImyiD5tuKAJrP7wOYQ2c6Ltks1h9FUlShqSmIqolXLrh-JEpSPJlD5tCt.pk7r26gVen8wp-eVm7eK9A',
    'apps_flyer_api': 'https://hq1.appsflyer.com/api/[path]/v2/data/app',
    'apps_flyer_export_url': 'https://hq1.appsflyer.com/api/raw-data/export/app/{}/{}/v5',
    'aws_data_locker_bucket': 'af-datalocker-progameslab',
    'aws_directory_cohort_user_acquisition': 'f0ac-acc-yxAoxWWV-f0ac/progameslab-datalocker/t=cohort_user_acquisition',
}

APPS_FLYER_APP_ID_ANDROID_KEY = 'apps_flyer_app_id_android'
APPS_FLYER_APP_ID_IOS_KEY = 'apps_flyer_app_id_ios'
APPS_FLYER_TOKEN_V1_KEY = 'apps_flyer_token_v1'
APPS_FLYER_TOKEN_V2_KEY = 'apps_flyer_token_v2'
APPS_FLYER_API_KEY = 'apps_flyer_api'
APPS_FLYER_EXPORT_URL_KEY = 'apps_flyer_export_url'
AWS_DATA_LOCKER_BUCKET_KEY = 'aws_data_locker_bucket'
AWS_DIRECTORY_COHORT_USER_ACQUISITION_KEY = 'aws_directory_cohort_user_acquisition'
AWS_PAGE_SIZE = 1024

MIN_START_DATE = '2023-10-27'

CH_APPSFLYER_COHORT_USER_ACQUISITION_TABLE = 'appsflyer_cohort_user_acquisition'
CH_APPSFLYER_COHORT_USER_ACQUISITION_EXTRACTED_TABLE = 'appsflyer_cohort_user_acquisition_extracted'

CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_TABLE = 'appsflyer_skan_aggregated_performance_report'
CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_EXTRACTED_TABLE = \
    'appsflyer_skan_aggregated_performance_report_extracted'
TMP_SKAN_AGGREGATED_PERFORMANCE_REPORT_FILE_PATH = 'skan_aggregated_performance_report_[connection].csv'

CH_APPSFLYER_MARKETING_CAMPAIGN_TABLE = 'appsflyer_marketing_campaign'
CH_APPSFLYER_MARKETING_CAMPAIGN_EXTRACTED_TABLE = 'appsflyer_marketing_campaign_extracted'
TMP_MARKETING_CAMPAIGN_FILE_PATH = 'marketing_campaign_[connection].csv'

CH_APPSFLYER_RAW_DATA_TABLE = 'appsflyer_raw_data'

TMP_RAW_DATA_ANDROID_REPORT_FILE_PATH = 'raw_data_android_[connection].csv'
TMP_RAW_DATA_ANDROID_REPORT_RESULT_FILE_PATH = 'raw_data_android_[connection]_result.tsv'

TMP_RAW_DATA_IOS_REPORT_FILE_PATH = 'raw_data_ios_[connection].csv'
TMP_RAW_DATA_IOS_REPORT_RESULT_FILE_PATH = 'raw_data_ios_[connection]_result.tsv'

INSTALL_REPORT_TYPE = 'installs_report'
IN_APP_EVENTS_REPORT_TYPE = 'in_app_events_report'
UNINSTALL_EVENTS_REPORT_TYPE = 'uninstall_events_report'
ORGANIC_INSTALLS_REPORT_TYPE = 'organic_installs_report'
ORGANIC_IN_APP_EVENTS_REPORT_TYPE = 'organic_in_app_events_report'
ORGANIC_UNINSTALL_EVENTS_REPORT_TYPE = 'organic_uninstall_events_report'

ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS = [
    {
        'report_name': 'non_organic_installs',
        'report_type': INSTALL_REPORT_TYPE,
        'table': 'appsflyer_non_organic_installs_report_extracted',
        'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                             'match_type,device_category,keyword_match_type,att,campaign_type,is_lat'
    },
    {
        'report_name': 'non_organic_in_app_events',
        'report_type': IN_APP_EVENTS_REPORT_TYPE,
        'table': 'appsflyer_non_organic_in_app_events_report_extracted',
        'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                             'match_type,device_category,keyword_match_type,att,campaign_type,is_lat'
    },
    {
        'report_name': 'non_organic_uninstalls',
        'report_type': UNINSTALL_EVENTS_REPORT_TYPE,
        'table': 'appsflyer_non_organic_uninstalls_report_extracted',
        'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,keyword_match_type,is_lat'
    },
    {
        'report_name': 'organic_installs',
        'report_type': ORGANIC_INSTALLS_REPORT_TYPE,
        'table': 'appsflyer_organic_installs_report_extracted',
        'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,install_app_store,'
                             'keyword_match_type,att,campaign_type,is_lat'
    },
    {
        'report_name': 'organic_in_app_events',
        'report_type': ORGANIC_IN_APP_EVENTS_REPORT_TYPE,
        'table': 'appsflyer_organic_in_app_events_report_extracted',
        'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,'
                             'keyword_match_type,att,campaign_type'
    },
    {
        'report_name': 'organic_uninstalls',
        'report_type': ORGANIC_UNINSTALL_EVENTS_REPORT_TYPE,
        'table': 'appsflyer_organic_uninstalls_report_extracted',
        'additional_fields': 'device_model,keyword_id,store_reinstall,deeplink_url,oaid,keyword_match_type,is_lat'
    }
]


