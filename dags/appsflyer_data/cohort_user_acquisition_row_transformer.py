import pandas as pd
from datetime import datetime


class CohortUserAcquisitionRowTransformer:
    @staticmethod
    def process(df: pd.DataFrame, app_id: str, file_name: str) -> pd.DataFrame:
        df['event_date'] = df['event_date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d').date())
        df['unique_users'] = df['unique_users'].apply(lambda x: int(x) if x != '' else None)
        df['event_count'] = df['event_count'].apply(lambda x: int(x) if x != '' else None)
        df['revenue_usd'] = df['revenue_usd'].apply(lambda x: float(x) if x != '' else None)
        df['revenue_selected_currency'] = df['revenue_selected_currency'].apply(lambda x: float(x) if x != '' else None)
        df['app_id'] = app_id
        df['filename'] = file_name
        return df
