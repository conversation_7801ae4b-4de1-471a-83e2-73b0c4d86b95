from typing import Dict
from datetime import datetime, timezone
from dateutil import parser
from dags.lib.helpers.convert_value_helper import get_default_value
import json


class ReportRowTransformer:
    EVENT_VALUE_FIELD = 'event_value'
    ATTRIBUTES_MAPPER = [
        {'column': 'ad', 'field': 'Ad', 'type': 'str'},
        {'column': 'ad_id', 'field': 'Ad ID', 'type': 'str'},
        {'column': 'ad_type', 'field': 'Ad Type', 'type': 'str'},
        {'column': 'ad_set', 'field': 'Adset', 'type': 'str'},
        {'column': 'ad_set_id', 'field': 'Adset ID', 'type': 'str'},
        {'column': 'campaign', 'field': 'Campaign', 'type': 'str'},
        {'column': 'campaign_id', 'field': 'Campaign ID', 'type': 'str'},
        {'column': 'channel', 'field': 'Channel', 'type': 'str'},
        {'column': 'campaign_type', 'field': 'Campaign Type', 'type': 'str'},
        {'column': 'cost_currency', 'field': 'Cost Currency', 'type': 'str'},
        {'column': 'cost_model', 'field': 'Cost Model', 'type': 'str'},
        {'column': 'cost_value', 'field': 'Cost Value', 'type': 'str'},
        {'column': 'site_id', 'field': 'Site ID', 'type': 'str'},
        {'column': 'sub_site_id', 'field': 'Sub Site ID', 'type': 'str'},
        {'column': 'is_retargeting', 'field': 'Is Retargeting', 'type': 'str'},
        {'column': 'media_source', 'field': 'Media Source', 'type': 'str'},
        {'column': 'original_url', 'field': 'Original URL', 'type': 'str'},
        {'column': 'user_agent', 'field': 'User Agent', 'type': 'str'},
        {'column': 'advertising_id', 'field': 'Advertising ID', 'type': 'str'},
        {'column': 'android_id', 'field': 'Android ID', 'type': 'str'},
        {'column': 'app_id', 'field': 'App ID', 'type': 'str'},
        {'column': 'app_name', 'field': 'App Name', 'type': 'str'},
        {'column': 'app_version', 'field': 'App Version', 'type': 'str'},
        {'column': 'apps_flyer_id', 'field': 'AppsFlyer ID', 'type': 'str'},
        {'column': 'bundle_id', 'field': 'Bundle ID', 'type': 'str'},
        {'column': 'carrier', 'field': 'Carrier', 'type': 'str'},
        {'column': 'country_code', 'field': 'Country Code', 'type': 'str'},
        {'column': 'custom_data', 'field': 'Custom Data', 'type': 'str'},
        {'column': 'deeplink_url', 'field': 'Deeplink URL', 'type': 'str'},
        {'column': 'device_category', 'field': 'Device Category', 'type': 'str'},
        {'column': 'device_type', 'field': 'Device Type', 'type': 'str'},
        {'column': 'device_model', 'field': 'Device Model', 'type': 'str'},
        {'column': 'device_download_time', 'field': 'Device Download Time', 'type': 'datetime'},
        {'column': 'event_name', 'field': 'Event Name', 'type': 'str'},
        {'column': 'event_revenue', 'field': 'Event Revenue', 'type': 'str'},
        {'column': 'event_revenue_currency', 'field': 'Event Revenue Currency', 'type': 'str'},
        {'column': 'event_time', 'field': 'Event Time', 'type': 'datetime'},
        {'column': 'event_value', 'field': 'Event Value', 'type': 'str'},
        {'column': 'idfa', 'field': 'IDFA', 'type': 'str'},
        {'column': 'idfv', 'field': 'IDFV', 'type': 'str'},
        {'column': 'imei', 'field': 'IMEI', 'type': 'str'},
        {'column': 'install_app_store', 'field': 'Install App Store', 'type': 'str'},
        {'column': 'install_time', 'field': 'Install Time', 'type': 'datetime'},
        {'column': 'ip', 'field': 'IP', 'type': 'str'},
        {'column': 'is_lat', 'field': 'Is LAT', 'type': 'str'},
        {'column': 'language', 'field': 'Language', 'type': 'str'},
        {'column': 'oaid', 'field': 'OAID', 'type': 'str'},
        {'column': 'att', 'field': 'ATT', 'type': 'str'},
        {'column': 'operator', 'field': 'Operator', 'type': 'str'},
        {'column': 'os_version', 'field': 'OS Version', 'type': 'str'},
        {'column': 'platform', 'field': 'Platform', 'type': 'str'},
        {'column': 'sdk_version', 'field': 'SDK Version', 'type': 'str'},
        {'column': 'wifi', 'field': 'WIFI', 'type': 'str'},
        {'column': 'store_reinstall', 'field': 'Store Reinstall', 'type': 'str'},
        {'column': 'amazon_fire_id', 'field': 'Amazon Fire ID', 'type': 'str'},
        {'column': 'city', 'field': 'City', 'type': 'str'},
        {'column': 'attributed_touch_time', 'field': 'Attributed touch Time', 'type': 'str'},
        {'column': 'attributed_touch_type', 'field': 'Attributed touch Type', 'type': 'str'},
        {'column': 'dma', 'field': 'DMA', 'type': 'str'},
        {'column': 'event_revenue_usd', 'field': 'Event Revenue USD', 'type': 'str'},
        {'column': 'event_source', 'field': 'Event Source', 'type': 'str'},
        {'column': 'http_referrer', 'field': 'HTTP Referrer', 'type': 'str'},
        {'column': 'is_primary_attribution', 'field': 'Is Primary Attribution', 'type': 'str'},
        {'column': 'is_receipt_validated', 'field': 'Is Receipt Validated', 'type': 'str'},
        {'column': 'match_type', 'field': 'Match Type', 'type': 'str'},
        {'column': 'postal_code', 'field': 'Postal Code', 'type': 'str'},
        {'column': 'region', 'field': 'Region', 'type': 'str'},
        {'column': 'retargeting_conversation_type', 'field': 'Retargeting Conversion Type', 'type': 'str'},
        {'column': 'state', 'field': 'State', 'type': 'str'},
        {'column': 'keyword_id', 'field': 'Keyword ID', 'type': 'str'},
        {'column': 'keyword_match_type', 'field': 'Keyword match Type', 'type': 'str'},
        {'column': 'network_account_id', 'field': 'Network account ID', 'type': 'str'},
        {'column': 'rejected_reason_value', 'field': 'Rejected reason value', 'type': 'str'},
        {'column': 'rejected_reason', 'field': 'Rejected reason', 'type': 'str'},
        {'column': 'blocked_reason', 'field': 'Blocked reason', 'type': 'str'},
        {'column': 'blocked_sub_reason', 'field': 'Blocked sub reason', 'type': 'str'},
        {'column': 'blocked_reason_value', 'field': 'Blocked reason value', 'type': 'str'},
        {'column': 'google_play_click_time', 'field': 'Google Play click time', 'type': 'datetime'},
        {'column': 'google_play_install_begin_time', 'field': 'Google Play install begin time', 'type': 'datetime'},
        {'column': 'google_play_referrer', 'field': 'Google Play referrer', 'type': 'str'},
        {'column': 'google_play_broadcast_referrer', 'field': 'Google Play broadcast referrer', 'type': 'str'},
        {'column': 'ad_unit', 'field': 'Ad unit', 'type': 'str'},
        {'column': 'segment', 'field': 'segment', 'type': 'str'},
        {'column': 'placement', 'field': 'Placement', 'type': 'str'},
        {'column': 'monatization_network', 'field': 'Monetization network', 'type': 'str'},
        {'column': 'impressions', 'field': 'Impressions', 'type': 'str'},
        {'column': 'mediation_network', 'field': 'Mediation network', 'type': 'str'},
        {'column': 'custom_dimension', 'field': 'Custom dimension'},
        {'column': 'app_type', 'field': 'App type', 'type': 'str'},
        {'column': 'fraud_reason', 'field': 'Fraud reason', 'type': 'str'},
        {'column': 'fraud_sub_reason', 'field': 'Fraud sub reason', 'type': 'str'},
        {'column': 'is_organic', 'field': 'Is organic', 'type': 'str'},
        {'column': 'detection_date', 'field': 'Detection date', 'type': 'str'},
        {'column': 'customer_user_id', 'field': 'Customer User ID', 'type': 'str'},
        {'column': 'attribution_lookback', 'field': 'Attribution Lookback', 'type': 'str'}
    ]

    @staticmethod
    def __get_env_from_field_data(data: str) -> str:
        env = '\\N'
        env_field = 'env'
        json_data = json.loads(data)
        if env_field in json_data:
            env = json_data[env_field]
        return env

    @staticmethod
    def process(row: Dict) -> Dict:
        env = '\\N'
        document = {}
        min_date_time = get_default_value(datetime)
        for field in row.keys():
            val = row.get(field)
            for map_settings in ReportRowTransformer.ATTRIBUTES_MAPPER:
                if str.lower(map_settings['field']) == str.lower(field):
                    if map_settings['type'] == 'str':
                        val = row.get(field).replace('\t', '').replace('\n', '')
                        if len(val) > 0 and val[-1] == '\\':
                            val = val[0:len(val)-1]
                        if val == 'None':
                            val = ''
                        if str.lower(map_settings['column']) == ReportRowTransformer.EVENT_VALUE_FIELD and len(val) > 0:
                            env = ReportRowTransformer.__get_env_from_field_data(val)
                    if map_settings['type'] == 'datetime':
                        if row.get(field) != '':
                            val = parser.parse(row.get(field))
                        else:
                            val = min_date_time
                        if val.replace(tzinfo=timezone.utc).timestamp() < min_date_time.replace(
                                tzinfo=timezone.utc).timestamp():
                            val = min_date_time
                    document[map_settings['column']] = val
        document['environment'] = env
        return document
