import pandas as pd
import io
import os
import csv
import requests
from datetime import datetime, timedelta
from airflow import DAG
from airflow.exceptions import AirflowSkipException
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.hooks.base import BaseHook
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.file_path_service import FilePathService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE, TSV_DATA_TYPE
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.services.load_data_http_service import HTTP_METHOD_GET
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.helpers.date_helper import get_date_previous_day, get_max_date, str_to_date_time
from dags.appsflyer_data.cohort_user_acquisition_row_transformer import CohortUserAcquisitionRowTransformer
from dags.appsflyer_data.report_row_transformer import ReportRowTransformer
from dags.lib.helpers.csv_helper import csv_stream
import dags.appsflyer_data.config.default as dag_config
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
APPS_FLYER_APP_ID_ANDROID = config.get_setting(dag_config.APPS_FLYER_APP_ID_ANDROID_KEY)
APPS_FLYER_APP_ID_IOS = config.get_setting(dag_config.APPS_FLYER_APP_ID_IOS_KEY)
APPS_FLYER_TOKEN_V2 = config.get_setting(dag_config.APPS_FLYER_TOKEN_V2_KEY)
AWS_DIRECTORY_COHORT_USER_ACQUISITION_KEY = config.get_setting(dag_config.AWS_DIRECTORY_COHORT_USER_ACQUISITION_KEY)
AWS_DATA_LOCKER_BUCKET = config.get_setting(dag_config.AWS_DATA_LOCKER_BUCKET_KEY)

file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_SKAN_AGGREGATED_PERFORMANCE_REPORT_FILE_PATH:
            [APPS_FLYER_APP_ID_IOS],
        dag_config.TMP_MARKETING_CAMPAIGN_FILE_PATH: [APPS_FLYER_APP_ID_IOS],
        dag_config.TMP_RAW_DATA_ANDROID_REPORT_FILE_PATH:
            [dag_config.INSTALL_REPORT_TYPE,
             dag_config.IN_APP_EVENTS_REPORT_TYPE,
             dag_config.UNINSTALL_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_INSTALLS_REPORT_TYPE,
             dag_config.ORGANIC_IN_APP_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_UNINSTALL_EVENTS_REPORT_TYPE],
        dag_config.TMP_RAW_DATA_IOS_REPORT_FILE_PATH:
            [dag_config.INSTALL_REPORT_TYPE,
             dag_config.IN_APP_EVENTS_REPORT_TYPE,
             dag_config.UNINSTALL_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_INSTALLS_REPORT_TYPE,
             dag_config.ORGANIC_IN_APP_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_UNINSTALL_EVENTS_REPORT_TYPE],
        dag_config.TMP_RAW_DATA_ANDROID_REPORT_RESULT_FILE_PATH:
            [dag_config.INSTALL_REPORT_TYPE,
             dag_config.IN_APP_EVENTS_REPORT_TYPE,
             dag_config.UNINSTALL_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_INSTALLS_REPORT_TYPE,
             dag_config.ORGANIC_IN_APP_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_UNINSTALL_EVENTS_REPORT_TYPE],
        dag_config.TMP_RAW_DATA_IOS_REPORT_RESULT_FILE_PATH:
            [dag_config.INSTALL_REPORT_TYPE,
             dag_config.IN_APP_EVENTS_REPORT_TYPE,
             dag_config.UNINSTALL_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_INSTALLS_REPORT_TYPE,
             dag_config.ORGANIC_IN_APP_EVENTS_REPORT_TYPE,
             dag_config.ORGANIC_UNINSTALL_EVENTS_REPORT_TYPE]
    }
)


def save_apps_flyer_state(source_name: str, key_name: str, date: datetime):
    state = {
        'connection_id': config.get_connection(ConfigService.CONN_AWS_S3),
        'source_name': source_name,
        'key_name': key_name,
        'last_value_date': date
    }

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    df = pd.DataFrame([state])
    ch.insert_dataframe(df=df, table_name=CH_EXTRACT_STATE_TABLE)
    LoggerService.logger.info(f'Saved state for "{source_name}"')


def truncate_etl_table(table_name: str):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(table_name)
    LoggerService.logger.info(f'Truncated table "{table_name}"')


def clear_temp_files():
    for path in file_paths.get_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def move_data_from_etl_to_main_table(etl_table: str, main_table: str):
    try:
        LoggerService.start_move_data(etl_table, main_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {main_table} SELECT * FROM {conn_etl.schema}.{etl_table}')
    except Exception as e:
        LoggerService.error_move_data(etl_table, main_table, str(e))
        raise


def get_period_dates(source_name: str, key: str = 'start_date', date_format: str = '%Y-%m-%d',
                     date_increase: str = 'day'):
    try:
        LoggerService.logger.info(f'Start get last date processes marketing campaign data')
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        extract_state = ExtractStateService(ch)
        min_start_date = dag_config.MIN_START_DATE \
            if date_format == '%Y-%m-%d' \
            else f'{dag_config.MIN_START_DATE} 00:00:00'

        last_start_date = extract_state.get_last_value(
            config.get_connection(ConfigService.CONN_AWS_S3),
            source_name,
            key,
            ExtractStateService.DATE_TYPE
        )

        if date_increase == 'day':
            last_start_date += timedelta(days=1)
        else:
            last_start_date += timedelta(seconds=1)

        start_date = get_max_date(
            last_start_date.strftime(date_format),
            min_start_date,
            date_format
        )
        end_date = get_date_previous_day().strftime(date_format)
        LoggerService.logger.info({'StartDate': start_date, 'EndDate': end_date})
        if str_to_date_time(start_date, date_format) > str_to_date_time(end_date, date_format):
            raise AirflowSkipException
        return {'start_date': start_date, 'end_date': end_date}
    except Exception as e:
        LoggerService.logger.error(f'Error get last date processes marketing campaign data, '
                                   f'see detailed information: {str(e)}')
        raise


def load_cohort_user_acquisition(app_id: str):
    period_dates = get_period_dates(f'{AWS_DIRECTORY_COHORT_USER_ACQUISITION_KEY}/{app_id}')
    start_date = str_to_date_time(period_dates['start_date']).date()
    end_date = str_to_date_time(period_dates['end_date']).date()
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    s3_hook = S3Hook(aws_conn_id=config.get_connection(ConfigService.CONN_AWS_S3))
    if end_date == start_date:
        return

    while True:
        aws_directory = f'{AWS_DIRECTORY_COHORT_USER_ACQUISITION_KEY}/dt={start_date.strftime("%Y-%m-%d")}/app_id={app_id}'
        parts = s3_hook.list_keys(AWS_DATA_LOCKER_BUCKET, prefix=aws_directory, page_size=dag_config.AWS_PAGE_SIZE)
        LoggerService.logger.info(f'Load directory "{aws_directory}"')
        for part in parts:
            buffer = io.BytesIO()
            obj = s3_hook.get_key(bucket_name=AWS_DATA_LOCKER_BUCKET, key=part)
            obj.download_fileobj(buffer)
            df = CohortUserAcquisitionRowTransformer.process(pd.read_parquet(buffer), app_id, part)
            ch.insert_dataframe(df=df, table_name=dag_config.CH_APPSFLYER_COHORT_USER_ACQUISITION_EXTRACTED_TABLE)
            LoggerService.logger.info(f'Data from file {part} was loaded '
                                      f'to {dag_config.CH_APPSFLYER_COHORT_USER_ACQUISITION_EXTRACTED_TABLE} '
                                      f'successful, was loaded {len(df.index)} rows')
        start_date += timedelta(days=1)
        if start_date > end_date:
            break

    save_apps_flyer_state(
        f'{AWS_DIRECTORY_COHORT_USER_ACQUISITION_KEY}/{app_id}',
        'start_date',
        datetime.combine(end_date, datetime.max.time())
    )

    LoggerService.logger.info(f'End load cohort user acquisition for "{app_id}"')


def extract_apps_flyer_data(app_id: str, app_token: str, api_path: str, file_path_tpl: str):
    period_dates = get_period_dates(f'{dag_config.CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_TABLE}/{app_id}')
    start_date = period_dates['start_date']
    end_date = period_dates['end_date']
    file_path = file_paths.get_path(
        APPS_FLYER_APP_ID_IOS,
        file_path_tpl
    )
    LoggerService.logger.info(f'Start get data from Apps Flyer and write to {file_path}')
    base_url = (config.get_setting(dag_config.APPS_FLYER_API_KEY)).replace('[path]', api_path)
    request_url = f'{base_url}/{app_id}?start_date={start_date}&end_date={end_date}'
    LoggerService.logger.info(f'Get AppsFlyer data from {request_url}')
    headers = {'Authorization': 'Bearer {}'.format(app_token)}
    res = requests.request(HTTP_METHOD_GET, request_url, headers=headers)
    if res.status_code == 200:
        f = open(file_path, 'w', newline='', encoding='utf-8')
        if str_to_date_time(start_date).date() != str_to_date_time(end_date).date():
            f.write(res.text)
        f.close()
    else:
        LoggerService.logger.error(f'Cannot get data from AppsFlyer URL: {request_url}, headers: {headers}, '
                                   f'see detailed information: code = {res.status_code}, text: {res.text}')
        raise


def save_state_by_app_id_task(table_name: str, app_id: str, date_format: str = '%Y-%m-%d',
                              date_increase: str = 'day'):
    period_dates = get_period_dates(
        source_name=f'{table_name}/{app_id}',
        date_format=date_format,
        date_increase=date_increase
    )
    start_date = str_to_date_time(period_dates['start_date'], date_format)
    end_date = str_to_date_time(period_dates['end_date'], date_format)
    if end_date == start_date:
        return

    if date_format == '%Y-%m-%d':
        end_date = datetime.combine(end_date.date(), datetime.max.time())

    save_apps_flyer_state(
        f'{table_name}/{app_id}',
        'start_date',
        end_date
    )


def extract_raw_report_data(app_id: str, app_token: str, report_type: str, additional_fields: str,
                            file_path: str, start_date: str, end_date: str):
    try:
        LoggerService.logger.info(f'Start get data from AppsFlyer and write to {file_path} for period '
                                  f'from {start_date} to {end_date}')
        params = {
            'from': start_date,
            'to': end_date,
            'additional_fields': additional_fields,
            'maximum_rows': 1000000,
        }
        headers = {
            'accept': 'text/csv',
            'authorization': 'Bearer {}'.format(app_token)
        }
        f = open(file_path, 'w', newline='', encoding='utf-8')
        request_url = (config.get_setting(dag_config.APPS_FLYER_EXPORT_URL_KEY)).format(app_id, report_type)
        res = requests.request(HTTP_METHOD_GET, request_url, params=params, headers=headers)

        LoggerService.logger.error(f'Try get data from AppsFlyer URL: {request_url}, params: {params}')

        if res.status_code == 200:
            f.write(res.text)
        else:
            LoggerService.logger.error(f'Cannot get data, '
                                       f'see detailed information: code = {res.status_code}, text: {res.text}')
            raise Exception(f'Wrong response code ({res.status_code})')
        f.close()
    except Exception as e:
        LoggerService.logger.error(f'Error get data from AppsFlyer and write to {file_path} for period '
                                   f'from {start_date} to {end_date}, see detailed information: {str(e)}')
        raise e


def get_reports_data(app_id: str, app_token: str, report_type: str, additional_fields: str):
    try:
        period_dates = get_period_dates(
            source_name=f'{report_type}/{app_id}',
            date_format='%Y-%m-%d %H:%M:%S',
            date_increase='seconds'
        )
        start_date = period_dates['start_date']
        end_date = period_dates['end_date']

        if app_id == APPS_FLYER_APP_ID_IOS:
            sfp = dag_config.TMP_RAW_DATA_IOS_REPORT_FILE_PATH
            rfp = dag_config.TMP_RAW_DATA_IOS_REPORT_RESULT_FILE_PATH
        else:
            sfp = dag_config.TMP_RAW_DATA_ANDROID_REPORT_FILE_PATH
            rfp = dag_config.TMP_RAW_DATA_ANDROID_REPORT_RESULT_FILE_PATH

        source_file_path = file_paths.get_path(report_type, sfp)
        result_file_path = file_paths.get_path(report_type, rfp)
        counter = 0
        LoggerService.logger.info(f'Start get data from {source_file_path} and write data to {result_file_path}')
        result_file = open(result_file_path, 'w', newline='', encoding='utf-8')
        extract_raw_report_data(
            app_id=app_id,
            app_token=app_token,
            report_type=report_type,
            additional_fields=additional_fields,
            file_path=source_file_path,
            start_date=start_date,
            end_date=end_date
        )
        for row in csv_stream(source_file_path):
            document = ReportRowTransformer.process(row)
            if counter == 0:
                fieldnames = list(document.keys())
            writer = csv.DictWriter(result_file, delimiter='\t', lineterminator='\n', fieldnames=fieldnames)
            if counter == 0:
                writer.writeheader()
            writer.writerow(document)
            counter += 1
        LoggerService.logger.info(f'Finish get data from {source_file_path} and write data to {result_file_path}')
        result_file.close()
    except Exception as e:
        LoggerService.logger.error(f'Error get data, see detailed information: {str(e)}')
        raise


def move_raw_data_reports(from_table: str):
    try:
        to_table = dag_config.CH_APPSFLYER_RAW_DATA_TABLE
        LoggerService.start_move_data(from_table, to_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {to_table} SELECT * FROM {conn_etl.schema}.{from_table}')
    except Exception as e:
        LoggerService.error_move_data(from_table, to_table, str(e))
        raise


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_files_task = PythonOperator(
        task_id='clear_temp_files',
        python_callable=clear_temp_files
    )

    truncate_cohort_user_acquisition_extracted_table_task = PythonOperator(
        task_id='truncate_cohort_user_acquisition_extracted_table',
        python_callable=truncate_etl_table,
        op_kwargs={
            'table_name': dag_config.CH_APPSFLYER_COHORT_USER_ACQUISITION_EXTRACTED_TABLE
        }
    )

    start_load_cohort_user_acquisition_task = EmptyOperator(
        task_id='start_load_cohort_user_acquisition'
    )

    load_cohort_user_acquisition_tasks = []
    for app_id in [APPS_FLYER_APP_ID_ANDROID, APPS_FLYER_APP_ID_IOS]:
        op_system = 'ios' if app_id == APPS_FLYER_APP_ID_IOS else 'android'
        load_cohort_user_acquisition_tasks.append(
            PythonOperator(
                task_id=f'load_cohort_user_acquisition_app_id_{op_system}',
                python_callable=load_cohort_user_acquisition,
                op_kwargs={
                    'app_id': app_id
                }
            )
        )

    finish_load_cohort_user_acquisition_task = EmptyOperator(
        task_id='finish_load_cohort_user_acquisition'
    )

    move_cohort_user_acquisition_extracted_table_task = PythonOperator(
        task_id='move_cohort_user_acquisition',
        python_callable=move_data_from_etl_to_main_table,
        op_kwargs={
            'etl_table': dag_config.CH_APPSFLYER_COHORT_USER_ACQUISITION_EXTRACTED_TABLE,
            'main_table': dag_config.CH_APPSFLYER_COHORT_USER_ACQUISITION_TABLE
        }
    )

    truncate_skan_aggregated_performance_report_extracted_table_task = PythonOperator(
        task_id='truncate_skan_aggregated_performance_report_extracted_table',
        python_callable=truncate_etl_table,
        op_kwargs={
            'table_name': dag_config.CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_EXTRACTED_TABLE
        }
    )

    extract_skan_aggregated_performance_report_data_ios_task = PythonOperator(
        task_id='extract_skan_aggregated_performance_report_data_ios',
        python_callable=extract_apps_flyer_data,
        op_kwargs={
            'app_id': APPS_FLYER_APP_ID_IOS,
            'app_token': APPS_FLYER_TOKEN_V2,
            'api_path': 'skadnetworks',
            'file_path_tpl': dag_config.TMP_SKAN_AGGREGATED_PERFORMANCE_REPORT_FILE_PATH
        }
    )

    load_skan_aggregated_performance_report_data_ios_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_path(
            APPS_FLYER_APP_ID_IOS,
            dag_config.TMP_SKAN_AGGREGATED_PERFORMANCE_REPORT_FILE_PATH
        ),
        table_name=dag_config.CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_EXTRACTED_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='load_skan_aggregated_performance_report_data_ios',
        delimiter=','
    )

    move_skan_aggregated_performance_report_data_extracted_table_task = PythonOperator(
        task_id='move_skan_aggregated_performance_report_data',
        python_callable=move_data_from_etl_to_main_table,
        op_kwargs={
            'etl_table': dag_config.CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_EXTRACTED_TABLE,
            'main_table': dag_config.CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_TABLE
        }
    )

    save_state_skan_aggregated_performance_report_data_ios_task = PythonOperator(
        task_id='save_state_skan_aggregated_performance_report_data_ios',
        python_callable=save_state_by_app_id_task,
        op_kwargs={
            'app_id': APPS_FLYER_APP_ID_IOS,
            'table_name': dag_config.CH_APPSFLYER_SKAN_AGGREGATED_PERFORMANCE_REPORT_TABLE
        }
    )

    truncate_marketing_campaign_extracted_table_task = PythonOperator(
        task_id='truncate_marketing_campaign_extracted_table',
        python_callable=truncate_etl_table,
        op_kwargs={
            'table_name': dag_config.CH_APPSFLYER_MARKETING_CAMPAIGN_EXTRACTED_TABLE
        }
    )

    extract_marketing_campaign_data_ios_task = PythonOperator(
        task_id='extract_marketing_campaign_data_ios',
        python_callable=extract_apps_flyer_data,
        op_kwargs={
            'app_id': APPS_FLYER_APP_ID_IOS,
            'app_token': APPS_FLYER_TOKEN_V2,
            'api_path': 'skadnetworks-postbacks',
            'file_path_tpl': dag_config.TMP_MARKETING_CAMPAIGN_FILE_PATH
        }
    )

    load_marketing_campaign_data_ios_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_path(
            APPS_FLYER_APP_ID_IOS,
            dag_config.TMP_MARKETING_CAMPAIGN_FILE_PATH
        ),
        table_name=dag_config.CH_APPSFLYER_MARKETING_CAMPAIGN_EXTRACTED_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='load_marketing_campaign_data_ios',
        delimiter=','
    )

    move_marketing_campaign_data_extracted_table_task = PythonOperator(
        task_id='move_marketing_campaign_data',
        python_callable=move_data_from_etl_to_main_table,
        op_kwargs={
            'etl_table': dag_config.CH_APPSFLYER_MARKETING_CAMPAIGN_EXTRACTED_TABLE,
            'main_table': dag_config.CH_APPSFLYER_MARKETING_CAMPAIGN_TABLE
        }
    )

    save_state_marketing_campaign_data_ios_task = PythonOperator(
        task_id='save_state_marketing_campaign_data_ios',
        python_callable=save_state_by_app_id_task,
        op_kwargs={
            'app_id': APPS_FLYER_APP_ID_IOS,
            'table_name': dag_config.CH_APPSFLYER_MARKETING_CAMPAIGN_TABLE
        }
    )

    start_truncate_report_tables_task = EmptyOperator(
        task_id='start_truncate_report_tables'
    )

    truncate_report_tables_tasks = [
        PythonOperator(
            task_id=f'truncate_table_{report["table"]}',
            python_callable=truncate_etl_table,
            op_kwargs={
                'table_name': report['table']
            }
        )
        for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_truncate_report_tables_task = EmptyOperator(
        task_id='finish_truncate_report_tables'
    )

    start_get_reports_android_data_task = EmptyOperator(
        task_id='start_get_reports_android_data'
    )

    get_reports_android_data_tasks = [
        PythonOperator(
            task_id=f'get_{report["report_type"]}_android_data',
            python_callable=get_reports_data,
            op_kwargs={
                'app_id': APPS_FLYER_APP_ID_ANDROID,
                'app_token': APPS_FLYER_TOKEN_V2,
                'report_type': report['report_type'],
                'additional_fields': report['additional_fields']
            }
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_get_reports_android_data_task = EmptyOperator(
        task_id='finish_get_reports_android_data'
    )

    load_reports_android_data_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(
                report['report_type'],
                dag_config.TMP_RAW_DATA_ANDROID_REPORT_RESULT_FILE_PATH
            ),
            table_name=report['table'],
            data_type=TSV_DATA_TYPE,
            task_id=f'load_{report["report_type"]}_android_data'
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_load_reports_android_data_task = EmptyOperator(
        task_id='finish_load_reports_android_data_task'
    )

    get_reports_ios_data_tasks = [
        PythonOperator(
            task_id=f'get_{report["report_type"]}_ios_data',
            python_callable=get_reports_data,
            op_kwargs={
                'app_id': APPS_FLYER_APP_ID_IOS,
                'app_token': APPS_FLYER_TOKEN_V2,
                'report_type': report['report_type'],
                'additional_fields': report['additional_fields']
            }
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_get_reports_ios_data_task = EmptyOperator(
        task_id='finish_get_reports_ios_data'
    )

    load_reports_ios_data_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(
                report['report_type'],
                dag_config.TMP_RAW_DATA_IOS_REPORT_RESULT_FILE_PATH
            ),
            table_name=report['table'],
            data_type=TSV_DATA_TYPE,
            task_id=f'load_{report["report_type"]}_ios_data'
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_load_reports_ios_data_task = EmptyOperator(
        task_id='finish_load_reports_ios_data_task'
    )

    move_reports_data_tasks = [
        PythonOperator(
            task_id=f'move_{report["report_type"]}_data',
            python_callable=move_data_from_etl_to_main_table,
            op_kwargs={
                'etl_table': report['table'],
                'main_table': report['table'].replace('_extracted', '')
            }
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_move_reports_data_task = EmptyOperator(
        task_id='finish_move_reports_data_task'
    )

    save_state_reports_android_data_tasks = [
        PythonOperator(
            task_id=f'save_state_{report["report_type"]}_android_data_task',
            python_callable=save_state_by_app_id_task,
            op_kwargs={
                'app_id': APPS_FLYER_APP_ID_ANDROID,
                'table_name': f'{report["report_type"]}',
                'date_format': '%Y-%m-%d %H:%M:%S',
                'date_increase': 'seconds'
            }
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_save_state_reports_android_data_task = EmptyOperator(
        task_id='finish_save_state_reports_android_data_task'
    )

    save_state_reports_ios_data_tasks = [
        PythonOperator(
            task_id=f'save_state_{report["report_type"]}_ios_data_task',
            python_callable=save_state_by_app_id_task,
            op_kwargs={
                'app_id': APPS_FLYER_APP_ID_IOS,
                'table_name': f'{report["report_type"]}',
                'date_format': '%Y-%m-%d %H:%M:%S',
                'date_increase': 'seconds'
            }
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_save_state_reports_ios_data_task = EmptyOperator(
        task_id='finish_save_state_reports_ios_data_task'
    )

    move_raw_data_reports_tasks = [
        PythonOperator(
            task_id=f'move_raw_data_{report["report_type"]}',
            python_callable=move_raw_data_reports,
            op_kwargs={
                'from_table': report['table']
            }
        ) for report in dag_config.ORGANIC_AND_NON_ORGANIC_TABLES_SETTINGS
    ]

    finish_move_raw_data_reports_task = EmptyOperator(
        task_id='finish_move_raw_data_reports_task'
    )

    clear_temp_files_task >> truncate_cohort_user_acquisition_extracted_table_task \
        >> start_load_cohort_user_acquisition_task >> load_cohort_user_acquisition_tasks \
        >> finish_load_cohort_user_acquisition_task >> move_cohort_user_acquisition_extracted_table_task

    clear_temp_files_task >> truncate_skan_aggregated_performance_report_extracted_table_task \
        >> extract_skan_aggregated_performance_report_data_ios_task \
        >> load_skan_aggregated_performance_report_data_ios_task \
        >> move_skan_aggregated_performance_report_data_extracted_table_task \
        >> save_state_skan_aggregated_performance_report_data_ios_task

    clear_temp_files_task >> truncate_marketing_campaign_extracted_table_task \
        >> extract_marketing_campaign_data_ios_task >> load_marketing_campaign_data_ios_task \
        >> move_marketing_campaign_data_extracted_table_task >> save_state_marketing_campaign_data_ios_task

    clear_temp_files_task >> start_truncate_report_tables_task >> truncate_report_tables_tasks \
        >> finish_truncate_report_tables_task >> start_get_reports_android_data_task >> get_reports_android_data_tasks \
        >> finish_get_reports_android_data_task >> load_reports_android_data_tasks \
        >> finish_load_reports_android_data_task >> get_reports_ios_data_tasks >> finish_get_reports_ios_data_task \
        >> load_reports_ios_data_tasks >> finish_load_reports_ios_data_task >> move_reports_data_tasks \
        >> finish_move_reports_data_task >> save_state_reports_android_data_tasks \
        >> finish_save_state_reports_android_data_task >> save_state_reports_ios_data_tasks \
        >> finish_save_state_reports_ios_data_task >> move_raw_data_reports_tasks >> finish_move_raw_data_reports_task

if __name__ == '__main__':
    dag.cli()
