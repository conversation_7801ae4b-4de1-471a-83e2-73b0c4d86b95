from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for add new currencies to datamarts',
}

CH_CBR_CURRENCY_TABLE = 'cbr_currency'
CH_CBR_CURRENCY_TABLE_LOAD = 'cbr_currency_load'
EXCHANGE_RATES_API_KEY_VARIABLE = 'EXCHANGE_RATES_API_KEY'

TMP_CURRENCIES_EXTRACT_STATE_FILE_PATH = 'currencies_es_[connection].csv'

SETTINGS = {
    'exchange_rates_url': 'https://api.apilayer.com/exchangerates_data'
}
