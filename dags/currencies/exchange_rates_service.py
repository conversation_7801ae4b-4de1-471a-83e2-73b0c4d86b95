from typing import Dict, List
import requests


class ExchangeRatesService:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {
            'apikey': api_key
        }

    def symbols(self) -> Dict:
        path = 'symbols'
        request = requests.get(url=f'{self.base_url}/{path}', headers=self.headers)
        if request.status_code == 200:
            result = request.json()
            return result[path]
        else:
            raise Exception(f'Get symbols of currencies code {request.status_code}')

    def latest(self, symbols: List, base_currency: str = 'USD') -> Dict:
        path = 'latest'
        request = requests.get(
            url=f'{self.base_url}/{path}?base={base_currency}&symbols={",".join(symbols)}',
            headers=self.headers
        )
        if request.status_code == 200:
            return request.json()
        else:
            raise Exception(f'Get currencies rate code {request.status_code}')


