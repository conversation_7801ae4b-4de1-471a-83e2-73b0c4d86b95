import os
import csv
from typing import List, Dict
from datetime import datetime, timed<PERSON>ta
from airflow import DAG
from airflow.models import Variable
from airflow.exceptions import AirflowSkipException
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.file_path_service import FilePathService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.helpers.convert_value_helper import get_converted_value
from dags.currencies.exchange_rates_service import ExchangeRatesService
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.helpers.date_helper import str_to_date_time, get_current_datetime, date_time_to_str
import dags.currencies.config.default as dag_config
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_CURRENCIES_EXTRACT_STATE_FILE_PATH:
            [config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS)]
    }
)


def clear_tmp_files():
    for path in file_paths.get_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def check_last_load_date():
    try:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        extract_state = ExtractStateService(ch)
        datamarts_connection_id = config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS)
        last_date = get_converted_value(val=extract_state.get_last_value(
            datamarts_connection_id,
            dag_config.CH_CBR_CURRENCY_TABLE_LOAD,
            'last_date',
            ExtractStateService.DATE_TYPE
        ), type_val=datetime)
        current_date = get_converted_value(val=get_current_datetime(), type_val=datetime)
        if (last_date + timedelta(hours=8)) > current_date:
            raise AirflowSkipException

        path_state = file_paths.get_path(
            datamarts_connection_id,
            dag_config.TMP_CURRENCIES_EXTRACT_STATE_FILE_PATH
        )
        path_state_header = file_paths.get_header_path(dag_config.TMP_CURRENCIES_EXTRACT_STATE_FILE_PATH)

        states = [{
            'connection_id': datamarts_connection_id,
            'source_name': dag_config.CH_CBR_CURRENCY_TABLE_LOAD,
            'key_name': 'last_date',
            'last_value_date': date_time_to_str(get_current_datetime())
        }]
        header = states[0].keys()

        if not os.path.exists(path_state_header):
            with open(path_state_header, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, header, delimiter=';')
                writer.writeheader()

        with open(path_state, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, header, delimiter=';')
            for state in states:
                writer.writerow(state)

    except Exception as e:
        LoggerService.logger.error(f'Error get last date of load currencies, see detailed information: {str(e)}')
        raise e


def get_data_marts_code_currencies() -> List:
    try:
        data_marts_currencies = []
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        data_marts_conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
        sql = f"SELECT DISTINCT char_code FROM remote('{data_marts_conn.host}', " \
              f"{data_marts_conn.schema}.{dag_config.CH_CBR_CURRENCY_TABLE}, '{data_marts_conn.login}', " \
              f"'{data_marts_conn.password}') WHERE num_code != '-1'"
        for row in ch.stream(sql):
            data_marts_currencies.append(row[0])
        return data_marts_currencies
    except Exception as e:
        LoggerService.logger.error(f'Error get datamarts currency codes, see detailed information: {str(e)}')
        raise e


def insert_currencies(currencies: List[Dict]):
    if len(currencies) > 0:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        data_marts_conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
        ch.insert(f"INSERT INTO FUNCTION remote('{data_marts_conn.host}', "
                  f"{data_marts_conn.schema}.{dag_config.CH_CBR_CURRENCY_TABLE_LOAD}, '{data_marts_conn.login}', "
                  f"'{data_marts_conn.password}') (num_code, char_code, nominal, name, value, date) VALUES ",
                  currencies)
        LoggerService.logger.info(f'Inserted {len(currencies)} currency rows')


def load_currencies():
    try:
        data_marts_code_currencies = get_data_marts_code_currencies()
        exchange_rates_service = ExchangeRatesService(
            base_url=config.get_setting(ConfigService.EXCHANGE_RATES_URL),
            api_key=Variable.get(dag_config.EXCHANGE_RATES_API_KEY_VARIABLE)
        )
        exchange_currencies = exchange_rates_service.symbols()
        symbols = []
        for code in exchange_currencies:
            if code not in data_marts_code_currencies:
                symbols.append(code)
        rate_currencies = exchange_rates_service.latest(symbols=symbols)
        currencies = []
        date_rate = str_to_date_time(rate_currencies['date']).date()
        rates = rate_currencies['rates']
        rub_rate = rates.pop('RUB')

        def calc_nominal_and_value(val: float) -> (int, float):
            degree = 0
            while val < 10:
                val *= 10
                degree += 1
            return 10**degree, round(val, 4)

        for char_code in rates:
            nominal, value = calc_nominal_and_value(float((1 / rates[char_code]) * rub_rate))
            currencies.append({
                'num_code': '-1',
                'char_code': char_code,
                'nominal': nominal,
                'name': exchange_currencies[char_code],
                'value': value,
                'date': date_rate
            })
        insert_currencies(currencies=currencies)
    except Exception as e:
        LoggerService.logger.error(f'Load currencies error: {str(e)}')
        raise


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_tmp_files_task = PythonOperator(
        task_id='clear_tmp_files',
        python_callable=clear_tmp_files
    )

    check_last_load_date_task = PythonOperator(
        task_id='check_last_load_date',
        python_callable=check_last_load_date
    )

    load_currencies_task = PythonOperator(
        task_id='load_currencies',
        python_callable=load_currencies
    )

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_CURRENCIES_EXTRACT_STATE_FILE_PATH,
        connection_ids=[config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS)]
    )

    load_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_CURRENCIES_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='load_extract_state'
    )

    clear_tmp_files_task >> check_last_load_date_task >> load_currencies_task >> concat_extract_state_files_task \
        >> load_extract_state_task

if __name__ == '__main__':
    dag.cli()
