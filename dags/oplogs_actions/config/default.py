from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load oplogs actions data from http request',
}

SETTINGS = {
    'oplogs_actions': [
        {'data_type': {'id': int, 'action_name': str}, 'columns': {'Id': 'id', 'ActionName': 'action_name'},
         'platform_type': 'vk', 'response_data_type': 'csv',
         'url': 'https://server-develop.elka2024-dev.starkgames.app/info/operationsList'},
        # {'data_type': {'id': int, 'action_name': str}, 'columns': {'Id': 'id', 'ActionName': 'action_name'},
        #  'platform_type': 'mobile', 'response_data_type': 'csv',
        #  'url': 'https://server-develop-mobile.elka2023-dev.starkgames.app/info/operationsList'},
        # {'data_type': {'id': int, 'action_name': str}, 'columns': {'Id': 'id', 'ActionName': 'action_name'},
        #  'platform_type': 'ok', 'response_data_type': 'csv',
        #  'url': 'https://server-develop-ok.elka2023-dev.starkgames.app/info/operationsList'},
        # {'data_type': {'id': int, 'action_name': str}, 'columns': {'Id': 'id', 'ActionName': 'action_name'},
        #  'platform_type': 'standalone', 'response_data_type': 'csv',
        #  'url': 'https://server-develop-standalone.elka2023-dev.starkgames.app/info/operationsList'}
    ]
}

CH_ACTIONS_EXTRACTED_TABLE = 'http_oplogs_actions_extracted'
CH_ACTIONS_TABLE = 'oplogs_actions'
