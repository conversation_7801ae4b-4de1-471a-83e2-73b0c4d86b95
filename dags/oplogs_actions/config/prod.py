DAG_ARGS = {
    'schedule_interval': '15 */6 * * *',
}

SETTINGS = {
    'oplogs_actions': [
        {'data_type': {'id': int, 'action_name': str}, 'columns': {'Id': 'id', 'ActionName': 'action_name'},
         'platform_type': 'vk', 'response_data_type': 'csv',
         'url': 'https://elka2024-server-vk.starkgames.app/info/operationsList'},
        {'data_type': {'id': int, 'action_name': str}, 'columns': {'Id': 'id', 'ActionName': 'action_name'},
         'platform_type': 'mobile', 'response_data_type': 'csv',
         'url': 'https://elka2024-server-mobile.starkgames.app/info/operationsList'},
        {'data_type': {'id': int, 'action_name': str}, 'columns': {'Id': 'id', 'ActionName': 'action_name'},
         'platform_type': 'ok', 'response_data_type': 'csv',
         'url': 'https://elka2024-server-ok.starkgames.app/info/operationsList'},
    ]
}
