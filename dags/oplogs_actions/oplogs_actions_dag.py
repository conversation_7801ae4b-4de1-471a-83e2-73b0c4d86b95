from typing import Dict
from airflow import DAG
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>
from airflow.hooks.base import BaseHook
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.load_data_http_service import LoadDataHttpService, REQUEST_DATA_TYPE_CSV
import dags.oplogs_actions.config.default as dag_config


DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)


def truncate_actions_extracted_table():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_ACTIONS_EXTRACTED_TABLE)


def load_platform_op_logs_actions(platform_load_settings: Dict):
    load_service = LoadDataHttpService(
        platform_type=platform_load_settings['platform_type'],
        url=platform_load_settings['url'],
        data_type=platform_load_settings['data_type'],
        columns=platform_load_settings['columns']
    )

    if platform_load_settings['response_data_type'] == REQUEST_DATA_TYPE_CSV:
        df = load_service.read_csv_response()
    else:
        df = load_service.read_json_response()

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.insert_dataframe(df, dag_config.CH_ACTIONS_EXTRACTED_TABLE)


def truncate_actions_main_table():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    ch.truncate(dag_config.CH_ACTIONS_TABLE)


def move_platform_op_logs_actions():
    try:
        from_table = dag_config.CH_ACTIONS_EXTRACTED_TABLE
        to_table = dag_config.CH_ACTIONS_TABLE
        LoggerService.start_move_data(from_table, to_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {to_table} SELECT * FROM {conn_etl.schema}.{from_table}')
    except Exception as e:
        LoggerService.error_move_data(from_table, to_table, str(e))
        raise


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}


with DAG(**dg) as dag:
    truncate_actions_extracted_table_task = PythonOperator(
        task_id='truncate_actions_extracted_table',
        python_callable=truncate_actions_extracted_table
    )

    load_platforms_actions_task = []
    for platform_settings in config.get_setting(ConfigService.OP_LOG_ACTIONS):
        load_platforms_actions_task.append(
            PythonOperator(
                task_id=f'load_{platform_settings["platform_type"]}_op_logs_actions',
                python_callable=load_platform_op_logs_actions,
                op_kwargs={
                    'platform_load_settings': platform_settings
                },
            )
        )

    truncate_actions_main_table_task = PythonOperator(
        task_id='truncate_actions_main_table',
        python_callable=truncate_actions_main_table
    )

    move_platforms_actions_task = PythonOperator(
        task_id='move_platforms_actions',
        python_callable=move_platform_op_logs_actions
    )

    truncate_actions_extracted_table_task >> load_platforms_actions_task \
        >> truncate_actions_main_table_task >> move_platforms_actions_task

if __name__ == '__main__':
    dag.cli()
