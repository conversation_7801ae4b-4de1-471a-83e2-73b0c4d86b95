import os
from glob import glob
import csv
from datetime import datetime, timedelta, timezone, time
from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from airflow.operators.python import PythonOperator
import dags.nginx_logs.config.default as dag_config
from dags.lib.helpers.date_helper import str_to_date_time, date_time_to_str
from dags.lib.services.file_path_service import FilePathService
from airflow.exceptions import AirflowSkipException
import gzip
import shutil
from dags.nginx_logs.row_transformer import RowTransformer
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from airflow.hooks.base import BaseHook

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)

file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_FILE_PATH: [],
    }
)

dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}


def clear_temp_data():
    LoggerService.logger.info(f'Env: {ENV}')
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_EXTRACTED_TABLE)
    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)

def extract_and_transform():

    directory_path = config.get_setting(dag_config.KEY_PATH)
    file_path_source = directory_path + 'output.log'
    if not os.path.exists(file_path_source):
        LoggerService.logger.warn(f'Source file not found {file_path_source}')
    else:
        os.rename(file_path_source, directory_path + dag_config.FILE_NAME_TEMPLATE.format(date=date_time_to_str(datetime.now(), '%Y%m%d-%H%M%S')))
    now_timestamp = datetime.now().timestamp()
    files = []
    for file in glob(f'{directory_path}logs_*.log'):
        LoggerService.logger.info(file)
        file_stats = os.stat(file)
        LoggerService.logger.info(f'now: {now_timestamp}, mtime: {file_stats.st_mtime}')
        if now_timestamp - file_stats.st_mtime >= dag_config.PROCESSING_DELAY_SECONDS:
            files.append(file)

    if len(files) == 0:
        raise Exception('No files for processing')

    LoggerService.logger.info(files)

    writer = None
    file_path_transformed = file_paths.get_result_path(dag_config.TMP_FILE_PATH)
    with open(file_path_transformed, mode='w') as file_transformed:
        for file in files:
            with open(file) as file_raw:
                for row in file_raw:
                    row = RowTransformer.process(row, file)
                    if row is None:
                        continue
                    if writer is None:
                        writer = csv.DictWriter(file_transformed, delimiter=',', lineterminator='\n', fieldnames=row.keys())
                        writer.writeheader()
                    writer.writerow(row)

    path_files = file_paths.get_result_path(dag_config.TMP_FILE_FILES_PATH)
    with open(path_files, 'w') as file:
        for file_path in files:
            file.write(file_path + '\n')


def move():
    from_table = dag_config.CH_EXTRACTED_TABLE
    to_table = dag_config.CH_TABLE
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    sql = f'INSERT INTO {to_table} SELECT * FROM {conn_etl.schema}.{from_table}'
    LoggerService.logger.info(sql)
    ch.execute(sql)

def cleanup():
    path_files = file_paths.get_result_path(dag_config.TMP_FILE_FILES_PATH)
    with open(path_files, mode='r', encoding='utf-8') as file:
        for line in file:
            file_path = line.rstrip()
            if os.path.exists(file_path):
                LoggerService.logger.info(f'Removing {file_path}')
                os.remove(file_path)


with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id=f'clear_temp_data',
        python_callable=clear_temp_data,
    )

    extract_and_transform_task = PythonOperator(
        task_id=f'extract_and_transform',
        python_callable=extract_and_transform,
    )

    load_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_FILE_PATH),
        table_name=dag_config.CH_EXTRACTED_TABLE,
        data_type=CSV_DATA_TYPE,
        delimiter=',',
        task_id=f'load'
    )

    move_task = PythonOperator(
        task_id=f'move',
        python_callable=move,
    )

    cleanup_task = PythonOperator(
        task_id=f'cleanup',
        python_callable=cleanup,
    )

    clear_temp_data_task >> extract_and_transform_task >> load_task >> move_task >> cleanup_task

if __name__ == '__main__':
    dag.cli()
