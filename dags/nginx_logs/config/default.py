from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load Nginx logs',
}

SETTINGS = {
    'path': '/tmp/',
}

KEY_PATH = 'path'

CH_EXTRACTED_TABLE = 'nginx_logs_extracted'
CH_TABLE = 'nginx_logs'

PROCESSING_DELAY_SECONDS = 180
FILE_NAME_TEMPLATE = 'logs_{date}.log'

TMP_FILE_PATH = 'nginx_logs_[connection].csv'
TMP_FILE_FILES_PATH = 'nginx_logs_files_[connection].csv'

ROW_MAPPING = {
    'stream': {'ch_field': 'stream', 'ch_type': 'String'},
    '_p': {'ch_field': 'fluentbit_status', 'ch_type': 'String'},
    # “time”:“2024-06-26T17:36:41+00:00"
    'time': {'ch_field': 'time', 'ch_type': 'DateTime', 'format': '%Y-%m-%dT%H:%M:%S%z'},
    'remote_addr': {'ch_field': 'remote_addr', 'ch_type': 'String'},
    'request_id': {'ch_field': 'request_id', 'ch_type': 'String'},
    'remote_user': {'ch_field': 'remote_user', 'ch_type': 'String'},
    'bytes_sent': {'ch_field': 'bytes_sent', 'ch_type': 'UInt32'},
    'body_bytes_sent': {'ch_field': 'body_bytes_sent', 'ch_type': 'UInt32'},
    'request_time': {'ch_field': 'request_time', 'ch_type': 'Decimal'},
    'upstream_time': {'ch_field': 'upstream_time', 'ch_type': 'Decimal'},
    'status': {'ch_field': 'status', 'ch_type': 'UInt16'},
    'vhost': {'ch_field': 'vhost', 'ch_type': 'String'},
    'request_proto': {'ch_field': 'request_proto', 'ch_type': 'String'},
    'path': {'ch_field': 'path', 'ch_type': 'String'},
    'request_query': {'ch_field': 'request_query', 'ch_type': 'String'},
    'request_length': {'ch_field': 'request_length', 'ch_type': 'UInt32'},
    'method': {'ch_field': 'method', 'ch_type': 'String'},
    'http_referrer': {'ch_field': 'http_referrer', 'ch_type': 'String'},
    'http_user_agent': {'ch_field': 'http_user_agent', 'ch_type': 'String'},
    'ingress_name': {'ch_field': 'ingress_name', 'ch_type': 'String'},
    'proxy_upstream_name': {'ch_field': 'proxy_upstream_name', 'ch_type': 'String'},
    'upstream_status': {'ch_field': 'upstream_status', 'ch_type': 'UInt16'},
    'host': {'ch_field': 'host', 'ch_type': 'String'},

}