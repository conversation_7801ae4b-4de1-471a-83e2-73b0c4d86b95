import json
import dags.nginx_logs.config.default as dag_config
from datetime import datetime
from dags.lib.services.logger_service import LoggerService
from dags.lib.helpers.date_helper import str_to_date_time, date_time_to_str
from json.decoder import JSONDecodeError


class RowTransformer:

    @staticmethod
    def __check_value(param_config, value):
        if value is None:
            return
        if param_config['ch_type'] == 'DateTime':
            datetime.strptime(value, param_config['format'])
        elif param_config['ch_type'] in ['UInt8', 'UInt16', 'UInt32', 'UInt64']:
            try:
                value = int(value)
            except ValueError as e:
                LoggerService.logger.error(f'param {param_config["ch_field"]}: {value}')
                raise e
        elif param_config['ch_type'] in ['Decimal']:
            try:
                value = float(value)
            except ValueError as e:
                LoggerService.logger.error(f'param {param_config["ch_field"]}: {value}')
                raise e
        return None

    @staticmethod
    def process(row: str, filename: str):
        transformed = {
            '_file': filename
        }
        try:
            row = json.loads(row)
        except JSONDecodeError as e:
            LoggerService.logger.info(row)
            raise e
        if row['stream'] == 'stderr':
            return None
        if 'host' not in row:
            return None
        for param in row:
            param_config = dag_config.ROW_MAPPING[param]
            param_name = param_config['ch_field']
            value = row[param]
            RowTransformer.__check_value(param_config, value)

            if param_name == 'time':
                value = date_time_to_str(datetime.strptime(value, param_config['format']))
            transformed[param_config['ch_field']] = value

        return transformed
