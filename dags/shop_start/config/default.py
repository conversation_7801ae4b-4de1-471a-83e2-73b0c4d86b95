from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load shop start information',
}

SETTINGS = {}

CH_SHOP_START_EXTRACTED_TABLE = 'mysql_shop_start_extracted'
CH_SHOP_START_TABLE = 'shop_start'

MYSQL_SHOP_START_TABLE = 'shop_start'

TMP_SHOP_START_FILE_PATH = 'shop_start_[connection].parquet'

TMP_SHOP_START_EXTRACT_STATE_FILE_PATH = 'shop_start_es_[connection].csv'
