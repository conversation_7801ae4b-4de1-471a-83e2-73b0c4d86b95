import os
import pandas as pd
import csv
from typing import Dict
from airflow import DAG
from airflow.operators.python import PythonOperator
from dags.lib.services.clickhouse_service import ClickhouseService, PARQUET_DATA_TYPE, CSV_DATA_TYPE
from dags.lib.services.mysql_service import MySQLService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.file_path_service import FilePathService
from airflow.operators.empty import EmptyOperator
from airflow.hooks.base import BaseHook
from dags.shop_start.shop_start_row_transformer import ShopStartRowTransformer
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
import dags.shop_start.config.default as dag_config
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_SHOP_START_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_MASTERS),
        dag_config.TMP_SHOP_START_EXTRACT_STATE_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_MASTERS)
    }
)


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_SHOP_START_EXTRACTED_TABLE)

    for path in file_paths.get_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def stream_shop_start(mysql_connection_config: Dict):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    extract_state = ExtractStateService(ch)
    max_id = extract_state.get_last_value(mysql_connection_config['id'], dag_config.MYSQL_SHOP_START_TABLE, 'id')
    sql = f'''
    SELECT 
        id as _id,
        user_id,
        start_time,
        platform_type,
        app_type,
        shop_type,
        segment_id
    FROM {dag_config.MYSQL_SHOP_START_TABLE} 
    WHERE id > {max_id}'''

    ms = MySQLService(mysql_connection_config['id'])
    LoggerService.logger.info(sql)
    for row in ms.stream(sql):
        yield row


def extract_shop_start(mysql_connection_config: Dict):
    LoggerService.start_extracting(
        mysql_connection_config['platform'],
        mysql_connection_config['id'],
        dag_config.MYSQL_SHOP_START_TABLE
    )

    file_path = file_paths.get_path(mysql_connection_config['id'])
    path_state = file_paths.get_path(mysql_connection_config['id'], dag_config.TMP_SHOP_START_EXTRACT_STATE_FILE_PATH)
    path_state_header = file_paths.get_header_path(dag_config.TMP_SHOP_START_EXTRACT_STATE_FILE_PATH)

    counter = 0
    max_id = 0
    shop_start_data = []
    for row in stream_shop_start(mysql_connection_config):
        shop_start_data.append(ShopStartRowTransformer.process(row, mysql_connection_config))
        max_id = max(max_id, row['_id'])
        counter += 1

    data_frame = pd.DataFrame(shop_start_data)
    data_frame.to_parquet(file_path)

    if counter == 0:
        return

    state = {
        'connection_id': mysql_connection_config['id'],
        'source_name': dag_config.MYSQL_SHOP_START_TABLE,
        'key_name': 'id',
        'last_value_int': max_id
    }

    if not os.path.exists(path_state_header):
        with open(path_state_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, state.keys(), delimiter=';')
            writer.writeheader()
    with open(path_state, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, state.keys(), delimiter=';')
        writer.writerow(state)

    LoggerService.finish_extracting(counter)


def move_shop_start():
    try:
        from_table = dag_config.CH_SHOP_START_EXTRACTED_TABLE
        to_table = dag_config.CH_SHOP_START_TABLE
        LoggerService.start_move_data(from_table, to_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {to_table} SELECT * FROM {conn_etl.schema}.{from_table}')
    except Exception as e:
        LoggerService.error_move_data(from_table, to_table, str(e))
        raise


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    extract_shop_start_tasks = [
        PythonOperator(
            task_id=f'extract_shop_start_{row["id"]}',
            python_callable=extract_shop_start,
            op_kwargs={
                'mysql_connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_MASTERS)
    ]

    start_load_shop_start_tasks = EmptyOperator(
        task_id='start_load_shop_start'
    )

    load_shop_start_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(row['id']),
            table_name=dag_config.CH_SHOP_START_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'load_shop_start_{row["id"]}'
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_MASTERS)
    ]

    move_shop_start_extracted_table_task = PythonOperator(
        task_id='move_shop_start',
        python_callable=move_shop_start
    )

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_SHOP_START_EXTRACT_STATE_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MYSQL_MASTERS)
    )

    load_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_SHOP_START_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='load_extract_state'
    )

    clear_temp_data_task >> extract_shop_start_tasks >> start_load_shop_start_tasks \
        >> load_shop_start_tasks >> move_shop_start_extracted_table_task >> concat_extract_state_files_task \
        >> load_extract_state_task

if __name__ == '__main__':
    dag.cli()
