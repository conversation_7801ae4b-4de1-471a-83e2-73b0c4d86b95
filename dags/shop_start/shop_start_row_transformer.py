from typing import Dict
from datetime import datetime
from dags.lib.services.platform_service import PlatformService
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data


class ShopStartRowTransformer:
    @staticmethod
    def process(self: Dict, connection_config: Dict) -> Dict:
        self['_connection_id'] = connection_config['id']
        self['platform_type'] = PlatformService.get_name(self['platform_type'])

        try:
            self['start_time'] = get_converted_value(val=self['start_time'], type_val=datetime)
        except Exception as e:
            self['start_time'] = get_default_value(type_val=datetime)

        return prepare_parquet_data(self)
