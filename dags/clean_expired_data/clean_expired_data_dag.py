from airflow import DAG
from airflow.operators.python import PythonOperator
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.clean_data_tables_service import CleanDataTablesService

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()


def clear_expired_data():
    clean_expired_data_service = CleanDataTablesService(ENV, DAG_ID)
    LoggerService.logger.info('Start cleaning expired data from ClickHouse')
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    ch_etl = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    for query_params in clean_expired_data_service.get_clean_queries():
        conn = ch if not query_params[clean_expired_data_service.USE_ETL_DATABASE] else ch_etl
        query = query_params[clean_expired_data_service.QUERY_FIELD]
        LoggerService.logger.info(f'Execute query: {query}')
        conn.execute(query)
    LoggerService.logger.info('Finish cleaning expired data from ClickHouse')


config = ConfigService(ENV, DAG_ID)
dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clean_expired_data_clickhouse_task = PythonOperator(
        task_id='clean_expired_data_clickhouse',
        python_callable=clear_expired_data
    )

    clean_expired_data_clickhouse_task

if __name__ == '__main__':
    dag.cli()
