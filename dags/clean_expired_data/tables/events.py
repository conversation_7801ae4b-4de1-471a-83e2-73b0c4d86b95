from dags.lib.services.clean_data_tables_service import CleanDataTablesService
from dags.lib.services.environment_service import EnvironmentService

CLEAN_CONDITIONS = {
    EnvironmentService.DEVELOP: [
        {
            CleanDataTablesService.TTL_TABLE_FIELD_KEY: 'date',
            CleanDataTablesService.TTL_DATE_FORMAT_KEY: CleanDataTablesService.TTL_DATE_TIME_FORMAT,
            CleanDataTablesService.TTL_CONDITIONS_KEY: 14
        }
    ],
    EnvironmentService.PRODUCTION: [
        # {
        #     'eventtype': ["'shop_open'", "'shop_click'", "'object_upgrade'", "'quest_complete'", "'quest_skip'",
        #                   "'quest_boost'", "'batlpas_award'", "'level_complete'", "'tutorial_step_start'",
        #                   "'tutorial_step_finish'", "'chest_open'", "'chest_upgrade'", "'chest_boost'",
        #                   "'rewarded_ad_get'", "'factory_upgrade'", "'elf_upgrade'", "'minigame_start'",
        #                   "'minigame_purchase_elf'", "'minigame_merge_elf'", "'minigame_upgrade_elf'",
        #                   "'minigame_finish'", "'game_restart'", "'minigame_pause'", "'minigame_pick_elf'",
        #                   "'loading_finish'", "'coop_latency'", "'disconnect_coop'", "'error_window'"],
        #     CleanDataTablesService.TTL_TABLE_FIELD_KEY: 'date',
        #     CleanDataTablesService.TTL_DATE_FORMAT_KEY: CleanDataTablesService.TTL_DATE_TIME_FORMAT,
        #     CleanDataTablesService.TTL_CONDITIONS_KEY: 16 * 30
        # },
        # {
        #     'eventtype': ["'promo_open'", "'offers_click'", "'card_get'", "'workshop_collection'", "'workshop_request'",
        #                   "'workshop_exchange'", "'friend_visit'", "'achievement'", "'terms_of_use_show'",
        #                   "'terms_of_use_accept'", "'rate_us_show'", "'rate_us_store_transition'", "'fullscreen'",
        #                   "'music'", "'sound'", "'animation'", "'wheel'", "'wheel_gold'", "'clan_create'",
        #                   "'clan_join'", "'clan_leave'", "'clan_request'", "'clan_proposal'", "'clan_message'",
        #                   "'clan_change_language'", "'clan_search'", "'toy_purchase'", "'glades_compare'",
        #                   "'glades_upgrade'", "'object_select'", "'object_merge'", "'object_change_position'",
        #                   "'object_gather'", "'object_activation'", "'object_bought'", "'basket_gather'",
        #                   "'merge_level_upgrade'", "'likes_upgrade'", "'performance'", "'minigame_performance'",
        #                   "'environment'", "'server_response'", "'waiting_screen_shown'", "'content_loading_finish'"],
        #     CleanDataTablesService.TTL_TABLE_FIELD_KEY: 'date',
        #     CleanDataTablesService.TTL_DATE_FORMAT_KEY: CleanDataTablesService.TTL_DATE_TIME_FORMAT,
        #     CleanDataTablesService.TTL_CONDITIONS_KEY: 12 * 30
        # },
        # {
        #     'eventtype': ["'object_open'", "'object_close'", "'multiplayer_run_step'", "'minigame_wave_finish'"],
        #     CleanDataTablesService.TTL_TABLE_FIELD_KEY: 'date',
        #     CleanDataTablesService.TTL_DATE_FORMAT_KEY: CleanDataTablesService.TTL_DATE_TIME_FORMAT,
        #     CleanDataTablesService.TTL_CONDITIONS_KEY: 3 * 30
        # }
    ]
}
