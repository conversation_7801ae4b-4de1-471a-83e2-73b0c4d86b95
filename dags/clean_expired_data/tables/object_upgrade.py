from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.clean_data_tables_service import CleanDataTablesService

CLEAN_CONDITIONS = {
    EnvironmentService.DEVELOP: [
        {
            CleanDataTablesService.TTL_TABLE_FIELD_KEY: 'date',
            CleanDataTablesService.TTL_DATE_FORMAT_KEY: CleanDataTablesService.TTL_DATE_TIME_FORMAT,
            CleanDataTablesService.TTL_CONDITIONS_KEY: 14
        }
    ],
    EnvironmentService.PRODUCTION: []
}
