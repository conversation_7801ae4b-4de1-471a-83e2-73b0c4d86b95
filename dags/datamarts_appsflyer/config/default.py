from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load appsflyer data',
}

SETTINGS = {
    'bucket_name': 'af-datalocker-progameslab',
    'bucket_cohort_user_acquisition_path': 'f0ac-acc-yxAoxWWV-f0ac/progameslab-datalocker/t=cohort_user_acquisition/dt={date}/app_id={app_id}',
    'applications': [
        # days_per_run - данные за сколько дней выгружаем за один запуск DAG
        {
            'app_id': 'id6446766326',
            'app_id_human': 'Goblins Wood iOS',
            'days_per_run': 3,
            'date_start': '2023-02-01'
        },
        {
            'app_id': 'idle.goblins.wood.tycoon',
            'app_id_human': 'Goblins Wood Android',
            'days_per_run': 3,
            'date_start': '2023-02-01'
        },
        {
            'app_id': 'com.progameslab.magic.seasons2024.farm.match.collect',
            'app_id_human': 'Magic Seasons 2024 Android',
            'days_per_run': 1,
            'date_start': '2023-10-01'
        },
        {
            'app_id': 'id6463635391',
            'app_id_human': 'Magic Seasons 2024 iOS',
            'days_per_run': 1,
            'date_start': '2023-10-01'
        },
        {
            'app_id': 'com.progameslab.tile.master.triple.puzzle.match.travel.farm.adventure',
            'app_id_human': 'Magic Seasons 2025 Android',
            'days_per_run': 3,
            'date_start': '2024-07-23'
        },
        {
            'app_id': 'id6504757972',
            'app_id_human': 'Magic Seasons 2025 iOS',
            'days_per_run': 3,
            'date_start': '2024-07-23'
        },
        {
            'app_id': 'com.progameslab.gold.zombies.titans.idle.tycoon.merge.action.rpg.clicker',
            'app_id_human': 'Gold and Zombies Android',
            'days_per_run': 1,
            'date_start': '2025-01-10'
        },
    ]
}

BUCKET_COHORT_USER_ACQUISITION_PATH_KEY = 'bucket_cohort_user_acquisition_path'
BUCKET_NAME_KEY = 'bucket_name'
APPLICATIONS_KEY = 'applications'
OBJECTS_LIST_PAGE_SIZE = 1024
# Выгружаем из S3 только файлы, которые созданы час назад минимум. Чтобы защититься от кейса, когда Appsflyer
# еще продолжает создавать файлы в папке за очередной день
# Также опираемся здесь на то, что файлы за день появляются в папке с разницей в секунды. Час - перестраховка с
# запасом
LAST_MODIFIED_GAP_SECONDS = 3600

CH_COHORT_USER_ACQUISITION_TABLE = 'appsflyer_cohort_user_acquisition'
CH_COHORT_USER_ACQUISITION_EXTRACTED_TABLE = 'appsflyer_cohort_user_acquisition_extracted'

TMP_COHORT_USER_ACQUISITION_EXTRACT_STATE_FILE_PATH = 'datamarts_appsflyer_cohort_acquisition_es_[connection].csv'



