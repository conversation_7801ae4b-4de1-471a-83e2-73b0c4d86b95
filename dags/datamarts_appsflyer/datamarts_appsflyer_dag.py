import pandas as pd
import io
import os
from typing import Dict
from datetime import datetime, timedelta, timezone
from airflow import DAG
import csv
from airflow.exceptions import AirflowSkipException
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.hooks.base import BaseHook
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.file_path_service import FilePathService
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator, CSV_DATA_TYPE
from dags.lib.helpers.date_helper import str_to_date_time, get_current_date
from dags.datamarts_appsflyer.cohort_user_acquisition_row_transformer import CohortUserAcquisitionRowTransformer
import dags.datamarts_appsflyer.config.default as dag_config
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_COHORT_USER_ACQUISITION_EXTRACT_STATE_FILE_PATH: [],
    }
)

def clear_temp_data():
    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def get_dates(application_config: Dict, connection_id: str) -> (datetime, datetime, list):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    extract_state = ExtractStateService(ch)
    date_start = str_to_date_time(application_config['date_start']).date()

    date_max = extract_state.get_last_value(
        connection_id,
        application_config['app_id'],
        'date',
        ExtractStateService.DATE_TYPE
    ).date()
    date_max_saved = date_max

    date_min = extract_state.get_last_value(
        connection_id,
        application_config['app_id'],
        'date_min',
        ExtractStateService.DATE_TYPE
    ).date()
    date_min_saved = date_min

    # За текущий день нет смысла пытаться выгружать, папка появится лишь на следующий день
    date_yesterday = get_current_date() - timedelta(days=1)
    LoggerService.logger.info(f'Change date max {date_max} to')
    if date_max < date_start:
        date_max = date_yesterday - timedelta(days=(application_config['days_per_run'] - 1))
    else:
        date_max += timedelta(days=1)
    LoggerService.logger.info(f'{date_max}')

    # Кроме свежих данных пытаемся также выгружать исторические, поэтому две даты - min и max, от которых
    # происходят расчеты в будущее (чаще всего это будет 1 ближайший день) и прошлое
    dates = []
    if date_max_saved < date_yesterday:
        for _ in range(application_config['days_per_run']):
            LoggerService.logger.info(f'Append to future {date_max}')
            dates.append(date_max)
            if date_max == date_yesterday:
                break
            date_max += timedelta(days=1)

    # if len(dates) < application_config['days_per_run'] and date_min > date_start:
    #     for _ in range(application_config['days_per_run'] - len(dates)):
    #         date_min -= timedelta(days=1)
    #         LoggerService.logger.info(f'Append to past {date_min}')
    #         dates.append(date_min)
    # Кейс самого первого запуска
    # if date_min_saved < date_start:
    #     date_min_saved = date_max + timedelta(days=1)
    LoggerService.logger.info(f'{application_config["app_id_human"]} dates:')
    LoggerService.logger.info(dates)
    return (date_max_saved, date_min_saved, dates)


def load_cohort_user_acquisition_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    # TODO: Устаревшая логика, где в одном методе в цикле выгружается сразу все доступное, поэтому приходится очищать
    # табилцу здесь, чтобы повторы не создавали дубли в extracted-таблице
    ch.truncate(dag_config.CH_COHORT_USER_ACQUISITION_EXTRACTED_TABLE)

    connection_id = config.get_connection(ConfigService.CONN_AWS_S3)
    s3_hook = S3Hook(aws_conn_id=connection_id)
    bucket_name = config.get_setting(dag_config.BUCKET_NAME_KEY)
    bucket_path = config.get_setting(dag_config.BUCKET_COHORT_USER_ACQUISITION_PATH_KEY)

    extract_state = []
    now = datetime.now().replace(tzinfo=timezone.utc)
    for application_config in config.get_setting(dag_config.APPLICATIONS_KEY):

        (date_max_saved, date_min_saved, dates) = get_dates(application_config, connection_id)
        date_max = date_max_saved
        date_min = date_min_saved
        for date in dates:
            path = bucket_path.format(date=date.strftime('%Y-%m-%d'), app_id=application_config['app_id'])
            parts = s3_hook.list_keys(bucket_name, prefix=path, page_size=dag_config.OBJECTS_LIST_PAGE_SIZE)
            LoggerService.logger.info(f'Load directory {path}')
            skip = False
            for part in parts:
                buffer = io.BytesIO()
                obj = s3_hook.get_key(bucket_name=bucket_name, key=part)
                if (now - obj.last_modified).total_seconds() < dag_config.LAST_MODIFIED_GAP_SECONDS:
                    skip = True
                    break
                obj.download_fileobj(buffer)
                df = CohortUserAcquisitionRowTransformer.process(pd.read_parquet(buffer), application_config, part)
                ch.insert_dataframe(df=df, table_name=dag_config.CH_COHORT_USER_ACQUISITION_EXTRACTED_TABLE)
                LoggerService.logger.info(f'Data from file {part} was loaded, {len(df.index)} rows')
            if (not skip) and (len(parts) > 0):
                date_max = max(date_max, date)
                date_min = min(date_min, date)
        if date_max > date_max_saved:
            extract_state.append({
                'connection_id': connection_id,
                'source_name': application_config['app_id'],
                'key_name': 'date',
                'last_value_date': date_max
            })
        if date_min < date_min_saved:
            extract_state.append({
                'connection_id': connection_id,
                'source_name': application_config['app_id'],
                'key_name': 'date_min',
                'last_value_date': date_min
            })

    if len(extract_state) > 0:
        file_path_state = file_paths.get_result_path(dag_config.TMP_COHORT_USER_ACQUISITION_EXTRACT_STATE_FILE_PATH)
        with open(file_path_state, 'w', newline='', encoding='utf-8') as file:
            header = extract_state[0].keys()
            writer = csv.DictWriter(file, header, delimiter=';')
            writer.writeheader()
            writer.writerows(extract_state)
    else:
        raise AirflowSkipException


def move_data(etl_table: str, main_table: str):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    ch.execute(f'INSERT INTO {main_table} SELECT * FROM {conn_etl.schema}.{etl_table}')


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_files_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    load_cohort_user_acquisition_task = PythonOperator(
        task_id=f'load_cohort_user_acquisition',
        python_callable=load_cohort_user_acquisition_data
    )

    move_cohort_user_acquisition_data_task = PythonOperator(
        task_id='move_cohort_user_acquisition',
        python_callable=move_data,
        op_kwargs={
            'etl_table': dag_config.CH_COHORT_USER_ACQUISITION_EXTRACTED_TABLE,
            'main_table': dag_config.CH_COHORT_USER_ACQUISITION_TABLE
        }
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_COHORT_USER_ACQUISITION_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_es'
    )

    clear_temp_files_task >> load_cohort_user_acquisition_task >> move_cohort_user_acquisition_data_task \
    >> import_extract_state_task

if __name__ == '__main__':
    dag.cli()
