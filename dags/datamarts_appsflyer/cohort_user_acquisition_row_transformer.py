import pandas as pd
from datetime import datetime
from typing import Dict


class CohortUserAcquisitionRowTransformer:
    @staticmethod
    def process(df: pd.DataFrame, application_config: Dict, file_name: str) -> pd.DataFrame:
        df['event_date'] = df['event_date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d').date())
        df['unique_users'] = df['unique_users'].apply(lambda x: int(x) if x != '' else None)
        df['event_count'] = df['event_count'].apply(lambda x: int(x) if x != '' else None)
        df['revenue_usd'] = df['revenue_usd'].apply(lambda x: float(x) if x != '' else None)
        df['revenue_selected_currency'] = df['revenue_selected_currency'].apply(lambda x: float(x) if x != '' else None)
        df['days_post_attribution'] = df['days_post_attribution'].apply(lambda x: int(x) if x != '' else None)
        df['conversion_date'] = df['conversion_date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d').date())
        df['app_id'] = application_config['app_id']
        df['app_id_human'] = application_config['app_id_human']
        df['filename'] = file_name
        return df
