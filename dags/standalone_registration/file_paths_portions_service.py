import os
from glob import glob
import math
from datetime import datetime
import dags.standalone_registration.config.default as dag_config
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.file_path_service import FilePathService


class FilePathsPortionsService:

    def __init__(self, path_service: FilePathService, glob_template, portions_count):
        self.path_serivce = path_service
        self.glob_template = glob_template
        self.portions_count = portions_count

    def make_portions(self, platform):
        files = []
        now_timestamp = datetime.now().timestamp()
        count = 0
        path = self.glob_template.replace('[platform]', platform)
        LoggerService.logger.info(f'Searching files in {path}...')
        for filename in glob(path):
            LoggerService.logger.info(filename)
            file_stats = os.stat(filename)
            if now_timestamp - file_stats.st_mtime >= dag_config.FILE_MTIME_SECONDS_PASSED:
                files.append(f'{filename}|{file_stats.st_mtime}\n')
            count += 1
            if count >= dag_config.MAX_FILES_FOR_PROCESSING_COUNT:
                break
        portion_size = math.ceil(len(files) / self.portions_count)
        portion_number = 1
        if portion_size > 0:
            for i in range(0, len(files), portion_size):
                path = self.path_serivce.get_path(f'{platform}_{portion_number}', dag_config.TMP_FILES_FILE_PATH)
                with open(path, 'w') as file:
                    file.writelines(files[i:i + portion_size])
                portion_number += 1

    def get_portion(self, platform, portion_number):
        path = self.path_serivce.get_path(f'{platform}_{portion_number}', dag_config.TMP_FILES_FILE_PATH)
        files = []
        if not os.path.exists(path):
            return files
        with open(path, 'r') as file:
            for line in file:
                files.append(line.split('|'))
        return files

    def stream_all(self, platform):
        for portion_number in range(1, self.portions_count + 1):
            path = self.path_serivce.get_path(f'{platform}_{portion_number}', dag_config.TMP_FILES_FILE_PATH)
            LoggerService.logger.info(f'Reading portion file {path}')
            if not os.path.exists(path):
                LoggerService.logger.info(f'Portion file {path} do not exist')
                continue
            with open(path, 'r') as file:
                for line in file:
                    parts = line.split('|')
                    yield parts[0], float(parts[1].rstrip())
