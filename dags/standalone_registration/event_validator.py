import dags.standalone_registration.config.default as dag_config
from datetime import datetime


class EventValidator:
    ERROR_BAD_JSON = 'bad json'
    ERROR_UNKNOWN_EVENT_TYPE = 'unknown type'
    ERROR_NO_PARAM = 'no param'
    ERROR_NO_BASE_PARAM = 'no base param'
    ERROR_BAD_VALUE = 'bad value'

    RESULT_OK = 0
    RESULT_BAD_BASE_PARAMS = 1
    RESULT_BAD_PARAMS = 2

    EVENT_TYPES = dag_config.EVENTS.keys()

    @staticmethod
    def __check_value(param_name, param_config, value):
        if value is None:
            return
        if param_config['type'] == 'DateTime':
            try:
                datetime.strptime(value, param_config['validation']['format'])
            except ValueError:
                return {
                    'type': EventValidator.ERROR_BAD_VALUE,
                    'details': {
                        'name': param_name
                    }
                }
        elif param_config['type'] in ['UInt8', 'UInt16', 'UInt32', 'UInt64']:
            # TODO: Добавить проверку на диапазоны допустимых значений
            try:
                value = int(value)
            except ValueError:
                return {
                    'type': EventValidator.ERROR_BAD_VALUE,
                    'details': {
                        'name': param_name
                    }
                }
        return None

    @staticmethod
    def validate(event):
        errors = []
        result = EventValidator.RESULT_OK
        fields_config = dag_config.EVENTS_PARAMS
        for param in dag_config.EVENTS_BASE_PARAMS:
            if param not in event:
                result = EventValidator.RESULT_BAD_BASE_PARAMS
                errors.append({
                    'type': EventValidator.ERROR_NO_BASE_PARAM,
                    'details': {
                        'name': param
                    }
                })
                continue
            error = EventValidator.__check_value(param, fields_config[param], event[param])
            if error is not None:
                result = EventValidator.RESULT_BAD_BASE_PARAMS
                errors.append(error)

        if ('event' in event) and (event['event'] not in EventValidator.EVENT_TYPES):
            result = EventValidator.RESULT_BAD_BASE_PARAMS
            errors.append({
                'type': EventValidator.ERROR_UNKNOWN_EVENT_TYPE,
                'details': {
                    'name': event['event']
                }
            })
            return result, errors

        if  'event' not in event:
            return result, errors

        for param in dag_config.EVENTS[event['event']]:
            if param not in event:
                if result == EventValidator.RESULT_OK:
                    result = EventValidator.RESULT_BAD_PARAMS
                errors.append({
                    'type': EventValidator.ERROR_NO_PARAM,
                    'details': {
                        'name': param
                    }
                })
                continue
            field_config = fields_config[param] if 'ch_field' in fields_config[param] else None
            if field_config is None:
                # Значит одному и тому же названию параметра на клиенте соответствует несколько колонок в базе
                for config in fields_config[param]:
                    if event['event'] in config['events']:
                        field_config = config
                        break
            error = EventValidator.__check_value(param, field_config, event[param])
            if error is not None:
                if result == EventValidator.RESULT_OK:
                    result = EventValidator.RESULT_BAD_PARAMS
                errors.append(error)
        return result, errors
