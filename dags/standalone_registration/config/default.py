from datetime import timedelta
from dags.lib.services.platform_service import PlatformService

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load standalone registration events from client',
    'concurrency': 6,
}

SETTINGS = {
    'events_files_folder': '/tmp/events',
}

FILE_PATHS_PORTIONS_COUNT = 6
FILE_MTIME_SECONDS_PASSED = 180

BACKUP_FOLDER = 'backup'
BACKUP_RESTORE_FOLDER = 'restore'
BACKUP_SECONDS = 259200 # 3 дня

MAX_FILES_FOR_PROCESSING_COUNT = 10000

CH_EVENTS_EXTRACTED_TABLE = 'standalone_registration_extracted'
CH_EVENTS_TABLE = 'standalone_registration'
CH_ERRORS_TABLE = 'standalone_registration_errors'

# На самом деле events можно брать в шаблон как имя ДАГа, чтобы не было возможности человеческой ошибки
TMP_EVENTS_FILE_PATH = 'standalone_registration_[connection].parquet'
TMP_ERRORS_FILE_PATH = 'standalone_registration_errors_[connection].parquet'
TMP_FILES_FILE_PATH = 'standalone_registration_files_[connection].txt'

EVENTS = {
    'web_open': [],
    'login_open': [],
    'login_success': [],
    'login_close': [],
    'language_click': [],
    'language_change': [],
    'get_user_id': [],
    'authorization_error': [],
    'guest_try': [],
    'guest_success': [],
}

EVENTS_BASE_PARAMS = ['pregame_user_id', 'event', 'fluent_time', 'language', 'referrer_data', 'extra', 'browser_name',
                      'app_type']

# {"pregame_user_id":"b8e607a0-672d-4220-a19e-c26ed4e3811f","date":1699979562866,"language":"ru",
# "referrer_data":"","browser_name":"Chrome 119","app_type":5,"extra":1,"n":6,"session":"",
# "event":"login_open","fluent_time":"2023-11-14 16:32:43 +0000"}

EVENTS_PARAMS = {
    # common
    'pregame_user_id': {'ch_field': 'pregame_user_id', 'type': 'String', 'ptype': str},
    'fluent_time': {'ch_field': 'date', 'type': 'DateTime', 'ptype': str,
                    'validation': {'format': '%Y-%m-%d %H:%M:%S %z'}},
    'event': {'ch_field': 'eventtype', 'type': 'String', 'ptype': str},
    'language': {'ch_field': 'language', 'type': 'String', 'ptype': str}, # LowCardinality
    'referrer_data': {'ch_field': 'referrer_data', 'type': 'String', 'ptype': str},
    'extra': {'ch_field': 'extra', 'type': 'String', 'ptype': str},
    'browser_name': {'ch_field': 'browser_name', 'type': 'String', 'ptype': str}, # LowCardinality
    'app_type': {'ch_field': 'app_type', 'type': 'UInt8', 'ptype': int},
}
