from typing import Dict
import os
from airflow import DAG
from airflow.hooks.base import <PERSON><PERSON><PERSON>
from airflow.operators.python import Python<PERSON>perator
from dags.lib.services.mysql_service import MySQLService
from dags.lib.services.clickhouse_service import ClickhouseService, PARQUET_DATA_TYPE, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
import dags.clans.config.default as dag_config
from dags.clans.clans_row_transformer import ClansRowTransformer
from dags.clans.clans_document_transformer import ClansDocumentTransformer
from dags.clans.clans_document_splitter import ClansDocumentSplitter
from glob import glob
from dags.lib.services.file_path_service import FilePathService
import pandas as pd
from airflow.providers.mongo.hooks.mongo import Mongo<PERSON>ook
from dags.lib.services.extract_state_service import ExtractStateService
import csv
from dags.lib.operators.concat_csv_operator import Concat<PERSON><PERSON>perator
from dags.config.default import CH_EXTRACT_STATE_TABLE
from airflow.operators.empty import EmptyOperator

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_MYSQL_CLANS_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_MASTERS),
        dag_config.TMP_MONGO_CLANS_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS),
        dag_config.TMP_CLANS_MATCHMAKING_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS),
        dag_config.TMP_CLANS_MATCHMAKING_EXTRACT_STATE_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS),
    }
)


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_MYSQL_CLANS_EXTRACTED_TABLE)
    ch.truncate(dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE)
    ch.truncate(dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE)

    for path in file_paths.get_all_paths():
        if os.path.exists(path):
            LoggerService.logger.info(f'Removing {path}...')
            os.remove(path)


def stream_mysql_clans(connection_config: Dict):
    mysql_connection = connection_config['id']
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    max = ch.get_max_column_value(
        dag_config.CH_CLANS_TABLE,
        '_update_time_mysql',
        {'platform_type': connection_config['platform']},
        True
    )
    LoggerService.logger.info(f'Max timestamp: {max}')
    sql = f'''
        SELECT id AS clan_id, name, logo, description, user_level, member_count, status, locale AS language, 
            `rank`, install_time, remove_time, IF(transferred = 1, 1, 0) AS old_clan, user_id_head, moderator_count,
            update_time
        FROM {dag_config.MYSQL_CLANS_TABLE}  
        WHERE update_time > {max}
    '''
    ms = MySQLService(mysql_connection)
    for row in ms.stream(sql):
        yield row


def extract_mysql_clans(connection_config: Dict):
    counter = 0
    gen = stream_mysql_clans(connection_config)
    data = []
    for row in gen:
        data.append(ClansRowTransformer.process(row, connection_config))
        counter += 1

    file_path = file_paths.get_path(connection_config['id'], dag_config.TMP_MYSQL_CLANS_FILE_PATH)
    data_frame = pd.DataFrame(data)
    data_frame.to_parquet(file_path)

    # Писать статистику в базу
    LoggerService.finish_extracting(counter)

def load_mysql_clans():
    pass
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.execute(f'''
        INSERT INTO {dag_config.CH_MYSQL_CLANS_TABLE} (
            _connection_id, _update_time, platform_type, clan_id, name, logo, description, 
            user_level, member_count, status, old_clan, rank, install_time, remove_time, user_id_head, 
            moderator_count, language)
        SELECT _connection_id, update_time, platform_type, clan_id, name, logo, description, 
            user_level, member_count, status, old_clan, rank, install_time, remove_time, user_id_head, 
            moderator_count, language
        FROM {dag_config.CH_MYSQL_CLANS_EXTRACTED_TABLE}
    ''')

def stream_mongo_clans(connection_config: Dict):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    max = ch.get_max_column_value(
        dag_config.CH_CLANS_TABLE,
        '_update_time_mongo',
        {'_connection_id': connection_config['id'], 'platform_type': connection_config['platform']},
        True
    )
    LoggerService.logger.info(f'Max id is: {max}')
    connection = BaseHook.get_connection(connection_config['id'])
    documents_count = 0
    with MongoHook(connection_config['id']).get_conn() as mongo:
        collection = mongo.get_database(connection.schema).get_collection(dag_config.MONGO_CLANS_COLLECTION)
        with collection.find(
                {'$or': [
                    {'_lastUpdated': {'$exists': False}},
                    {'_lastUpdated': {'$gt': max}}
                ]},
                {k: 1 for k, v in dag_config.MONGO_COLLECTION_FIELDS.items()}
        ) as cursor:
            for document in cursor:
                yield document
                documents_count += 1
    LoggerService.logger.info(f'Documents count: {documents_count}')

def get_matchmaking_extract_state_key(platform: str):
    return f'init_time_{platform}'

def extract_mongo_clans(connection_config: Dict):
    data = []
    data_matchmaking = []

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    extract_state = ExtractStateService(ch)
    extract_state_key = get_matchmaking_extract_state_key(connection_config['platform'])
    max_init_time = extract_state.get_last_value(
        connection_config['id'],
        dag_config.MONGO_CLANS_COLLECTION,
        extract_state_key
    )
    LoggerService.logger.info(f'Max init time: {max_init_time}')

    gen = stream_mongo_clans(connection_config)
    for document in gen:
        document = ClansDocumentTransformer.process(document, connection_config)
        splitted = ClansDocumentSplitter.process(document, connection_config, max_init_time)
        LoggerService.logger.info(splitted)
        data.append(splitted[dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE])
        data_matchmaking.extend(splitted[dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE])

    file_path = file_paths.get_path(connection_config['id'], dag_config.TMP_MONGO_CLANS_FILE_PATH)
    data_frame = pd.DataFrame(data)
    data_frame.to_parquet(file_path)

    file_path = file_paths.get_path(connection_config['id'], dag_config.TMP_CLANS_MATCHMAKING_FILE_PATH)
    data_frame = pd.DataFrame(data_matchmaking)
    data_frame.to_parquet(file_path)

    if len(data_matchmaking) > 0:

        file_path_state = file_paths.get_path(
            connection_config['id'],
            dag_config.TMP_CLANS_MATCHMAKING_EXTRACT_STATE_FILE_PATH
        )
        file_path_state_header = file_paths.get_header_path(dag_config.TMP_CLANS_MATCHMAKING_EXTRACT_STATE_FILE_PATH)

        state = {
            'connection_id': connection_config['id'],
            'source_name': dag_config.MONGO_CLANS_COLLECTION,
            'key_name': extract_state_key,
            'last_value_int': data_frame['date'].max()
        }

        header = state.keys()
        if not os.path.exists(file_path_state_header):
            with open(file_path_state_header, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, header, delimiter=';')
                writer.writeheader()

        with open(file_path_state, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, header, delimiter=';')
            writer.writerow(state)

def join_clans():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    LoggerService.logger.info(dag_config.SQL_JOIN_CLANS.format(
        schema=conn_etl.schema,
        table_to=dag_config.CH_CLANS_TABLE,
        mysql_clans_table=dag_config.CH_MYSQL_CLANS_TABLE,
        mongo_clans_table=dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE
    ))
    ch.execute(dag_config.SQL_JOIN_CLANS.format(
        schema=conn_etl.schema,
        table_to=dag_config.CH_CLANS_TABLE,
        mysql_clans_table=dag_config.CH_MYSQL_CLANS_TABLE,
        mongo_clans_table=dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE
    ))

def join_clans_dynamics():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    conn_main = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    LoggerService.logger.info(dag_config.SQL_JOIN_CLANS.format(
        schema=conn_main.schema,
        table_to=dag_config.CH_CLANS_DYNAMICS_TABLE,
        mysql_clans_table=dag_config.CH_MYSQL_CLANS_TABLE,
        mongo_clans_table=dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE
    ))
    ch.execute(dag_config.SQL_JOIN_CLANS.format(
        schema=conn_main.schema,
        table_to=dag_config.CH_CLANS_DYNAMICS_TABLE,
        mysql_clans_table=dag_config.CH_MYSQL_CLANS_TABLE,
        mongo_clans_table=dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE
    ))

def move_clans_matchmaking():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.execute(f'INSERT INTO {dag_config.CH_CLANS_MATCHMAKING_TABLE} SELECT * FROM '
               f'{dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE}')



dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data)

    extract_mysql_clans_tasks = [
        PythonOperator(
            task_id=f'extract_mysql_clans_{row["id"]}',
            python_callable=extract_mysql_clans,
            op_kwargs={
                'connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_MASTERS)
    ]

    import_mysql_clans_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(row['id'], dag_config.TMP_MYSQL_CLANS_FILE_PATH),
            table_name=dag_config.CH_MYSQL_CLANS_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'import_mysql_clans_{row["id"]}'
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_MASTERS)
    ]

    load_mysql_clans_task = PythonOperator(
        task_id=f'load_mysql_clans',
        python_callable=load_mysql_clans,
    )

    extract_mongo_clans_tasks = [
        PythonOperator(
            task_id=f'extract_mongo_clans_{row["id"]}',
            python_callable=extract_mongo_clans,
            op_kwargs={
                'connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MONGO_SHARDS)
    ]

    import_mongo_clans_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(row['id'], dag_config.TMP_MONGO_CLANS_FILE_PATH),
            table_name=dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'import_mongo_clans_{row["id"]}'
        )
        for row in config.get_connections(ConfigService.CONN_MONGO_SHARDS)
    ]

    import_clans_matchmaking_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(row['id'], dag_config.TMP_CLANS_MATCHMAKING_FILE_PATH),
            table_name=dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'import_clans_matchmaking_{row["id"]}'
        )
        for row in config.get_connections(ConfigService.CONN_MONGO_SHARDS)
    ]

    join_clans_task = PythonOperator(
        task_id=f'join_clans',
        python_callable=join_clans,
    )

    join_clans_dynamics_task = PythonOperator(
        task_id=f'join_clans_dynamics',
        python_callable=join_clans_dynamics,
    )

    move_clans_matchmaking_task = PythonOperator(
        task_id='move_clans_matchmaking',
        python_callable=move_clans_matchmaking
    )

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_matchmaking_es_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_CLANS_MATCHMAKING_EXTRACT_STATE_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS)
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_CLANS_MATCHMAKING_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_es'
    )

    import_task = import_mysql_clans_tasks.pop(0)
    clear_temp_data_task >> extract_mysql_clans_tasks >> import_task
    for task in import_mysql_clans_tasks:
        import_task >> task
        import_task = task
    import_task >> load_mysql_clans_task >> join_clans_task

    finish_extract_mongo_clans = EmptyOperator(
        task_id='finish_extract_mongo_clans'
    )

    import_task = import_mongo_clans_tasks.pop(0)
    clear_temp_data_task >> extract_mongo_clans_tasks >> finish_extract_mongo_clans >> import_task
    for task in import_mongo_clans_tasks:
        import_task >> task
        import_task = task
    import_task >> join_clans_task >> join_clans_dynamics_task

    import_task = import_clans_matchmaking_tasks.pop(0)
    finish_extract_mongo_clans >> import_task
    for task in import_clans_matchmaking_tasks:
        import_task >> task
        import_task = task
    import_task >> move_clans_matchmaking_task >> concat_extract_state_files_task >> import_extract_state_task

if __name__ == '__main__':
    dag.cli()
