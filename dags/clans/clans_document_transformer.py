from typing import Dict
from dags.lib.services.platform_service import PlatformService
import dags.clans.config.default as dag_config
from dags.lib.services.logger_service import LoggerService
from math import floor

# Можно сделать базовый класс?
class ClansDocumentTransformer:

    @staticmethod
    def __set_fields(fields_config: Dict, document: Dict):
        transformed_document = {}
        for key, config in fields_config.items():
            parts = key.split('.')
            value = document[parts.pop(0)] if parts[0] in document else None
            if value is not None:
                for part in parts:
                    if (part not in value):
                        value = None
                        break
                    value = value[part]
            transformed_document[config['ch_field']] = value
        return transformed_document

    @staticmethod
    def process(document: Dict, connection_config: Dict) -> Dict:

        # LoggerService.logger.info(document)

        transformed_document = ClansDocumentTransformer.__set_fields(dag_config.MONGO_COLLECTION_FIELDS, document)

        # LoggerService.logger.info(transformed_document)

        transformed_document['_connection_id'] = connection_config['id']
        transformed_document['platform_type'] = connection_config['platform']

        if transformed_document['update_time'] is None:
            transformed_document['update_time'] = 0

        transformed_document['league'] = transformed_document['league_current'] \
            if ('league_current' in transformed_document) and (transformed_document['league_current'] is not None) \
            else transformed_document['league_prev']

        return transformed_document
