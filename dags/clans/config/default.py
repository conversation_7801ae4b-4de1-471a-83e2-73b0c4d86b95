from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load clans information',
}

SETTINGS = {

}

# ID союзного ивента
EVENT_MINE_ID = 1
EVENT_REGATTA_ID = 4
EVENT_VILLAGE_TOURNAMENT = 6

CH_MYSQL_CLANS_EXTRACTED_TABLE = 'mysql_clans_extracted'
CH_MYSQL_CLANS_TABLE = 'mysql_clans'
CH_MONGO_CLANS_EXTRACTED_TABLE = 'mongo_clans_extracted'
CH_CLANS_DYNAMICS_TABLE = 'clans_dynamics'
CH_CLANS_TABLE = 'clans'

MYSQL_CLANS_TABLE = 'clans'
MONGO_CLANS_COLLECTION = 'clans'

CH_CLANS_MATCHMAKING_EXTRACTED_TABLE = 'clans_matchmaking_extracted'
CH_CLANS_MATCHMAKING_TABLE = 'clans_matchmaking'

TMP_MYSQL_CLANS_FILE_PATH = 'clans_mysql_[connection].parquet'
TMP_MONGO_CLANS_FILE_PATH = 'clans_mongo_[connection].parquet'
TMP_CLANS_MATCHMAKING_FILE_PATH = 'clans_matchmaking_[connection].parquet'
TMP_CLANS_MATCHMAKING_EXTRACT_STATE_FILE_PATH = 'clans_matchmaking_es_[connection].csv'

MONGO_COLLECTION_FIELDS = {
    '_id': {'ch_field': 'clan_id'},
    '_lastUpdated': {'ch_field': 'update_time'},

    'village.castle.stage': {'ch_field': 'castle_level'},
    'village.tournament.current.league': {'ch_field': 'league_current'},
    'village.tournament.prev.league': {'ch_field': 'league_prev'},

    # for table clans_matchmaking

    'name': {'ch_field': 'name'},
    'usersCount': {'ch_field': 'member_count'},

    'village.tournament.current.initTime': {'ch_field': 'village_date'},
    'village.tournament.current.clans': {'ch_field': 'village_clans'},
    'village.tournament.current.id': {'ch_field': 'village_season_id'},
    'village.tournament.score': {'ch_field': 'village_mmr'},

    'events.mine.initTime': {'ch_field': 'mine_date'},
    'events.mine.rating.clans': {'ch_field': 'mine_clans'},
    'events.mine.seasonId': {'ch_field': 'mine_season_id'},

    'events.regatta.initTime': {'ch_field': 'regatta_date'},
    'events.regatta.rating.clans': {'ch_field': 'regatta_clans'},
    'events.regatta.seasonId': {'ch_field': 'regatta_season_id'},
}

CLANS_CH_FIELDS = ['clan_id', 'update_time', 'castle_level', 'league']
CLANS_MATCHMAKING_CH_FIELDS = ['clan_id', 'update_time', 'castle_level', 'league']

SQL_JOIN_CLANS = '''
    INSERT INTO {schema}.{table_to} (
        _connection_id, _update_time, _update_time_mysql, _update_time_mongo, 
        platform_type, clan_id, name, logo, description, user_level, member_count, status, old_clan, 
        rank, install_time, remove_time, user_id_head, moderator_count, language, castle_level, league)
    SELECT ms._connection_id, greatest(ms._update_time, mg.update_time), ms._update_time, mg.update_time, 
        ms.platform_type, ms.clan_id, ms.name, ms.logo, ms.description, ms.user_level, ms.member_count, ms.status, ms.old_clan, 
        ms.rank, ms.install_time, ms.remove_time, ms.user_id_head, ms.moderator_count, ms.language, mg.castle_level, mg.league
    FROM {mysql_clans_table} AS ms, {mongo_clans_table} AS mg
    WHERE ms.platform_type = mg.platform_type AND ms.clan_id = mg.clan_id
'''
