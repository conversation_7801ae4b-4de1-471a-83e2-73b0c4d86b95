from typing import Dict
from dags.lib.services.platform_service import PlatformService
import dags.clans.config.default as dag_config
from dags.lib.services.logger_service import LoggerService
from math import floor

# Можно сделать базовый класс?
class ClansDocumentSplitter:
    @staticmethod
    def process(document: Dict, connection_config: Dict, max_init_time: int) -> Dict:

        common = {
            '_connection_id': connection_config['id'],
            'platform_type': connection_config['platform'],
            'clan_id': document['clan_id'],
            'castle_level': document['castle_level'],
            'league': document['league'],
        }

        transformed = {
            dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE: {
                'update_time': document['update_time'],
            },
            dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE: []
        }
        transformed[dag_config.CH_MONGO_CLANS_EXTRACTED_TABLE].update(common)

        common['name'] = document['name']
        common['member_count'] = document['member_count']

        # Для трех ивентов можно пока обойтись полукопипастом
        if (document['regatta_date'] is not None) and (document['regatta_date'] > max_init_time):
            row = {
                'date': document['regatta_date'],
                'clans': document['regatta_clans'],
                'event_id': dag_config.EVENT_REGATTA_ID,
                'season_id': document['regatta_season_id']
            }
            row.update(common)
            transformed[dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE].append(row)

        if (document['mine_date'] is not None) and (document['mine_date'] > max_init_time):
            row = {
                'date': document['mine_date'],
                'clans': document['mine_clans'],
                'event_id': dag_config.EVENT_MINE_ID,
                'season_id': document['mine_season_id']
            }
            row.update(common)
            transformed[dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE].append(row)

        if (document['village_date'] is not None) and (document['village_date'] > max_init_time):
            row = {
                'date': document['village_date'],
                'clans': document['village_clans'] if type(document['village_clans']) is list else document['village_clans'].values(),
                'event_id': dag_config.EVENT_VILLAGE_TOURNAMENT,
                'season_id': document['village_season_id'],
                'mmr': document['village_mmr'],
            }
            row.update(common)
            transformed[dag_config.CH_CLANS_MATCHMAKING_EXTRACTED_TABLE].append(row)

        return transformed
