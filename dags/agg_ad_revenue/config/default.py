from datetime import timedelta
from dags.lib.services.platform_service import PlatformService

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for calculating aggregated retention data',
}

SETTINGS = {
    'platforms': [PlatformService.PLATFORM_VK, PlatformService.PLATFORM_OK, PlatformService.PLATFORM_IOS,
                  PlatformService.PLATFORM_ANDROID]
}

PLATFORMS_KEY = 'platforms'
CH_DATAMARTS_TABLE = 'elka2024_agg_ad_revenue'

SQL_SOCIAL_REVENUE = '''
    INSERT INTO agg_ad_revenue (platform_type, dt, install_dt, day_number, geo_country, ads_watched, banner_ads_web, 
        banner_ads_mobile, ad_revenue_usd)
    WITH
    ok_vk_users AS (
        SELECT user_id
             , toDate(install_time, 'Europe/Moscow') AS install_dt
             , geo_country
          FROM users_info
         WHERE install_dt <= toDate('{date}')
           AND platform_type = '{platform}'
    ),
    ok_vk_ads AS (
        SELECT user_id
             , toDate(date, 'Europe/Moscow') AS dt
             , countIf(eventtype = 'ad_watched') AS ads_watched
             , countIf(eventtype = '{platform_event_type}' AND app_type = 1) AS banner_ads_web
             , countIf(eventtype = '{platform_event_type}' AND app_type = 2) AS banner_ads_mobile
          FROM events
         WHERE toDate(date, 'Europe/Moscow') = toDate('{date}')
           AND platform_type = '{platform}'
           AND eventtype IN ('ad_watched', '{platform_event_type}')
      GROUP BY 1, 2
    ),
    ok_vk AS (
        SELECT '{platform}' AS platform_type
             , dt
             , install_dt
             , geo_country
             , SUM(ads_watched) AS ads_watched
             , SUM(banner_ads_web) AS banner_ads_web
             , SUM(banner_ads_mobile) AS banner_ads_mobile  
             , 0 AS ad_revenue_usd
          FROM ok_vk_users 
          JOIN ok_vk_ads
         USING user_id
      GROUP BY 2, 3, 4
    )
    SELECT 
        platform_type, dt, install_dt, dateDiff('day', install_dt, dt) AS day_number, 
        geo_country, ads_watched, banner_ads_web, banner_ads_mobile, ad_revenue_usd     
    FROM ok_vk
'''

SQL_MOBILE_REVENUE = '''
    INSERT INTO agg_ad_revenue (platform_type, dt, install_dt, day_number, geo_country, ads_watched, banner_ads_web, 
        banner_ads_mobile, ad_revenue_usd)
    WITH
    mobile_users AS (
        SELECT user_id
             , toDate(install_time, 'Europe/Moscow') AS install_dt
             , geo_country
          FROM users_info
         WHERE install_dt <= toDate('{date}')
           AND platform_type = '{platform}'
    ),
    mobile_ads AS (
        SELECT user_id
             , toDate(date, 'Europe/Moscow') AS dt
             , SUM(revenue) AS ad_revenue
          FROM events
         WHERE toDate(date, 'Europe/Moscow') = toDate('{date}')
           AND platform_type = '{platform}'
           AND eventtype = 'ad_revenue_watched'
      GROUP BY 1, 2
    ),
    mobile AS (
        SELECT '{platform}' AS platform_type
             , dt
             , install_dt
             , geo_country
             , 0 AS ads_watched
             , 0 AS banner_ads_web
             , 0 AS banner_ads_mobile
             , SUM(ad_revenue) AS ad_revenue_usd
          FROM mobile_users 
          JOIN mobile_ads
         USING user_id
      GROUP BY 2, 3, 4
    )
    SELECT 
        platform_type, dt, install_dt, dateDiff('day', install_dt, dt) AS day_number, 
        geo_country, ads_watched, banner_ads_web, banner_ads_mobile, ad_revenue_usd     
    FROM mobile
'''