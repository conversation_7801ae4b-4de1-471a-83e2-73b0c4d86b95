from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService
from airflow.operators.python import PythonOperator
from datetime import timedelta
import dags.agg_ad_revenue.config.default as dag_config
from dags.lib.helpers.date_helper import get_current_date
from airflow.exceptions import AirflowSkipException
from dags.lib.services.platform_service import PlatformService
from airflow.hooks.base import BaseHook

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)


def calculate(platform: str):
    date_yesterday = get_current_date() - timedelta(days=1)

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    max = ch.get_max_column_value(
        'agg_ad_revenue',
        'dt',
        {'platform_type': platform},
    )

    LoggerService.logger.info(f'Yesterday: {date_yesterday} ({type(date_yesterday)}), max: {max} ({type(max)})')
    if max >= date_yesterday:
        raise AirflowSkipException

    sql = dag_config.SQL_SOCIAL_REVENUE \
        if platform in [PlatformService.PLATFORM_OK, PlatformService.PLATFORM_VK] \
        else dag_config.SQL_MOBILE_REVENUE

    sql = sql.format(
        date=date_yesterday,
        platform=platform,
        platform_event_type='banner_ad_revenue' if platform == PlatformService.PLATFORM_OK else 'banner_ad_shown'
    )
    LoggerService.logger.info(sql)
    ch.execute(sql)

def extract_load_datamarts():
    yesterday = get_current_date() - timedelta(days=1)

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))
    max = ch.get_max_column_value(
        dag_config.CH_DATAMARTS_TABLE,
        'dt'
    )
    LoggerService.logger.info(f'Max source date: {max}')

    if max >= yesterday:
        return

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS))

    sql = f'''
          INSERT INTO FUNCTION remote('{conn.host}', {conn.schema}.{dag_config.CH_DATAMARTS_TABLE}, '{conn.login}', '{conn.password}')
          (*) 
          SELECT * 
          FROM {dag_config.CH_TABLE} 
          WHERE dt > toDate({max})'''
    ch.execute(sql)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    calculate_tasks = [
        PythonOperator(
            task_id=f'calculate_{platform}',
            python_callable=calculate,
            op_kwargs={
                'platform': platform
            },
        )
        for platform in config.get_setting(dag_config.PLATFORMS_KEY)
    ]

    extract_load_datamarts_task = [
        PythonOperator(
            task_id=f'extract_load_datamarts',
            python_callable=extract_load_datamarts
        )
    ]

    calculate_task = calculate_tasks.pop(0)
    for task in calculate_tasks:
        calculate_task >> task
        calculate_task = task

    calculate_task >> extract_load_datamarts_task

if __name__ == '__main__':
    dag.cli()
