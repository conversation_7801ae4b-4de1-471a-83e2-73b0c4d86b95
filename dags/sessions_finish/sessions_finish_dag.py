from airflow import DAG
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
import dags.sessions_finish.config.default as dag_config
from datetime import datetime, timedelta
from dags.lib.services.platform_service import PlatformService

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)

def prepare_table_for_join(platform):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))

    # 120 секунд отнимаем, чтобы отлавливать случаи сетевых задержек и нарушения порядка между стартом сессии и первым
    # в рамках сессии ивентом
    date_from = datetime.now() - config.get_setting(dag_config.SETTING_PERIOD_LAST_SESSION) - timedelta(seconds=120)
    date_to = datetime.now() - config.get_setting(dag_config.SETTING_PERIOD_LAST_EVENT)

    date_from = date_from.strftime("%Y-%m-%d %H:%M:%S")
    date_to = date_to.strftime("%Y-%m-%d %H:%M:%S")

    LoggerService.logger.info(f'Prepare events data from {date_from} to {date_to}')

    ch.truncate(dag_config.CH_EVENTS_LAST_BY_SESSION_TABLE_PART)

    sql = f'INSERT INTO {dag_config.CH_EVENTS_LAST_BY_SESSION_TABLE_PART} ' \
          f'SELECT * FROM {dag_config.CH_EVENTS_LAST_BY_SESSION_TABLE} ' \
          f'WHERE platform_type = \'{platform}\' ' \
          f'AND date > toDateTime(\'{date_from}\') ' \
          f'AND date < toDateTime(\'{date_to}\')'
    ch.execute(sql)


def fill_date_finish_column(platform):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))

    date_last_event = datetime.now() - config.get_setting(dag_config.SETTING_PERIOD_LAST_EVENT)
    date_last_session = datetime.now() - config.get_setting(dag_config.SETTING_PERIOD_LAST_SESSION)

    date_last_session = date_last_session.strftime("%Y-%m-%d %H:%M:%S")
    date_last_event = date_last_event.strftime("%Y-%m-%d %H:%M:%S")

    LoggerService.logger.info(f'Date for last session: {date_last_session}, date for last event: {date_last_event}')

    sql = f'INSERT INTO {dag_config.CH_SESSIONS_TABLE} (_id, _connection_id, _platform, _insert_datetime, platform_type, app_type, user_id, clan_id, session_id, session_number, test_id, test_group, date_start, date_finish, browser_name, device_vendor, device_model, referrer_source, google_services, level, factory_level, merge_level, progress, cash, energy, app_build, server_static, geo_country, os, ip, device_id, no_ads, tokens, passing_mode_level, standard_mode_collection, tournament_mode_collection, passing_mode_collection, standard_mode_collection_number, tournament_mode_collection_number, passing_mode_collection_number, progress_level) ' \
          f'SELECT _id, _connection_id, _platform, _insert_datetime, platform_type, app_type, user_id, clan_id, session_id, session_number, test_id, test_group, date_start, e.date, browser_name, device_vendor, device_model, referrer_source, google_services, level, factory_level, merge_level, progress, cash, energy, app_build, server_static, geo_country, os, ip, device_id, no_ads, tokens, passing_mode_level, standard_mode_collection, tournament_mode_collection, passing_mode_collection, standard_mode_collection_number, tournament_mode_collection_number, passing_mode_collection_number, progress_level ' \
          f'FROM {dag_config.CH_SESSIONS_TABLE} s, {dag_config.CH_EVENTS_LAST_BY_SESSION_TABLE_PART} e FINAL ' \
          f'WHERE s.platform_type = \'{platform}\' ' \
          f'AND s.date_start > toDateTime(\'{date_last_session}\') ' \
          f'AND s.date_finish IS NULL ' \
          f'AND s.user_id = e.user_id ' \
          f'AND s.session_id = e.session_id ' \
          f'AND e.platform_type = \'{platform}\' ' \
          f'AND e.date < toDateTime(\'{date_last_event}\')'
    ch.execute(sql)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    fill_date_finish_column_tasks = [
        (
            PythonOperator(
                task_id=f'prepare_table_for_join_{platform}',
                python_callable=prepare_table_for_join,
                op_kwargs={
                    'platform': platform
                },
            ),
            PythonOperator(
                task_id=f'fill_date_finish_column_{platform}',
                python_callable=fill_date_finish_column,
                op_kwargs={
                    'platform': platform
                },
            )
        )
        for platform in PlatformService.get_names(
            PlatformService.expand_mobile_connection(config.get_connections(ConfigService.CONN_MYSQL_MASTERS)))
    ]

    task_pair = fill_date_finish_column_tasks.pop(0)
    task_pair[0] >> task_pair[1]
    current_task = task_pair[1]
    for task in fill_date_finish_column_tasks:
        current_task >> task[0] >> task[1]
        current_task = task[1]
    current_task = None

if __name__ == '__main__':
    dag.cli()
