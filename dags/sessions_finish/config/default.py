from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for filling date_finish column for sessions table',
}

SETTING_PERIOD_LAST_EVENT = 'period_last_event'
SETTING_PERIOD_LAST_SESSION = 'period_last_session'
SETTING_MAX_SESSION_DURATION_SECONDS = 'max_session_duration_seconds'

SETTINGS = {
    SETTING_PERIOD_LAST_EVENT: timedelta(minutes=60),
    SETTING_PERIOD_LAST_SESSION: timedelta(hours=336),
    # Определено эмпирически по имеющимся данным, перцентиль 99
    SETTING_MAX_SESSION_DURATION_SECONDS: timedelta(hours=48)
}

CH_SESSIONS_TABLE = 'user_sessions'
CH_EVENTS_LAST_BY_SESSION_TABLE = 'events_last_by_session'
CH_EVENTS_LAST_BY_SESSION_TABLE_PART = 'events_last_by_session_part'