from airflow.operators.bash import <PERSON><PERSON><PERSON><PERSON>ator
import os
import errno
import pyarrow.parquet as pq
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE, TSV_DATA_TYPE, PARQUET_DATA_TYPE
from airflow.exceptions import AirflowSkipException


class DataToClickHouseOperator(BashOperator):
    def __init__(
            self,
            connection_id,
            file_path,
            table_name,
            task_id,
            data_type,
            skip_instead_of_error: bool = False,
            *args, **kwargs
    ) -> None:
        bash_command = ''
        if data_type == CSV_DATA_TYPE:
            delimiter = ';'
            if kwargs.get('delimiter') is not None:
                delimiter = kwargs.get('delimiter')
                del kwargs['delimiter']
            bash_command = ClickhouseService.get_csv_file_import_command(connection_id, table_name, file_path,
                                                                         delimiter)
        elif data_type == TSV_DATA_TYPE:
            bash_command = ClickhouseService.get_tsv_file_import_command(connection_id, table_name, file_path)
        elif data_type == PARQUET_DATA_TYPE:
            bash_command = ClickhouseService.get_parquet_file_import_command(connection_id, table_name, file_path)
        super().__init__(
            task_id=task_id,
            bash_command=bash_command,
            *args,
            **kwargs)
        self.file_path = file_path
        self.data_type = data_type
        self.skip_instead_of_error = skip_instead_of_error

    def __file_is_empty(self) -> bool:
        if self.data_type == PARQUET_DATA_TYPE:
            return pq.read_metadata(self.file_path).num_rows == 0
        else:
            return os.stat(self.file_path).st_size == 0

    def execute(self, context):
        if not os.path.exists(self.file_path):
            if self.skip_instead_of_error:
                raise AirflowSkipException
            else:
                raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), self.file_path)
        if self.__file_is_empty():
            LoggerService.logger.info(f"File {self.file_path} is empty")
            return
        super().execute(context=context)
