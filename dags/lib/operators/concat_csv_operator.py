from airflow.operators.bash import Bash<PERSON>perator
import os
from dags.lib.services.file_path_service import FilePathService


class ConcatCSVOperator(BashOperator):

    def __init__(
            self,
            task_id,
            base_path,
            path_template,
            connection_ids,
            *args, **kwargs
    ) -> None:
        super().__init__(
            task_id=task_id,
            bash_command='',
            *args,
            **kwargs)
        self.base_path = base_path
        self.path_template = path_template
        self.connection_ids = connection_ids

    def execute(self, context):

        fps = FilePathService('', self.base_path, {self.path_template: self.connection_ids})

        header_path = fps.get_header_path()
        result_path = fps.get_result_path()
        # Если header-файла не существует, значит ни по одному коннекту ничего нового не было выгружено
        # При этом нам нужно создать пустой результирующий файл
        if not os.path.exists(header_path):
            open(result_path, 'w', newline='', encoding='utf-8').close()
            return

        if os.path.exists(result_path):
            os.remove(result_path)

        paths = [header_path]
        for path in fps.get_paths():
            if os.path.exists(path) and path not in paths:
                paths.append(path)

        self.bash_command = 'cat ' + ' '.join(paths) + ' > ' + result_path
        super().execute(context=context)

        for path in paths:
            os.remove(path)
