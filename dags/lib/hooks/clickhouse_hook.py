from airflow_clickhouse_plugin.hooks.clickhouse_hook import ClickHouseHook
from clickhouse_driver import Client


class CustomClickHouseHook(ClickHouseHook):
    def __init__(self, use_compression=True, *args, ** kwargs):
        super().__init__(*args, ** kwargs)
        self.use_compression = use_compression

    def get_conn(self) -> Client:
        conn = self.get_connection(self.clickhouse_conn_id)
        connection_kwargs = conn.extra_dejson.copy()
        if conn.port:
            connection_kwargs.update(port=int(conn.port))
        if conn.login:
            connection_kwargs.update(user=conn.login)
        if conn.password:
            connection_kwargs.update(password=conn.password)
        if self.database:
            connection_kwargs.update(database=self.database)
        elif conn.schema:
            connection_kwargs.update(database=conn.schema)
        return Client(conn.host or 'localhost', compression=self.use_compression, **connection_kwargs)
