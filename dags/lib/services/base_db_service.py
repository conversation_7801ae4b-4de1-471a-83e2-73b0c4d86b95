from abc import ABC, abstractmethod
from typing import List, Tuple
import logging


class DBService(ABC):
    logger = logging.getLogger('airflow.task')
    block_size = 10000

    def __init__(self, db_conn_id: str):
        self._db_conn_id: str = db_conn_id
        self.conn = None

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    @abstractmethod
    def execute(self, sql: str) -> List[Tuple]:
        pass

    @abstractmethod
    def close(self):
        pass

    @abstractmethod
    def truncate(self, table_name):
        pass

    @abstractmethod
    def drop(self, table_name):
        pass
