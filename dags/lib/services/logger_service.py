import logging


class LoggerService:
    logger = logging.getLogger('airflow.task')

    def __init__(self):
        return

    @staticmethod
    def start_truncating(*tables):
        LoggerService.logger.info(f'Start truncating tables: {", ".join(tables)}')

    @staticmethod
    def error_truncating(e, *tables):
        LoggerService.logger.info(f'Can not truncate tables: {", ".join(tables)}. Details: {str(e)}')

    @staticmethod
    def start_extracting(platform, connection, *tables):
        LoggerService.logger.info(f'Start reading data from {platform} platform, {connection} connection. '
                                  f'List of tables: {", ".join(tables)}')

    @staticmethod
    def error_extracting(connection: str, err: str):
        LoggerService.logger.error(f'Cannot extract data from {connection}, see detailed information: {err}')

    @staticmethod
    def finish_extracting(rows_count):
        LoggerService.logger.info(f'Rows read: {rows_count}')

    @staticmethod
    def error_extracting(e, platform, connection, *tables):
        LoggerService.logger.info(f'Can not read data from {platform} platform, {connection} connection. '
                                  f'List of tables: {", ".join(tables)}. Details: {str(e)}')

    @staticmethod
    def start_move_data(from_table: str, to_table: str):
        LoggerService.logger.info(f'Start move payments data from {from_table} to {to_table}')

    @staticmethod
    def error_move_data(from_table: str, to_table: str, err: str):
        LoggerService.logger.error(f'Cannot move payments data from {from_table} to {to_table},'
                                   f' see detailed information: {err}')

    @staticmethod
    def start_connect_database(name: str):
        LoggerService.logger.info(f'Start try to connect to "{name}" database')

    @staticmethod
    def error_connect_database(name: str, err: str):
        LoggerService.logger.error(f'Cannot connect to "{name}" database, detailed information: {err}')

    @staticmethod
    def finish_connect_database(name: str):
        LoggerService.logger.info(f'Finish try to connect to "{name}" database')

    @staticmethod
    def get_data_from_mysql_table(table: str):
        LoggerService.logger.info(f'Get data from MySQL table "{table}"')

    @staticmethod
    def start_read_http_response(response_type: str):
        LoggerService.logger.info(f'Start read {response_type} response')

    @staticmethod
    def finish_read_http_response(response_type: str):
        LoggerService.logger.info(f'Finish read {response_type} response')

    @staticmethod
    def debug_info(data):
        LoggerService.logger.info(data)
