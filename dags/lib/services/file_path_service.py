from typing import Dict


class FilePathService:
    PLACEHOLDER = '[connection]'
    HEADER = 'header'

    def __init__(self, dag_id: str, base_path: str, path_configs: Dict):
        self.dag_id = dag_id
        self.base_path = base_path
        self.path_configs = path_configs

    def __get_path_template(self, template: str):
        if template is None:
            template = list(self.path_configs.keys())[0]
        return self.base_path + '/' + template

    def get_path(self, placeholder_value: str, template: str = None):
        return self.__get_path_template(template).replace(self.PLACEHOLDER, placeholder_value)

    def get_result_path(self, template: str = None):
        return self.__get_path_template(template).replace('_' + self.PLACEHOLDER, '')

    def get_header_path(self, template: str = None):
        return self.get_path(self.HEADER, template)

    def get_paths(self):
        paths = []
        for template, values in self.path_configs.items():
            for value in values:
                paths.append(self.get_path(value, template))
        return paths

    def get_all_paths(self):
        paths = []
        for template, values in self.path_configs.items():
            paths.append(self.get_header_path(template))
            paths.append(self.get_result_path(template))
            for value in values:
                paths.append(self.get_path(value, template))
        return paths

    def get_glob_pattern(self):
        return self.base_path + '/' + self.dag_id + '_*'
