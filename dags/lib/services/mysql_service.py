from typing import List, Tuple, Dict
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.hooks.base import BaseHook
from dags.lib.services.base_db_service import DBService


class MySQLService(DBService):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conn = MySqlHook(mysql_conn_id=self._db_conn_id).get_conn()
        self.schema = BaseHook.get_connection(self._db_conn_id).schema

    def execute(self, sql: str, parameters=None) -> List[Tuple]:
        with self.conn.cursor() as cursor:
            self.logger.info(sql)
            cursor.execute(sql, parameters)
            self.conn.commit()
            results = cursor.fetchall()
            return results

    def select_with_columns_names(self, sql: str, parameters=None) -> List[Dict]:
        with self.conn.cursor() as cursor:
            self.logger.info(sql)
            cursor.execute(sql, parameters)
            desc = cursor.description
            column_names = [col[0] for col in desc]
            results = [dict(zip(column_names, row)) for row in cursor.fetchall()]
            return results

    def insert(self, sql, values):
        with self.conn.cursor() as cursor:
            self.logger.info(sql)
            cursor.execute(sql, values)
        self.conn.commit()

    def truncate(self, table_name):
        sql = f"truncate table if exists {table_name}"
        self.logger.info(sql)
        self.execute(sql)

    def drop(self, table_name):
        sql = f"drop table if exists {table_name}"
        self.logger.info(sql)
        self.execute(sql)

    def close(self):
        self.conn.close()

    def stream(self, sql: str) -> Dict:
        columns_names = None
        with self.conn.cursor() as cursor:
            cursor.execute(sql)
            row = cursor.fetchone()
            while row:
                if not columns_names:
                    columns_names = self._get_columns_names(cursor)
                yield dict(zip(columns_names, row))
                row = cursor.fetchone()

    def table_exist(self, name: str) -> int:
        sql = f"""
            SELECT
                count(1) as exist
            FROM information_schema.TABLES
            WHERE 
                lower(TABLE_NAME) = lower('{name}')
            AND
                lower(TABLE_SCHEMA) = lower('{self.schema}')
        """
        data = self.select_with_columns_names(sql)
        return data[0]['exist']

    def get_tables(self, template: str) -> List[Dict]:
        sql = f"""
            SELECT DISTINCT
                lower(sch.table_name) AS table_name
            FROM information_schema.TABLES sch
            WHERE lower(sch.table_name) LIKE '{template}%'
                AND lower(sch.table_schema) = lower('{self.schema}')
        """
        return self.select_with_columns_names(sql)

    def get_total_rows_number(self, sql):
        with self.conn.cursor() as cursor:
            cursor.execute(f"select count(*) as total_rows from ({sql}) s")
            total_rows = cursor.fechone()[0]
            return {"total_rows": total_rows}

    @staticmethod
    def _get_columns_names(cursor) -> List[str]:
        return [desc[0] for desc in cursor.description]

    @property
    def connection(self):
        return self.conn
