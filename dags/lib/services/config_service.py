from importlib import import_module
from dags.lib.services.environment_service import EnvironmentService


class ConfigService:
    CONN_CLICKHOUSE_MAIN = 'clickhouse_main'
    CONN_CLICKHOUSE_ETL = 'clickhouse_etl'
    CONN_CLICKHOUSE_PREV_YEAR = 'clickhouse_prev_year'
    CONN_AWS_S3 = 'aws_s3'
    CONN_SLACK = 'slack'
    CONN_CLICKHOUSE_DATAMARTS = 'clickhouse_datamarts'
    CONN_CLICKHOUSE_DATAMARTS_ETL = 'clickhouse_datamarts_etl'
    CONN_GOOGLE_BIG_QUERY = 'google_big_query'
    CONN_MYSQL_MASTERS = 'mysql_masters'
    CONN_MYSQL_SHARDS = 'mysql_shards'
    CONN_MYSQL_OP_LOGS = 'mysql_op_logs'
    CONN_MONGO_SHARDS = 'mongo_shards'
    CONN_REDIS_MASTERS = 'redis_masters'
    TMP_FILES_FOLDER = 'tmp_files_folder'
    EVENTS_FILES_FOLDER = 'events_files_folder'
    OP_LOG_ACTIONS = 'oplogs_actions'
    BATCH_SIZE = 'batch_size'
    EXCHANGE_RATES_URL = 'exchange_rates_url'

    def __init__(self, env: str, dag_id: str):
        self.env = env
        self.dag_id = dag_id
        self.dag_args = None
        self.connections = None
        self.settings = None
        self.env_params = None
        self.batch_size = None

    def __merge(self, configs: list, key: str):
        merged = {}
        for config in configs:
            if key in config:
                merged = merged | config[key]
        return merged

    def __load_configs(self):
        if self.dag_args is not None:
            return
        global_default = import_module('dags.config.default')
        global_env = import_module(f'dags.config.{self.env}') if self.env != EnvironmentService.LOCAL else None
        dag_default = import_module(f'dags.{self.dag_id}.config.default')
        dag_env = import_module(f'dags.{self.dag_id}.config.{self.env}') \
            if self.env != EnvironmentService.LOCAL else None

        default_args = self.__merge(
            [
                global_default.DAG_ARGS,
                (global_env.DAG_ARGS if global_env is not None else {}),
                dag_default.DAG_ARGS,
                (dag_env.DAG_ARGS if dag_env is not None else {})
            ],
            'default_args'
        )

        self.dag_args = global_default.DAG_ARGS | \
                        (global_env.DAG_ARGS if global_env is not None else {}) | \
                        dag_default.DAG_ARGS | \
                        (dag_env.DAG_ARGS if dag_env is not None else {})
        self.dag_args['default_args'] = default_args

        self.connections = global_default.CONNECTIONS | (global_env.CONNECTIONS if global_env is not None else {})
        self.settings = global_default.SETTINGS | \
                        (global_env.SETTINGS if global_env is not None else {}) | \
                        dag_default.SETTINGS | \
                        (dag_env.SETTINGS if dag_env is not None else {})

    def get_dag_arguments(self):
        self.__load_configs()
        return self.dag_args

    def get_connection(self, name: str):
        self.__load_configs()
        return self.connections[name]

    def get_connections(self, type: str):
        self.__load_configs()
        return self.connections[type]

    def get_connection_ids(self, type: str):
        return [conn['id'] for conn in self.get_connections(type)]

    def get_setting(self, name: str):
        self.__load_configs()
        return self.settings[name]
