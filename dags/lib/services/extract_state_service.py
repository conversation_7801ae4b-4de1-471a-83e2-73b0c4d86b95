from datetime import datetime
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.config.default import CH_EXTRACT_STATE_TABLE
from dags.lib.helpers.convert_value_helper import get_default_value


class ExtractStateService:
    INT_TYPE = 'int'
    STR_TYPE = 'str'
    DATE_TYPE = 'date'

    def __init__(self, ch: ClickhouseService):
        self.ch = ch

    def get_last_value(self, connection_id: str, source_name: str, key_name: str, val_type: str = INT_TYPE):
        result = self.ch.get_first(f'''
            SELECT last_value_{val_type} 
            FROM {CH_EXTRACT_STATE_TABLE} FINAL
            WHERE connection_id = '{connection_id}' AND
                source_name = '{source_name}' AND 
                key_name = '{key_name}'
        ''')
        if result is None:
            if val_type == self.INT_TYPE:
                return get_default_value(type_val=int)
            if val_type == self.STR_TYPE:
                return ''
            if val_type == self.DATE_TYPE:
                return get_default_value(type_val=datetime)
        else:
            return result[0]
