from typing import List, Tuple, Dict
import pandas as pd
from dags.lib.hooks.clickhouse_hook import Custom<PERSON><PERSON>HouseHook
from airflow.hooks.base import BaseHook

CSV_DATA_TYPE = 'csv'
PARQUET_DATA_TYPE = 'parquet'
TSV_DATA_TYPE = 'tsv'


class ClickhouseService:
    def __init__(self, db_conn_id: str, block_size: int = 500000):
        self.hook = CustomClickHouseHook(
            clickhouse_conn_id=db_conn_id,
        )
        self.conn = self.hook.get_conn()
        self.block_size = block_size

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def select(self, sql: str, parameters=None) -> List[Tuple]:
        self.conn.execute(sql)
        results = self.conn.execute(sql, parameters)
        return results

    def stream(self, sql: str) -> Dict:
        rows = self.conn.execute_iter(query=sql, settings={'max_block_size': self.block_size})
        for row in rows:
            yield row

    def select_with_columns_names(self, sql: str) -> List[Dict]:
        result, columns = self.conn.execute(sql, with_column_types=True)
        return [dict(zip([row[0] for row in columns], row)) for row in result]

    def execute(self, sql: str) -> None:
        self.conn.execute(sql)

    def get_first(self, sql: str):
        return self.hook.get_first(sql)

    def insert(self, sql, values, types_check=True):
        self.conn.execute(sql, values, types_check=types_check)

    def truncate(self, table_name):
        sql = f'truncate table {table_name}'
        self.execute(sql)

    def close(self):
        self.conn.disconnect()

    def get_max_column_value(self, table_name, column_name, where=None, convert_to_timestamp=False):
        sql_where = []
        if where is not None:
            for column, value in where.items():
                sql_where.append(f'{column}=\'{value}\'')
        column_name = f'MAX({column_name})' if not convert_to_timestamp else f'toUnixTimestamp(MAX({column_name}))'
        result = self.hook.get_first(f'SELECT {column_name} FROM {table_name}' +
                                     (' WHERE ' + (' AND '.join(sql_where)) if len(sql_where) > 0 else ''))
        if result is None:
            # TODO: Наверное все-таки None нужно возвращать из метода
            return 0
        else:
            return result[0]

    def get_max_column_values(self, table_name, column_name, group_by_column):
        result = self.hook.get_records(f'SELECT {group_by_column}, MAX({column_name}) '
                                       f'FROM {table_name} '
                                       f'GROUP BY {group_by_column}')
        values = {}
        for row in result:
            values[row[0]] = row[1]
        return values

    # TODO: Можеть быть можно объединить с get_max_column_values, но пока сделал отдельно, чтобы не рефакторить
    # другие ДАГи
    def get_max_column_values_flat(self, table_name, column_name, group_by_columns):
        columns_joined = ', '.join(group_by_columns)
        result = self.hook.get_records(f'SELECT {columns_joined}, MAX({column_name}) '
                                       f'FROM {table_name} '
                                       f'GROUP BY {columns_joined}')
        values = []
        length = len(group_by_columns)
        for row in result:
            data = {}
            for index, name in enumerate(group_by_columns):
                data[name] = row[index]
            data['value'] = row[length]
            values.append(data)
        return values

    def insert_dataframe(self, df, table_name):
        columns = ','.join(df.columns.tolist())
        self.insert(f"insert into table {table_name} ({columns}) values", values=(row for row in df.values.tolist()))

    def optimize_table(self, table_name):
        sql = f'OPTIMIZE TABLE {table_name} FINAL DEDUPLICATE'
        self.execute(sql)

    def select_dataframe(self, sql, *args, **kwargs):
        return self.conn.query_dataframe(sql, *args, **kwargs)

    def select_dataframe_iter(self, sql, block_size):
        rows_gen = self.conn.execute_iter(sql, with_column_types=True)
        columns_names = self.__get_columns_names(rows_gen)
        rows_block = []
        for row in rows_gen:
            rows_block.append(row)
            if len(rows_block) % block_size == 0:
                df = pd.DataFrame(rows_block, columns=columns_names)
                rows_block.clear()
                yield df
        df = pd.DataFrame(rows_block, columns=columns_names)
        yield df

    @staticmethod
    def make_data_file_import_command(
            connection_name: str,
            table_name: str,
            data_type: str,
            delimiter: str = ';'
    ) -> str:
        conn = BaseHook.get_connection(connection_name)
        credentials = {
            'host': conn.host,
            'port': conn.port,
            'user': conn.login,
            'password': conn.password,
            'database': conn.schema
        }
        ch_command = 'clickhouse-client'
        for key, value in credentials.items():
            ch_command += f' --{key} {value}'
        if data_type == CSV_DATA_TYPE:
            ch_command += f' --input_format_skip_unknown_fields=1 --format_csv_allow_single_quotes=0' \
                          f' --format_csv_delimiter="{delimiter}"' \
                          f' --query="insert into {table_name} format CSVWithNames"'
        elif data_type == TSV_DATA_TYPE:
            ch_command += f' --input_format_skip_unknown_fields=1' \
                          f' --query="insert into {table_name} format TSVWithNames"'
        elif data_type == PARQUET_DATA_TYPE:
            ch_command += f' --input_format_parquet_allow_missing_columns=true' \
                          f' --query="insert into {table_name} format Parquet"'
        return ch_command

    @staticmethod
    def get_csv_file_import_command(connection_name, table_name, file_path, delimiter):
        return f'cat {file_path} | ' \
               f'{ClickhouseService.make_data_file_import_command(connection_name, table_name, CSV_DATA_TYPE, delimiter)}'

    @staticmethod
    def get_tsv_file_import_command(connection_name, table_name, file_path):
        return f'cat {file_path} | ' \
               f'{ClickhouseService.make_data_file_import_command(connection_name, table_name, TSV_DATA_TYPE)}'

    @staticmethod
    def get_parquet_file_import_command(connection_name, table_name, file_path):
        return f'cat {file_path} | ' \
               f'{ClickhouseService.make_data_file_import_command(connection_name, table_name, PARQUET_DATA_TYPE)}'

    @staticmethod
    def __get_columns_names(rows_gen):
        return tuple(column_name for column_name, _ in next(rows_gen))
