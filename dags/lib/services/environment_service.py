import platform
import os
from dags.lib.services.logger_service import LoggerService


class EnvironmentService:
    DEVELOP = 'dev'
    PRODUCTION = 'prod'
    LOCAL = 'local'

    @staticmethod
    def get_env() -> str:
        # elka2023-airflow-dev
        # elka2023-airflow-prod
        # elka2023-airflow-worker-prod1
        last_part = platform.node().split('-')[-1]
        LoggerService.logger.info(f'Env hostname part: {last_part}')
        if last_part in ('prod', 'prod1'):
            return EnvironmentService.PRODUCTION
        elif last_part == 'dev':
            return EnvironmentService.DEVELOP
        else:
            return EnvironmentService.LOCAL

    @staticmethod
    def get_dag_id(file_name: str) -> str:
        return os.path.basename(file_name).replace('_dag.py', '')
