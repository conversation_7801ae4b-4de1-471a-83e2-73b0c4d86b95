from airflow.hooks.base import <PERSON><PERSON><PERSON>
from airflow.utils.trigger_rule import TriggerRule
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService


class NotificationService:
    SLACK_FAILED_TASK_ID = 'slack_failed_notification_id'
    SLACK_SUCCESS_TASK_ID = 'slack_success_notification_id'
    SLACK_USER_NAME = 'airflow-notification-bot'

    @staticmethod
    def task_failure_alert(context, **kwargs) -> None:
        dag_id = NotificationService.__get_context_dag_id(context=context)
        slack_connection_id = NotificationService.__get_slack_connection_id(dag_id)
        slack_webhook_token = BaseHook.get_connection(slack_connection_id).password
        slack_message = '''
                :red_circle: Task failed.
                *Task*: {task}
                *Dag*: {dag}
                *Execution Time*: {exec_date}
                *Log Url*: {log_url}
                '''.format(
            task=NotificationService.__get_context_task_id(context=context),
            dag=dag_id,
            exec_date=NotificationService.__get_context_logical_date(context=context),
            log_url=NotificationService.__get_context_log_url(context=context)
        )
        return SlackWebhookOperator(
            task_id=NotificationService.SLACK_FAILED_TASK_ID,
            slack_webhook_conn_id=slack_connection_id,
            message=slack_message,
        ).execute(context=context)

    @staticmethod
    def task_success_alert(context, **kwargs):
        dag_id = NotificationService.__get_context_dag_id(context=context)
        slack_connection_id = NotificationService.__get_slack_connection_id(dag_id)
        slack_webhook_token = BaseHook.get_connection(slack_connection_id).password
        slack_message = '''
                    :large_green_circle: Task success. 
                    *Task*: {task}  
                    *Dag*: {dag} 
                    *Execution Time*: {exec_date}
                    '''.format(
            task=NotificationService.__get_context_task_id(context=context),
            dag=dag_id,
            exec_date=NotificationService.__get_context_logical_date(context=context)
        )
        return SlackWebhookOperator(
            task_id=NotificationService.SLACK_SUCCESS_TASK_ID,
            trigger_rule=TriggerRule.ALL_SUCCESS,
            http_conn_id=slack_connection_id,
            webhook_token=slack_webhook_token,
            message=slack_message,
            username=NotificationService.SLACK_USER_NAME
        ).execute(context=context)

    @staticmethod
    def __get_slack_connection_id(dag_id: str) -> str:
        return ConfigService(
            env=EnvironmentService.get_env(),
            dag_id=dag_id
        ).get_connection(ConfigService.CONN_SLACK)

    @staticmethod
    def __get_context_task_instance(context):
        return context.get('task_instance')
    
    @staticmethod
    def __get_context_task_id(context):
        return NotificationService.__get_context_task_instance(context).task_id
    
    @staticmethod
    def __get_context_dag_id(context):
        return NotificationService.__get_context_task_instance(context).dag_id

    @staticmethod
    def __get_context_log_url(context):
        return NotificationService.__get_context_task_instance(context).log_url
    
    @staticmethod
    def __get_context_logical_date(context):
        return context.get('logical_date')

