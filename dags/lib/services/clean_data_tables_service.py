import os
from datetime import datetime, timedelta
from typing import List, Dict
from importlib import import_module
from dags.lib.helpers.directory_helper import directory_scan_cursor
from dags.lib.helpers.date_helper import get_current_date, date_time_to_str
from dags.lib.services.environment_service import EnvironmentService


class CleanDataTablesService:
    TTL_CONDITIONS_KEY = 'ttl'
    TTL_TABLE_FIELD_KEY = 'field'
    TTL_DATE_FORMAT_KEY = 'date_format'
    TTL_DATE_FORMAT = '%Y-%m-%d'
    TTL_DATE_TIME_FORMAT = '%Y-%m-%d %H:%M:%S'
    QUERY_FIELD = 'query'
    USE_ETL_DATABASE = 'use_etl_database'

    def __init__(self, env: str, dag_id: str):
        self.env = env if env == EnvironmentService.PRODUCTION else EnvironmentService.DEVELOP
        self.dag_id = dag_id
        self.tables = None
        self.conditions = {}

    def get_clean_queries(self) -> Dict:
        self.__init_tables()
        result = {}
        for table in self.tables:
            table_conditions = self.__get_table_conditions(table)
            for table_condition in table_conditions:
                result[self.USE_ETL_DATABASE] = True if self.USE_ETL_DATABASE in table_condition else False
                ttl = table_condition[self.TTL_CONDITIONS_KEY]
                ttl_date_format = table_condition[self.TTL_DATE_FORMAT_KEY]
                ttl_field_orig = table_condition[self.TTL_TABLE_FIELD_KEY]
                ttl_field = f'toDate({ttl_field_orig})' if ttl_date_format == self.TTL_DATE_FORMAT \
                    else f'toDateTime({ttl_field_orig})'
                ttl_date = datetime.combine(get_current_date() - timedelta(days=ttl), datetime.max.time())
                where_in = []
                for key in table_condition:
                    if key in [self.TTL_CONDITIONS_KEY, self.TTL_TABLE_FIELD_KEY, self.TTL_DATE_FORMAT_KEY,
                               self.USE_ETL_DATABASE]:
                        continue
                    if len(table_condition[key]) > 0:
                        where_in.append(f'{key} IN ({",".join(table_condition[key])})')
                where_in = f"{' AND '.join(where_in)} AND " if len(where_in) > 0 else ''
                result[self.QUERY_FIELD] = f"ALTER TABLE {table} DELETE WHERE {where_in}{ttl_field_orig} IS NOT NULL " \
                                           f"AND {ttl_field} <= '{date_time_to_str(dt=ttl_date, fmt=ttl_date_format)}'"
                yield result

    def __get_table_conditions(self, table: str) -> List[Dict]:
        if table not in self.conditions:
            table_config = import_module(f'dags.{self.dag_id}.tables.{table}')
            self.conditions[table] = table_config.CLEAN_CONDITIONS[self.env]
        return self.conditions[table]

    def __init_tables(self):
        if self.tables is not None:
            return
        tables = []
        path = '/'.join((os.path.realpath(__file__)).split('/')[:-3] + [self.dag_id, 'tables'])
        for fl in directory_scan_cursor(path=path, extension='py'):
            table = fl.split('/').pop().replace('.py', '')
            tables.append(table)
        self.tables = tables
