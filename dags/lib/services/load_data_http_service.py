import requests
import pandas as pd
import json
from io import String<PERSON>
from dags.lib.services.logger_service import LoggerService
from dags.lib.helpers.convert_value_helper import get_converted_value
from dags.lib.helpers.sanitize_dataframe import sanitize_dataframe

HTTP_METHOD_GET = 'GET'
HTTP_METHOD_POST = 'POST'

REQUEST_DATA_TYPE_CSV = 'csv'
REQUEST_DATA_TYPE_JSON = 'json'
CSV_SEPARATOR_COMMA = ','


class LoadDataHttpService:
    def __init__(
            self,
            platform_type: str,
            url: str,
            data_type,
            columns,
            method: str = HTTP_METHOD_GET,
            request_data=None,
            request_headers=None,
            separator: str = CSV_SEPARATOR_COMMA,
            use_response_header_data: bool = False
    ):
        if request_headers is None:
            request_headers = {}
        if request_data is None:
            request_data = {}
        self.platform_type = platform_type
        self.url = url
        self.data_type = data_type
        self.columns = columns
        self.method = method
        self.request_data = request_data
        self.request_headers = request_headers
        self.separator = separator
        self.use_response_header_data = use_response_header_data

    def _send_request(self):
        if self.method == HTTP_METHOD_GET:
            request = requests.get(self.url, data=self.request_data, headers=self.request_headers)
        else:
            request = requests.post(self.url, data=self.request_data, headers=self.request_headers)
        if request.status_code == 200:
            return request.text
        else:
            return None

    def read_csv_response(self) -> pd.DataFrame:
        LoggerService.start_read_http_response(response_type=REQUEST_DATA_TYPE_CSV)
        response = self._send_request()
        if self.use_response_header_data:
            df = pd.read_csv(StringIO(response), sep=self.separator).rename(columns=self.columns)
        else:
            df = pd.read_csv(StringIO(f'{self.separator}'.join(self.columns)+'\n'+response), sep=self.separator)\
                .rename(columns=self.columns)
        df['platform_type'] = self.platform_type
        df = sanitize_dataframe(df)
        for source_column, destination_column in dict(self.columns).items():
            df[destination_column] = df[destination_column].apply(
                lambda x: get_converted_value(val=x, type_val=self.data_type[destination_column])
            )
        LoggerService.finish_read_http_response(response_type=REQUEST_DATA_TYPE_CSV)
        return df

    def read_json_response(self) -> pd.DataFrame:
        LoggerService.start_read_http_response(response_type=REQUEST_DATA_TYPE_JSON)
        response = self._send_request()
        df = pd.DataFrame.from_dict(json.loads(response)['data'], orient='index').rename(columns=self.columns)
        df['platform_type'] = self.platform_type
        df = sanitize_dataframe(df)
        for source_column, destination_column in dict(self.columns).items():
            try:
                df[destination_column] = df[destination_column].apply(
                    lambda x: get_converted_value(val=x, type_val=self.data_type[destination_column])
                )
            except Exception as e:
                continue
        LoggerService.finish_read_http_response(response_type=REQUEST_DATA_TYPE_JSON)
        return df
