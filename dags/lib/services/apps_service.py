
class AppsService:
    APP_SOC_NET_WEB = 'SocNet Web'
    APP_SOC_NET_MOBILE = 'SocNet Mobile'
    APP_SOC_NET_APPLICATION = 'SocNet Application'
    APP_MOBILE_APPLICATION = 'Mobile Application'
    APP_UNKNOWN = 'Unknown'
    APP_MAPPING = {
        1: APP_SOC_NET_WEB,
        2: APP_SOC_NET_MOBILE,
        3: APP_SOC_NET_APPLICATION,
        4: APP_MOBILE_APPLICATION
    }

    @staticmethod
    def get_name_by_id(app_id: int) -> str:
        return (AppsService.APP_MAPPING[app_id]
                if app_id in AppsService.APP_MAPPING
                else AppsService.APP_UNKNOWN)
