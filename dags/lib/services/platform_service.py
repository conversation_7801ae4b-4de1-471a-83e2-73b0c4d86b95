from typing import Dict


class PlatformService:
    PLATFORM_VK = 'vk'
    PLATFORM_OK = 'ok'
    PLATFORM_FB = 'fb'
    PLATFORM_WEB = 'web'
    PLATFORM_IOS = 'ios'
    PLATFORM_ANDROID = 'android'
    # Для коннектов, на уровне аналитиков такое значение не существует
    PLATFORM_MOBILE = 'mobile'
    PLATFORM_UNKNOWN = 'unknown'
    PLATFORM_MAPPING = {
        1: PLATFORM_VK,
        2: PLATFORM_OK,
        3: PLATFORM_FB,
        4: PLATFORM_IOS,
        5: PLATFORM_ANDROID,
        6: PLATFORM_WEB
    }

    @staticmethod
    def get_name(id: int) -> str:
        return (PlatformService.PLATFORM_MAPPING[id]
                if id in PlatformService.PLATFORM_MAPPING
                else PlatformService.PLATFORM_UNKNOWN)

    @staticmethod
    def get_names(connections: list) -> list:
        unique = set()
        for connection in connections:
            unique.add(connection['platform'])
        unique = list(unique)
        # Airflow не может обработать изменчивый порядок значений в списках задач
        unique.sort()
        return unique

    @staticmethod
    def expand_mobile_connection(connections: list) -> list:
        for index, connection in enumerate(connections):
            if connection['platform'] == PlatformService.PLATFORM_MOBILE:
                for platform in [PlatformService.PLATFORM_WEB,
                                 PlatformService.PLATFORM_ANDROID,
                                 PlatformService.PLATFORM_IOS]:
                    connections.append({'platform': platform, 'id': connection['id']})
                connections.pop(index)
                break
        return connections
