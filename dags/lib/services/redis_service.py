import redis
from typing import List
from airflow.hooks.base import BaseHook


class RedisService:
    def __init__(self, conn_id: str):
        rc = BaseHook.get_connection(conn_id)
        self.conn = redis.Redis(
            host=rc.host,
            port=rc.port,
            password=rc.password,
            db=int(rc.schema)
        )

    def ping(self) -> bool:
        return self.conn.ping()

    def get_keys(self, tpl: str = None) -> List:
        keys = self.conn.keys()
        result = []
        for key in keys:
            str_key = key.decode('UTF-8')
            if tpl is not None:
                if str_key.find(tpl) > -1:
                    result.append(str_key)
            else:
                result.append(str_key)
        return result

    def get_val(self, key: str):
        return self.conn.get(key)

