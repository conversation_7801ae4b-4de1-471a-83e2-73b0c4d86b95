from typing import Dict
import os
import csv


def csv_stream(file_path: str) -> Dict:
    try:
        if os.path.exists(file_path):
            with open(file_path, mode='r', encoding='utf-8') as csv_file:
                reader = csv.DictReader(csv_file)
                for row in reader:
                    yield row
    except Exception as e:
        raise
    finally:
        if os.path.exists(file_path):
            if not csv_file.closed:
                csv_file.close()
