from datetime import datetime, date, timedelta
import calendar


def get_current_date() -> date:
    return datetime.now().date()


def get_current_datetime() -> datetime:
    return datetime.combine(get_current_date(), datetime.now().time())


def get_year_str(dt: date) -> str:
    return str(dt.year)


def get_month_str(dt: date) -> str:
    return str(dt.month).zfill(2)


def get_day_str(dt: date) -> str:
    return str(dt.day).zfill(2)


def add_date_months(dt: date, count_months: int) -> date:
    date_result = dt
    for i in range(abs(count_months)):
        days_in_month = calendar.monthrange(date_result.year, date_result.month)[1]
        if count_months >= 0:
            date_result = date_result + timedelta(days=days_in_month)
        else:
            date_result = date_result - timedelta(days=days_in_month)
    return date_result


def get_date_previous_day() -> date:
    return datetime.combine(get_current_date() - timedelta(days=1), datetime.max.time())


def str_to_date_time(dt: str, fmt: str = '%Y-%m-%d') -> datetime:
    return datetime.strptime(dt, fmt)


def date_time_to_str(dt: datetime, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    return dt.strftime(fmt)


def get_max_date(dt1: str, dt2: str, fmt: str = '%Y-%m-%d') -> str:
    if str_to_date_time(dt1, fmt) > str_to_date_time(dt2, fmt):
        return dt1
    else:
        return dt2


def get_hours(td: timedelta) -> int:
    return (td.days * 86400 + td.seconds) // 3600
