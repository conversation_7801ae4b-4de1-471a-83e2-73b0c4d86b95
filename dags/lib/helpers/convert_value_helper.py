from datetime import datetime, date
import uuid
import pytz

timezone_default = pytz.timezone('UTC')
min_datetime_value = datetime(1970, 1, 1, 0, 0, 0, 0, timezone_default)

DEFAULT_VALUES = {
    int: -1,
    float: -1.0,
    str: '(null)',
    datetime: datetime.fromtimestamp(0, timezone_default),
    date: datetime.fromtimestamp(0, timezone_default).date(),
    bool: -1,
    bytes: uuid.UUID('00000000-0000-0000-0000-000000000000').bytes
}


def get_default_value(type_val):
    try:
        return DEFAULT_VALUES[type_val]
    except KeyError as e:
        return None


def get_converted_value(val, type_val):
    try:
        result = None
        if val is not None:
            if type_val == int:
                result = int(val)
            elif type_val == float:
                result = float(val)
            elif type_val == str:
                if str(val) != '':
                    if isinstance(val, datetime):
                        result = val.strftime('%Y-%m-%d %H:%M:%S')
                    elif str(val) != '(null)':
                        result = str(val)
                    else:
                        result = None
            elif type_val == datetime:
                if isinstance(val, int):
                    result = datetime.fromtimestamp(val, timezone_default)
                elif isinstance(val, str):
                    result = datetime.strptime(val, '%Y%m%dT%H%M%S%z').astimezone(timezone_default)
                elif isinstance(val, datetime):
                    result = val.astimezone(timezone_default)
                else:
                    result = None
                if result.date() < min_datetime_value.date():
                    raise Exception(f'Datetime out of range, result = {result}')
            elif type_val == date:
                if isinstance(val, int):
                    result = datetime.fromtimestamp(val, timezone_default).date()
                elif isinstance(val, str):
                    result = datetime.strptime(val, '%Y%m%dT%H%M%S%z').astimezone(timezone_default).date()
                result = datetime.fromtimestamp(val, timezone_default).date()
                if result < min_datetime_value.date():
                    raise Exception(f'Date out of range, result = {result}')
            elif type_val == bool:
                result = bool(val)
            elif type_val == bytes:
                result = uuid.UUID(val).bytes
            else:
                result = type_val(val)
    except Exception as e:
        raise Exception(
            f'Convertation error (source value = {val}, type = {type_val}), detailed information '
            f'about error: {str(e)}')
    finally:
        return result
