import pandas as pd
import numpy as np


def sanitize_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    dict_columns_to_rename = {}
    for column_name in df.columns:
        if column_name.find('#') != -1:
            dict_columns_to_rename[column_name] = column_name.replace('#', '')
        if df[column_name].dtype == 'object':
            df[column_name] = df[column_name].str.strip()
    df = df.rename(columns=dict_columns_to_rename).replace([''], [None]).replace([np.nan], [None])
    return df
