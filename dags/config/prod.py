from datetime import timedelta

DAG_ARGS = {
    'default_args': {
        'retries': 5,
        'retry_delay': timedelta(seconds=30)
    },
}

CONNECTIONS = {
    'redis_masters': [
        {'platform': 'vk', 'id': 'prod-vk_Redis_0'},
        {'platform': 'ok', 'id': 'prod-ok_Redis_0'},
        {'platform': 'mobile', 'id': 'prod-mobile_Redis_0'},
    ],
    'mysql_masters': [
        {'platform': 'vk', 'id': 'prod-vk_Mysql_0'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_0'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_0'},
    ],
    'mysql_shards': [
        {'platform': 'vk', 'id': 'prod-vk_Mysql_1'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_2'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_3'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_4'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_5'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_6'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_7'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_8'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_9'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_10'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_11'},
        {'platform': 'vk', 'id': 'prod-vk_Mysql_12'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_1'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_2'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_3'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_4'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_5'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_6'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_7'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_8'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_9'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_10'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_11'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_12'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_13'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_14'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_15'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_1'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_2'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_3'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_4'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_5'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_6'},

    ],
    'mysql_op_logs': [
        {'platform': 'vk', 'id': 'prod-vk_Mysql_100'},
        {'platform': 'ok', 'id': 'prod-ok_Mysql_100'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mysql_100'},
    ],
    'mongo_shards': [
        {'platform': 'vk', 'id': 'prod-vk_Mongo_1'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_2'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_3'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_4'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_5'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_6'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_7'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_8'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_9'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_10'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_11'},
        {'platform': 'vk', 'id': 'prod-vk_Mongo_12'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_1'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_2'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_3'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_4'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_5'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_6'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_7'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_8'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_9'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_10'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_11'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_12'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_13'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_14'},
        {'platform': 'ok', 'id': 'prod-ok_Mongo_15'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mongo_1'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mongo_2'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mongo_3'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mongo_4'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mongo_5'},
        {'platform': 'mobile', 'id': 'prod-mobile_Mongo_6'},
    ]
}

SETTINGS = {
    'tmp_files_folder': '/data',
    'events_files_folder': '/events/events-divided-airflow/elka2024-[platform]',
    'batch_size': 300000
}
