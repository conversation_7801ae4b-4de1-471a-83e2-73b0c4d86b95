from datetime import timedelta

DAG_ARGS = {
    'default_args': {
        'retries': 3,
        'retry_delay': timedelta(seconds=30)
    },
}

CONNECTIONS = {
    'redis_masters': [
        {'platform': 'vk', 'id': 'develop-vk_Redis_0'},
        {'platform': 'ok', 'id': 'develop-ok_Redis_0'},
        {'platform': 'mobile', 'id': 'develop-mobile_Redis_0'},
    ],
    'mysql_masters': [
        {'platform': 'vk', 'id': 'develop-vk_Mysql_0'},
        {'platform': 'ok', 'id': 'develop-ok_Mysql_0'},
        {'platform': 'mobile', 'id': 'develop-mobile_Mysql_0'},
    ],
    'mysql_shards': [
        {'platform': 'vk', 'id': 'develop-vk_Mysql_1'},
        {'platform': 'vk', 'id': 'develop-vk_Mysql_2'},
        {'platform': 'ok', 'id': 'develop-ok_Mysql_1'},
        {'platform': 'ok', 'id': 'develop-ok_Mysql_2'},
        {'platform': 'mobile', 'id': 'develop-mobile_Mysql_1'},
        {'platform': 'mobile', 'id': 'develop-mobile_Mysql_2'},

    ],
    'mysql_op_logs': [
        {'platform': 'vk', 'id': 'develop-vk_Mysql_100'},
        {'platform': 'ok', 'id': 'develop-ok_Mysql_100'},
        {'platform': 'mobile', 'id': 'develop-mobile_Mysql_100'},
    ],
    'mongo_shards': [
        {'platform': 'vk', 'id': 'develop-vk_Mongo_1'},
        {'platform': 'vk', 'id': 'develop-vk_Mongo_2'},
        {'platform': 'ok', 'id': 'develop-ok_Mongo_1'},
        {'platform': 'ok', 'id': 'develop-ok_Mongo_2'},
        {'platform': 'mobile', 'id': 'develop-mobile_Mongo_1'},
        {'platform': 'mobile', 'id': 'develop-mobile_Mongo_2'},
    ]
}

SETTINGS = {
    'tmp_files_folder': '/tmp',
    'events_files_folder': '/events/events-divided-airflow/elka2024-develop-[platform]',
    'batch_size': 10000
}

