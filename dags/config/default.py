from datetime import datetime, timedelta
from dags.lib.services.notification_service import NotificationService

CH_EXTRACT_STATE_TABLE = 'extract_state'

DAG_ARGS = {
    'default_args': {
        'owner': 'airflow',
        'depends_on_past': False,
        'start_date': datetime(2022, 8, 1),
        'provide_context': True,
        'retries': 0,
        'retry_delay': timedelta(seconds=5),
        'on_failure_callback': NotificationService.task_failure_alert
    },
    'catchup': False,
    'max_active_runs': 1
}

CONNECTIONS = {
    'clickhouse_main': 'Clickhouse_DB',
    'clickhouse_etl': 'Clickhouse_ETL_DB',
    'clickhouse_prev_year': 'Clickhouse_2023_DB',
    'aws_s3': 'AWS_Connect_Id',
    'slack': 'Slack_Connect_Id',
    'clickhouse_datamarts': 'Clickhouse_Datamarts_DB',
    'clickhouse_datamarts_etl': 'Clickhouse_Datamarts_ETL_DB',
    'google_big_query': 'Google_Big_Query',
    'redis_masters': [
        {'platform': 'vk', 'id': 'Redis_Master'}
    ],
    'mysql_masters': [
        {'platform': 'vk', 'id': 'MySQL_Master'},
    ],
    'mysql_shards': [
        {'platform': 'vk', 'id': 'MySQL_Shard1'},
        {'platform': 'vk', 'id': 'MySQL_Shard2'},
    ],
    'mysql_op_logs': [
        {'platform': 'vk', 'id': 'MySQL_Oplogs'}
    ],
    'mongo_shards': [
        {'platform': 'vk', 'id': 'Mongo_Shard1'},
        {'platform': 'vk', 'id': 'Mongo_Shard2'},
    ]
}

SETTINGS = {
    'tmp_files_folder': '/tmp',
    'events_files_folder': '/tmp/events',
    'batch_size': 100000
}
