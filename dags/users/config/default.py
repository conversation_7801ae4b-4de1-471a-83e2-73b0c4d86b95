from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load users information',
}

SETTINGS = {

}

CH_PERF_USERS_EXTRACTED_TABLE = 'mysql_perf_users_extracted'
CH_PERF_USERS_TABLE = 'mysql_perf_users'
CH_GAME_EXTRACTED_COLLECTION = 'mongo_game_extracted'
CH_USERS_TABLE = 'users_info'
CH_USERS_PREV_YEAR_TABLE = 'users_info2023'
CH_USERS_DYNAMICS_TABLE = 'users_info_dynamics'

MYSQL_PERF_USERS_TABLE = 'perf_users'

MONGO_GAME_COLLECTION = 'game'

TMP_PERF_USERS_FILE_PATH = 'perf_users_[connection].csv'
TMP_GAME_COLLECTION_FILE_PATH = 'game_collection_[connection].csv'

MONGO_GAME_COLLECTION_FIELDS = {
    '_id': {'ch_field': 'user_id'},
    '_sUserId': {'ch_field': 'user_id_platform'},
    '_lastUpdated': {'ch_field': '_update_time'},
    'installTime': {'ch_field': 'install_time'},
    'cSession.startMobilePlatform': {'ch_field': 'first_platform_type'},
    'cSession.platform': {'ch_field': 'platform_type'},
    'startScreen': {'ch_field': 'start_screen'},
    'buildInfo.mobileInstall': {'ch_field': 'install_build'},
    'buildInfo.mobileLast': {'ch_field': 'current_build'},
    'level': {'ch_field': 'level'},
    'friendsData.friendsCount': {'ch_field': 'friends_count'},
    'loginTime': {'ch_field': 'login_time'},
    'resource.energy': {'ch_field': 'energy'},
    'resource.cash': {'ch_field': 'cash'},
    'resource.factoryToken': {'ch_field': 'tokens'},
    'socialData.bdate': {'ch_field': 'birthdate'},
    'socialData.sex': {'ch_field': 'sex'},
    'socialData.city': {'ch_field': 'city'},
    'socialData.country': {'ch_field': 'country'},
    'socialData.email': {'ch_field': 'email'},
    'socialData.locale': {'ch_field': 'locale'},
    'socialData.last_name': {'ch_field': 'last_name'},
    'socialData.first_name': {'ch_field': 'first_name'},
    'viralRef.refSUserId': {'ch_field': 'referrer_user_id'},
    'viralRef.time': {'ch_field': 'referrer_user_time'},
    'geo.language': {'ch_field': 'geo_language'},
    'geo.country': {'ch_field': 'geo_country'},
    'geo.city': {'ch_field': 'geo_city'},
    'clan.id': {'ch_field': 'clan_id'},
    'clan.role': {'ch_field': 'clan_role'},
    'progress.points': {'ch_field': 'progress'},
    'merge.stage': {'ch_field': 'merge_level'},
    'lastPurchaseDate': {'ch_field': 'payer'},
    'mahjong.stage': {'ch_field': 'factory_level'},
    'gdpr.isRemoved': {'ch_field': 'gdpr_removed'},
    'platformAds.show': {'ch_field': 'no_ads'},
    'mahjong.mode.3.level': {'ch_field': 'passing_mode_level'},
    'mahjong.mode.1.gotTiles': {'ch_field': 'standard_mode_collection'},
    'mahjong.mode.2.gotTiles': {'ch_field': 'tournament_mode_collection'},
    'mahjong.mode.3.gotTiles': {'ch_field': 'passing_mode_collection'},
    'mahjong.mode.1.tiles': {'ch_field': 'standard_mode_collection_number'},
    'mahjong.mode.2.tiles': {'ch_field': 'tournament_mode_collection_number'},
    'mahjong.mode.3.tiles': {'ch_field': 'passing_mode_collection_number'},
    'achievement.1': {'ch_field': 'progress_level'},
    'isGuest': {'ch_field': 'guest'},
}

SQL_JOIN_TO_USERS_INFO = f'''
    INSERT INTO users_info (_connection_id, _platform, _update_time, platform_type, user_id, user_id_platform, 
        install_time, payer, start_screen, login_time, level, progress, progress_level,
        factory_level, merge_level, cash, energy, install_build, current_build, 
        birthdate, sex, city, country, email, locale, 
        friends_count, referrer_user_id, referrer_user_time, clan_id, clan_role, 
        geo_language, geo_country, geo_city, last_name, first_name, first_platform_type, gdpr_removed,
        no_ads, tokens, passing_mode_level, 
        standard_mode_collection, tournament_mode_collection, passing_mode_collection,
        standard_mode_collection_number, tournament_mode_collection_number, passing_mode_collection_number,
        guest, 
        referrer, referrer_data, old_user_id, old_user_platform)
    SELECT
        g._connection_id, g._platform, g._update_time, g.platform_type, g.user_id, g.user_id_platform, 
        g.install_time, g.payer, g.start_screen, g.login_time, g.level, g.progress, g.progress_level,
        g.factory_level, g.merge_level, g.cash, g.energy, g.install_build, g.current_build, 
        g.birthdate, g.sex, g.city, g.country, g.email, g.locale, 
        g.friends_count, g.referrer_user_id, g.referrer_user_time, g.clan_id, g.clan_role, 
        g.geo_language, g.geo_country, g.geo_city, g.last_name, g.first_name, g.first_platform_type, g.gdpr_removed,
        g.no_ads, g.tokens, g.passing_mode_level, 
        g.standard_mode_collection, g.tournament_mode_collection, g.passing_mode_collection,
        g.standard_mode_collection_number, g.tournament_mode_collection_number, g.passing_mode_collection_number,
        g.guest,
        pu.referrer, pu.referrer_data, u_prev.user_id, if(g._platform != 'mobile', g._platform, NULL)
    FROM
        {CH_GAME_EXTRACTED_COLLECTION} AS g 
            LEFT JOIN [schema].{CH_USERS_PREV_YEAR_TABLE} u_prev
            ON g._platform = u_prev.platform_type AND g.user_id_platform = u_prev.user_id_platform, 
        {CH_PERF_USERS_TABLE} AS pu
    WHERE g._platform = pu._platform AND g.user_id = pu.user_id;
'''

SQL_JOIN_TO_USERS_INFO_DYNAMICS = f'''
    INSERT INTO users_info_dynamics (_connection_id, _platform, _update_time, platform_type, user_id, user_id_platform, 
        install_time, payer, start_screen, login_time, level, progress, progress_level,
        factory_level, merge_level, cash, energy, install_build, current_build, 
        birthdate, sex, city, country, email, locale, 
        friends_count, referrer_user_id, referrer_user_time, clan_id, clan_role, 
        geo_language, geo_country, geo_city, last_name, first_name, first_platform_type, gdpr_removed,
        no_ads, tokens, passing_mode_level, 
        standard_mode_collection, tournament_mode_collection, passing_mode_collection,
        standard_mode_collection_number, tournament_mode_collection_number, passing_mode_collection_number,
        guest, 
        referrer, referrer_data, old_user_id, old_user_platform)
    SELECT
        g._connection_id, g._platform, g._update_time, g.platform_type, g.user_id, g.user_id_platform, 
        g.install_time, g.payer, g.start_screen, g.login_time, g.level, g.progress, g.progress_level,
        g.factory_level, g.merge_level, g.cash, g.energy, g.install_build, g.current_build, 
        g.birthdate, g.sex, g.city, g.country, g.email, g.locale, 
        g.friends_count, g.referrer_user_id, g.referrer_user_time, g.clan_id, g.clan_role, 
        g.geo_language, g.geo_country, g.geo_city, g.last_name, g.first_name, g.first_platform_type, g.gdpr_removed,
        g.no_ads, g.tokens, g.passing_mode_level, 
        g.standard_mode_collection, g.tournament_mode_collection, g.passing_mode_collection,
        g.standard_mode_collection_number, g.tournament_mode_collection_number, g.passing_mode_collection_number,
        g.guest,
        pu.referrer, pu.referrer_data, u_prev.user_id, if(g._platform != 'mobile', g._platform, NULL)
    FROM
        {CH_GAME_EXTRACTED_COLLECTION} AS g 
            LEFT JOIN [schema].{CH_USERS_PREV_YEAR_TABLE} u_prev
            ON g._platform = u_prev.platform_type AND g.user_id_platform = u_prev.user_id_platform, 
        {CH_PERF_USERS_TABLE} AS pu
    WHERE g._platform = pu._platform AND g.user_id = pu.user_id;
'''

SQL_EXTRACT_LOAD_USERS_PREV_YEAR = '''
INSERT INTO {{ table_target }} (platform_type, first_platform_type, user_id, user_id_platform, start_screen, install_time, login_time, level, progress, cash, energy, install_build, current_build, birthdate, sex, city, country, email, locale, friends_count, referrer, referrer_data, referrer_user_id, referrer_user_time, clan_id, clan_role, geo_language, geo_country, geo_city, last_name, first_name)
{% for platform_type, user_id in maxs.items() %}
    SELECT platform_type, first_platform_type, user_id, user_id_platform, start_screen, install_time, login_time, level, progress, cash, energy, install_build, current_build, birthdate, sex, city, country, email, locale, friends_count, referrer, referrer_data, referrer_user_id, referrer_user_time, clan_id, clan_role, geo_language, geo_country, geo_city, last_name, first_name
        FROM remote('{{ host }}', {{ schema }}.{{ table }}, '{{ user }}', '{{ password }}')
        WHERE platform_type = '{{ platform_type }}' AND user_id > {{ user_id }}
    {% if platform_type != last %}
        UNION ALL
    {% endif %}
{% endfor %}
'''
