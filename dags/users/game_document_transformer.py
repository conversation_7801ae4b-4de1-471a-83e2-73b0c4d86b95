from typing import Dict
from dags.lib.services.platform_service import PlatformService
import dags.users.config.default as dag_config
from math import floor

# Можно сделать базовый класс?
class GameDocumentTransformer:

    @staticmethod
    def __set_fields(fields_config: Dict, document: Dict):
        transformed_document = {}
        for key, config in fields_config.items():
            parts = key.split('.')
            value = document[parts.pop(0)] if parts[0] in document else None
            if value is not None:
                for part in parts:
                    if (part not in value):
                        value = None
                        break
                    value = value[part]
            transformed_document[config['ch_field']] = value
        return transformed_document

    @staticmethod
    def get_tiles_number(tiles):
        n = 0
        if tiles is None or not isinstance(tiles, list):
            return n
        for tile in tiles:
            if tile['state'] in [2, 3]:
                n += 1
        return n

    @staticmethod
    def process(document: Dict, connection_config: Dict) -> Dict:
        transformed_document = GameDocumentTransformer.__set_fields(dag_config.MONGO_GAME_COLLECTION_FIELDS, document)
        transformed_document['_connection_id'] = connection_config['id']
        transformed_document['_platform'] = connection_config['platform']
        transformed_document['platform_type'] = (
            PlatformService.get_name(transformed_document['platform_type'])
            if connection_config['platform'] == 'mobile'
            else connection_config['platform']
        )
        transformed_document['first_platform_type'] = \
            PlatformService.get_name(transformed_document['first_platform_type'])
        transformed_document['payer'] = 1 if transformed_document['payer'] is not None else 0
        transformed_document['gdpr_removed'] = transformed_document['gdpr_removed'] \
            if transformed_document['gdpr_removed'] is not None else 0
        transformed_document['no_ads'] = transformed_document['no_ads'] \
            if transformed_document['no_ads'] is not None else 0

        transformed_document['standard_mode_collection'] = int(floor(
            transformed_document['standard_mode_collection'] / 45)) \
            if transformed_document['standard_mode_collection'] is not None else 0
        transformed_document['tournament_mode_collection'] = int(floor(
            transformed_document['tournament_mode_collection'] / 45)) \
            if transformed_document['tournament_mode_collection'] is not None else 0
        transformed_document['passing_mode_collection'] = int(floor(
            transformed_document['passing_mode_collection'] / 45)) \
            if transformed_document['passing_mode_collection'] is not None else 0

        transformed_document['standard_mode_collection_number'] = \
            GameDocumentTransformer.get_tiles_number(transformed_document['standard_mode_collection_number'])
        transformed_document['tournament_mode_collection_number'] = \
            GameDocumentTransformer.get_tiles_number(transformed_document['tournament_mode_collection_number'])
        transformed_document['passing_mode_collection_number'] = \
            GameDocumentTransformer.get_tiles_number(transformed_document['passing_mode_collection_number'])

        transformed_document['progress_level'] = (transformed_document['progress_level'][1] + 1) \
            if transformed_document['progress_level'] is not None else 0

        return transformed_document
