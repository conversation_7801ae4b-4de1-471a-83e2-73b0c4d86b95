from typing import Dict
import os
import csv
from airflow import DAG
from airflow.hooks.base import <PERSON><PERSON>ook
from airflow.operators.python import PythonOperator
from dags.lib.services.mysql_service import MySQLService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from airflow.providers.mongo.hooks.mongo import MongoHook
from dags.lib.services.platform_service import PlatformService
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
import dags.users.config.default as dag_config
from dags.users.perf_users_row_transformer import PerfUsersRowTransformer
from dags.users.game_document_transformer import GameDocumentTransformer
import jinja2
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.lib.services.file_path_service import FilePathService

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_PERF_USERS_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_MASTERS),
        dag_config.TMP_GAME_COLLECTION_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS)
    }
)


def clear_temp_data():

    LoggerService.logger.info(f'Env: {ENV}')

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_GAME_EXTRACTED_COLLECTION)

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_PERF_USERS_EXTRACTED_TABLE)

    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def stream_perf_users(connection_config: Dict):
    mysql_connection = connection_config['id']
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    max = ch.get_max_column_value(
        dag_config.CH_PERF_USERS_TABLE,
        'user_id',
        {'_platform': connection_config['platform']}
    )
    # TODO: Есть ли смысл выносить тексты SQL-запросов в отдельные файлы?
    # Ninja templates?
    sql = f'''
        SELECT user_id, user_id_platform, install_time, referrer, referrer_data
        FROM {dag_config.MYSQL_PERF_USERS_TABLE}  
        WHERE user_id > {max} 
    '''
    ms = MySQLService(mysql_connection)
    for row in ms.stream(sql):
        yield row


def extract_perf_users(connection_config: Dict):
    counter = 0
    path = file_paths.get_path(connection_config['id'], dag_config.TMP_PERF_USERS_FILE_PATH)
    path_header = file_paths.get_header_path(dag_config.TMP_PERF_USERS_FILE_PATH)

    gen = stream_perf_users(connection_config)
    try:
        row = PerfUsersRowTransformer.process(next(gen), connection_config)
    except StopIteration:
        return

    # TODO: Скорей всего будем использовать формат Parquet
    if not os.path.exists(path_header):
        with open(path_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, row.keys(), delimiter=';')
            writer.writeheader()

    with open(path, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, row.keys(), delimiter=';')
        writer.writerow(row)
        counter += 1
        for row in gen:
            writer.writerow(PerfUsersRowTransformer.process(row, connection_config))
            counter += 1

    # Писать статистику в базу
    LoggerService.finish_extracting(counter)


def load_perf_users():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.execute(f'''
        INSERT INTO {dag_config.CH_PERF_USERS_TABLE} (
            _connection_id, _platform, user_id, user_id_platform, install_time, referrer, referrer_data)
        SELECT _connection_id, _platform, user_id, user_id_platform, install_time, referrer, referrer_data
        FROM {dag_config.CH_PERF_USERS_EXTRACTED_TABLE}
    ''')


def stream_game_collection(connection_config: Dict):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    max = ch.get_max_column_value(
        dag_config.CH_USERS_TABLE,
        '_update_time',
        {'_connection_id': connection_config['id'], '_platform': connection_config['platform']}
    )
    LoggerService.logger.info(f'Max id is: {max}')
    connection = BaseHook.get_connection(connection_config['id'])
    documents_count = 0
    with MongoHook(connection_config['id']).get_conn() as mongo:
        collection = mongo.get_database(connection.schema).get_collection(dag_config.MONGO_GAME_COLLECTION)
        with collection.find(
                {'$or': [
                    {'_lastUpdated': {'$exists': False}},
                    {'_lastUpdated': {'$gt': max}},
                    {'$and':[{'_lastUpdated': {'$lt': 'installTime'}}, {'installTime': {'$gt': max}}]}
                ]},
                {k: 1 for k, v in dag_config.MONGO_GAME_COLLECTION_FIELDS.items()}
        ) as cursor:
            for document in cursor:
                yield document
                documents_count += 1
    LoggerService.logger.info(f'Documents count: {documents_count}')


def extract_game_collection(connection_config: Dict):
    path = file_paths.get_path(connection_config['id'], dag_config.TMP_GAME_COLLECTION_FILE_PATH)
    path_header = file_paths.get_header_path(dag_config.TMP_GAME_COLLECTION_FILE_PATH)

    gen = stream_game_collection(connection_config)
    try:
        row = GameDocumentTransformer.process(next(gen), connection_config)
    except StopIteration:
        return

    # TODO: Скорей всего будем использовать формат Parquet
    if not os.path.exists(path_header):
        with open(path_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, row.keys(), delimiter=';')
            writer.writeheader()

    counter = 0
    with open(path, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, row.keys(), delimiter=';')
        writer.writerow(row)
        counter += 1
        for row in gen:
            writer.writerow(GameDocumentTransformer.process(row, connection_config))
            counter += 1

    # Писать статистику в базу
    LoggerService.finish_extracting(counter)


def load_users_prev_year():
    if ENV == EnvironmentService.DEVELOP:
        return
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    maxs = ch.get_max_column_values(
        dag_config.CH_USERS_PREV_YEAR_TABLE,
        'user_id',
        'platform_type'
    )
    platforms = PlatformService.get_names(PlatformService.expand_mobile_connection(config.get_connections(ConfigService.CONN_MYSQL_MASTERS)))
    for platform in platforms:
        if platform not in maxs:
            maxs[platform] = 0
    environment = jinja2.Environment()
    template = environment.from_string(dag_config.SQL_EXTRACT_LOAD_USERS_PREV_YEAR)
    conn = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_PREV_YEAR))
    sql = template.render(
        host=conn.host,
        schema=conn.schema,
        table=dag_config.CH_USERS_TABLE,
        table_target=dag_config.CH_USERS_PREV_YEAR_TABLE,
        user=conn.login,
        password=conn.password,
        maxs=maxs,
        last=list(maxs.keys())[-1]
    )
    LoggerService.logger.info(sql)
    ch.execute(sql)


def join_users():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    conn_main = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    ch.execute(dag_config.SQL_JOIN_TO_USERS_INFO.replace('[schema]', conn_main.schema))

    # Temp
    ch.execute(dag_config.SQL_JOIN_TO_USERS_INFO_DYNAMICS.replace('[schema]', conn_main.schema))


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data)

    extract_perf_users_tasks = [
        PythonOperator(
            task_id=f'extract_perf_users_{row["id"]}',
            python_callable=extract_perf_users,
            op_kwargs={
                'connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MYSQL_MASTERS)
    ]

    concat_perf_users_files_task = ConcatCSVOperator(
        task_id='concat_perf_users_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_PERF_USERS_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MYSQL_MASTERS)
    )

    import_perf_users_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_PERF_USERS_FILE_PATH),
        table_name=dag_config.CH_PERF_USERS_EXTRACTED_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_perf_users'
    )

    load_perf_users_task = PythonOperator(
        task_id=f'load_perf_users',
        python_callable=load_perf_users,
    )

    extract_game_collection_tasks = [
        PythonOperator(
            task_id=f'extract_mongo_collection_{row["id"]}',
            python_callable=extract_game_collection,
            op_kwargs={
                'connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MONGO_SHARDS)
    ]

    concat_game_files_task = ConcatCSVOperator(
        task_id='concat_game_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_GAME_COLLECTION_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS)
    )

    import_game_collection_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_GAME_COLLECTION_FILE_PATH),
        table_name=dag_config.CH_GAME_EXTRACTED_COLLECTION,
        data_type=CSV_DATA_TYPE,
        task_id=f'load_game_collection',
    )

    # load_users_prev_year_task = PythonOperator(
    #     task_id=f'load_users_prev_year',
    #     python_callable=load_users_prev_year,
    # )

    join_users_task = PythonOperator(
        task_id=f'join_users',
        python_callable=join_users,
    )

    clear_temp_data_task >> extract_perf_users_tasks >> concat_perf_users_files_task >> import_perf_users_task >> \
    load_perf_users_task >> join_users_task

    clear_temp_data_task >> extract_game_collection_tasks >> concat_game_files_task >> import_game_collection_task >> \
    join_users_task

if __name__ == '__main__':
    dag.cli()
