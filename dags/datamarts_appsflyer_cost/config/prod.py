from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(minutes=60),
}

SETTINGS = {
    'applications': [
            # days_per_run - данные за сколько дней выгружаем за один запуск DAG
            {
                'app_id': 'id6446766326',
                'app_id_human': 'Goblins Wood iOS',
                'days_per_run': 3,
                'date_start': '2024-03-01'
            },
            {
                'app_id': 'idle.goblins.wood.tycoon',
                'app_id_human': 'Goblins Wood Android',
                'days_per_run': 3,
                'date_start': '2024-03-01'
            },
            {
                'app_id': 'com.progameslab.magic.seasons2024.farm.match.collect',
                'app_id_human': 'Magic Seasons 2024 Android',
                'days_per_run': 3,
                'date_start': '2024-03-01'
            },
            {
                'app_id': 'id6463635391',
                'app_id_human': 'Magic Seasons 2024 iOS',
                'days_per_run': 3,
                'date_start': '2024-03-01'
            },
            {
                'app_id': 'com.progameslab.tile.master.triple.puzzle.match.travel.farm.adventure',
                'app_id_human': 'Magic Seasons 2025 Android',
                'days_per_run': 3,
                'date_start': '2024-08-23'
            },
            {
                'app_id': 'id6504757972',
                'app_id_human': 'Magic Seasons 2025 iOS',
                'days_per_run': 3,
                'date_start': '2024-08-23'
            },
            {
                'app_id': 'com.progameslab.gold.zombies.titans.idle.tycoon.merge.action.rpg.clicker',
                'app_id_human': 'Gold and Zombies Android',
                'days_per_run': 1,
                'date_start': '2025-01-10'
            },
            {
                'app_id': 'com.goblins.idle.merge.game',
                'app_id_human': 'Goblin Miner Android',
                'days_per_run': 3,
                'date_start': '2025-05-23'
            },
        ]
}
