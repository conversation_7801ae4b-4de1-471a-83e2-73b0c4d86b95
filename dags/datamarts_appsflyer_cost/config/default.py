from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load etl cost raw data from AWS S3',
}

SETTINGS = {
    'bucket_name': 'af-xpend-cost-etl-acc-yxaoxwwv-progameslab',
    'bucket_path': 'cost_etl/v1/dt={date}',
    'applications': [
        {
            'app_id': 'id6446766326',
            'app_id_human': 'Goblins Wood iOS',
        },
        {
            'app_id': 'idle.goblins.wood.tycoon',
            'app_id_human': 'Goblins Wood Android',
        },
        {
            'app_id': 'com.progameslab.magic.seasons2024.farm.match.collect',
            'app_id_human': 'Magic Seasons 2024 Android',
        },
        {
            'app_id': 'id6463635391',
            'app_id_human': 'Magic Seasons 2024 iOS',
        },
        {
            'app_id': 'com.progameslab.my.cooking.merge.kitchen.madness.love.fever.travel.town.idle',
            'app_id_human': 'My Cooking Merge Android',
        },
    ]
}

DATE_START = '2024-03-01'
# данные за сколько дней выгружаем за один запуск DAG
DAYS_PER_RUN = 1

APPLICATIONS_KEY = 'applications'
OBJECTS_LIST_PAGE_SIZE = 1024
BUCKET_PATH_KEY = 'bucket_path'
BUCKET_NAME_KEY = 'bucket_name'

# Выгружаем из S3 только файлы, которые созданы час назад минимум. Чтобы защититься от кейса, когда Appsflyer
# еще продолжает создавать файлы в папке за очередной день
# Также опираемся здесь на то, что файлы за день появляются в папке с разницей в секунды. Час - перестраховка с
# запасом
# (Да, можно было бы определять этот по наличию файла _SUCCESS, но в случае с другими данными Appsflyer - таких файлов
# в папках нет)
LAST_MODIFIED_GAP_SECONDS = 3600

CH_COST_TABLE = 'appsflyer_cost'
CH_COST_EXTRACTED_TABLE = 'appsflyer_cost_extracted'

TMP_COST_EXTRACT_STATE_FILE_PATH = 'datamarts_appsflyer_cost_es_[connection].csv'