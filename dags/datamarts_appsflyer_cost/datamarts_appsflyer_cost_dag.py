from typing import Dict
import io
import os
import re
import csv
import pandas as pd
from airflow.exceptions import AirflowSkipException
from datetime import datetime, timezone, timedelta, time
from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService
from dags.lib.services.extract_state_service import ExtractStateService
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from airflow.hooks.base import BaseHook
import dags.datamarts_appsflyer_cost.config.default as dag_config
from dags.lib.helpers.date_helper import str_to_date_time, get_current_date
from dags.datamarts_appsflyer_cost.appsflyer_cost_df_transformer import AppsflyerCostDfTransformer
from dags.lib.services.file_path_service import FilePathService
from dags.lib.operators.data_to_clickhouse_operator import <PERSON>To<PERSON><PERSON><PERSON>ouseOperator, CSV_DATA_TYPE
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_COST_EXTRACT_STATE_FILE_PATH: [],
    }
)

def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    ch.truncate(dag_config.CH_COST_EXTRACTED_TABLE)

    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)

def get_source_name() -> str:
    return f'appsflyer_cost_etl'

def get_dates(connection_id: str) -> (datetime, datetime, list):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    extract_state = ExtractStateService(ch)
    date_start = str_to_date_time(dag_config.DATE_START).replace(tzinfo=timezone.utc)

    date_max = extract_state.get_last_value(
        connection_id,
        get_source_name(),
        'date',
        ExtractStateService.DATE_TYPE
    ).replace(tzinfo=timezone.utc)
    date_max_saved = date_max

    # Пока нам не нужно выгружать исторические данные
    # date_min = extract_state.get_last_value(
    #     connection_id,
    #     get_source_name(),
    #     'date_min',
    #     ExtractStateService.DATE_TYPE
    # ).replace(tzinfo=timezone.utc)
    date_min = date_start
    date_min_saved = date_min

    date_today_midnight = datetime.combine(datetime.now().date(), time.min).replace(tzinfo=timezone.utc)
    if date_max < date_start:
        date_max = date_today_midnight - timedelta(days=(dag_config.DAYS_PER_RUN - 1))
    LoggerService.logger.info(f'Date max is {date_max}')

    # Кроме свежих данных пытаемся также выгружать исторические, поэтому две даты - min и max, от которых
    # происходят расчеты в будущее (чаще всего это будет 1 ближайший день) и прошлое
    dates = [date_max]
    date_max = datetime.combine(date_max.date(), time.min).replace(tzinfo=timezone.utc)
    for _ in range(dag_config.DAYS_PER_RUN):
        date_max += timedelta(days=1)
        if date_max >= date_today_midnight:
            break
        LoggerService.logger.info(f'Append {date_max}')
        dates.append(date_max)


    # if len(dates) < application_config['days_per_run'] and date_min > date_start:
    #     for _ in range(application_config['days_per_run'] - len(dates)):
    #         date_min -= timedelta(days=1)
    #         LoggerService.logger.info(f'Append to past {date_min}')
    #         dates.append(date_min)
    # Кейс самого первого запуска
    # if date_min_saved < date_start:
    #     date_min_saved = date_max + timedelta(days=1)
    LoggerService.logger.info(f'Dates:')
    LoggerService.logger.info(dates)
    return (date_max_saved, date_min_saved, dates)

def get_batch_number(file_name: str) -> int:
    return int(re.findall(r'b=(\d{1})', file_name)[0])

def extract_data():
    bucket_name = config.get_setting(dag_config.BUCKET_NAME_KEY)
    bucket_path = config.get_setting(dag_config.BUCKET_PATH_KEY)
    connection_id = config.get_connection(ConfigService.CONN_AWS_S3)
    s3_hook = S3Hook(aws_conn_id=connection_id)
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    extract_state = []
    now = datetime.now().replace(tzinfo=timezone.utc)

    (date_max_saved, date_min_saved, dates) = get_dates(connection_id)
    date_max = date_max_saved
    date_min = date_min_saved
    for date in dates:
        path = bucket_path.format(date=date.strftime('%Y-%m-%d'))
        parts = s3_hook.list_keys(bucket_name, prefix=path, page_size=dag_config.OBJECTS_LIST_PAGE_SIZE)
        LoggerService.logger.info(f'Load directory {path}')

        counter = 0

        # Если batch #4 есть в папке, значит последняя пачка файлов уже появилась в бакете. После этого можно
        # считать, что все файлы за текущий день обработаны
        b4_found = False
        for part in parts:
            if part.find('.parquet') == -1:
                # В папках есть файл без расширения - _SUCCESS
                continue

            # В течение дня файлы в папке появляются 4 раза
            obj = s3_hook.get_key(bucket_name=bucket_name, key=part)
            if (now - obj.last_modified).total_seconds() < dag_config.LAST_MODIFIED_GAP_SECONDS:
                continue
            if obj.last_modified <= date_max_saved:
                # Значит уже загружали этот файл
                if not b4_found:
                    b4_found = (get_batch_number(part) == 4)
                continue
            date = obj.last_modified

            buffer = io.BytesIO()
            obj.download_fileobj(buffer)
            df = AppsflyerCostDfTransformer.process(pd.read_parquet(buffer), config, part)
            ch.insert_dataframe(df=df, table_name=dag_config.CH_COST_EXTRACTED_TABLE)
            LoggerService.logger.info(f'Data from file {part} was loaded, {len(df.index)} rows')
            counter += 1

        LoggerService.logger.info(f'Counter: {counter}, b4_found: {b4_found}')
        if (counter > 0):
            date_max = max(date_max, date)
            date_min = min(date_min, date)
        elif b4_found:
            # Переводим дату на следующие сутки, время 00:00
            date_max = datetime.combine((date_max + timedelta(days=1)).date(), time.min).replace(tzinfo=timezone.utc)

    if date_max > date_max_saved:
        extract_state.append({
            'connection_id': connection_id,
            'source_name': get_source_name(),
            'key_name': 'date',
            'last_value_date': date_max.replace(tzinfo=None)
        })
    if date_min < date_min_saved:
        extract_state.append({
            'connection_id': connection_id,
            'source_name': get_source_name(),
            'key_name': 'date_min',
            'last_value_date': date_min.replace(tzinfo=None)
        })

    if len(extract_state) > 0:
        file_path_state = file_paths.get_result_path(dag_config.TMP_COST_EXTRACT_STATE_FILE_PATH)
        with open(file_path_state, 'w', newline='', encoding='utf-8') as file:
            header = extract_state[0].keys()
            writer = csv.DictWriter(file, header, delimiter=';')
            writer.writeheader()
            writer.writerows(extract_state)
    else:
        raise AirflowSkipException


def move_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL))
    ch.execute(f'INSERT INTO {dag_config.CH_COST_TABLE} SELECT * FROM {dag_config.CH_COST_EXTRACTED_TABLE}')

dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_files_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    extract_data_task = PythonOperator(
        task_id='extract_data',
        python_callable=extract_data
    )

    move_data_task = PythonOperator(
        task_id='move_data',
        python_callable=move_data
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_DATAMARTS_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_COST_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_es'
    )

    clear_temp_files_task >> extract_data_task >> move_data_task >> import_extract_state_task

if __name__ == '__main__':
    dag.cli()
