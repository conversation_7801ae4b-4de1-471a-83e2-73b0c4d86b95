import pandas as pd
from typing import Dict
from datetime import datetime
import dags.datamarts_appsflyer_cost.config.default as dag_config
from dags.lib.services.config_service import ConfigService

class AppsflyerCostDfTransformer:
    @staticmethod
    def process(df: pd.DataFrame, config: ConfigService, file_name: str):

        pd.options.mode.chained_assignment = None

        app_ids_map = {x['app_id']:x['app_id_human'] for x in config.get_setting(dag_config.APPLICATIONS_KEY)}
        df = df[df['app_id'].isin(app_ids_map.keys())]

        # Оставляем в дата-фрейме только интересующие нас app_id

        # При переводе аккаунта на новое юр. лицо немного сменились настройки экспорта, появились новые колонки
        for column in ['cost_without_fees', 'original_cost_without_fees', 'fees', 'os']:
            if column in df.columns:
                df.drop(
                    column,
                    axis=1,
                    inplace=True
                )

        df.replace(
            {
                'date': {'None': ''},
                'campaign_id': {'None': ''},
                'campaign': {'None': ''},
                'geo': {'None': ''},
                'media_source': {'None': ''},
                'adset_id': {'None': ''},
                'adset': {'None': ''},
                'ad_id': {'None': ''},
                'ad': {'None': ''},
                'channel': {'None': ''},
                'site_id': {'None': ''},
                'agency': {'None': ''}
            },
            inplace=True
        )
        df.rename({'keyword_term': 'keywords'}, axis=1, inplace=True)
        df['app_id_human'] = df['app_id'].apply(lambda x: app_ids_map[x])
        df['date'] = df['date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d').date())
        df['filename'] = file_name
        return df
