from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load users matchmaking information',
}

SETTINGS = {}

# ID ивента
EVENT_MONOPOLY_ID = 2
EVENT_DOORS_ID = 3
EVENT_TIME_MANAGEMENT_ID = 5

CH_USERS_MATCHMAKING_EXTRACTED_TABLE = 'users_matchmaking_extracted'
CH_USERS_MATCHMAKING_TABLE = 'users_matchmaking'

MONGO_GAME_COLLECTION = 'game'

TMP_USERS_MATCHMAKING_COLLECTION_FILE_PATH = 'users_matchmaking_collection_[connection].parquet'
TMP_USERS_MATCHMAKING_EXTRACT_STATE_FILE_PATH = 'users_matchmaking_es_[connection].csv'

MONGO_GAME_COLLECTION_FIELDS = {
    '_id': {'ch_field': 'user_id'},
    'progress.points': {'ch_field': 'progress'},

    'events.monopoly.startTime': {'ch_field': 'date'},
    'events.monopoly.rating.users': {'ch_field': 'users'},
    'events.monopoly.seasonId': {'ch_field': 'season_id'},

    'events.doors.startTime': {'ch_field': 'date'},
    'events.doors.rating.users': {'ch_field': 'users'},
    'events.doors.seasonId': {'ch_field': 'season_id'},

    'events.trade.startTime': {'ch_field': 'date'},
    'events.trade.rating.users': {'ch_field': 'users'},
    'events.trade.seasonId': {'ch_field': 'season_id'},
}

FIELDS_MAP = {
    EVENT_MONOPOLY_ID: ['_id', 'progress.points', 'events.monopoly.startTime', 'events.monopoly.rating.users', 'events.monopoly.seasonId'],
    EVENT_DOORS_ID: ['_id', 'progress.points', 'events.doors.startTime', 'events.doors.rating.users', 'events.doors.seasonId'],
    EVENT_TIME_MANAGEMENT_ID: ['_id', 'progress.points', 'events.trade.startTime', 'events.trade.rating.users', 'events.trade.seasonId'],
}
