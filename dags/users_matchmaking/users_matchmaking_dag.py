import os
from typing import Dict
from glob import glob
import pandas as pd
import csv
from airflow import DAG
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.providers.mongo.hooks.mongo import MongoHook
from dags.lib.services.clickhouse_service import ClickhouseService, PARQUET_DATA_TYPE, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.file_path_service import FilePathService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.users_matchmaking.users_matchmaking_row_transformer import UsersMatchmakingRowTransformer
import dags.users_matchmaking.config.default as dag_config
from dags.config.default import CH_EXTRACT_STATE_TABLE

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_USERS_MATCHMAKING_COLLECTION_FILE_PATH:
            config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS),
        dag_config.TMP_USERS_MATCHMAKING_EXTRACT_STATE_FILE_PATH:
            config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS)
    }
)


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_USERS_MATCHMAKING_EXTRACTED_TABLE)

    for path in glob(file_paths.get_glob_pattern()):
        if os.path.exists(path):
            LoggerService.logger.info(f'Removing {path}')
            os.remove(path)


def get_max_update_time(connection_config: Dict) -> int:
    try:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        extract_state = ExtractStateService(ch)
        extract_state_key = f'update_datetime_{connection_config["platform"]}'
        return extract_state.get_last_value(
            connection_config['id'],
            dag_config.MONGO_GAME_COLLECTION,
            extract_state_key
        )
    except Exception as e:
        LoggerService.logger.error(f'Error get max date update of users from ClickHouse database, see detailed '
                                   f'information: {str(e)}')
        raise


def stream_matchmaking_data(connection_config: Dict):
    try:
        max_date_time = get_max_update_time(connection_config)
        connection = BaseHook.get_connection(connection_config['id'])
        documents_count = 0
        with MongoHook(connection_config['id']).get_conn() as mongo:
            collection = mongo.get_database(connection.schema).get_collection(dag_config.MONGO_GAME_COLLECTION)
            with collection.find(
                    {'$and': [
                        {'$or': [
                            {'$and': [
                                {'events.monopoly.startTime': {'$exists': True}},
                                {'events.monopoly.startTime': {'$gt': max_date_time}},
                                {'events.monopoly.rating.init': 1},
                            ]},
                            {'$and': [
                                {'events.doors.startTime': {'$exists': True}},
                                {'events.doors.startTime': {'$gt': max_date_time}},
                                {'events.doors.rating.init': 1},
                            ]},
                            {'$and': [
                                {'events.trade.startTime': {'$exists': True}},
                                {'events.trade.startTime': {'$gt': max_date_time}},
                                {'events.trade.rating.init': 1},
                            ]}
                        ]},
                        {'_lastUpdated': {'$gt': max_date_time}}
                    ]},
                    {k: 1 for k in dag_config.MONGO_GAME_COLLECTION_FIELDS.keys()}
            ) as cursor:
                for document in cursor:
                    # Логика основывается на том, что нам заведомо известно, что в один и тот же момент времени активен
                    # только один ивент
                    document['event_id'] = dag_config.EVENT_DOORS_ID
                    if ('monopoly' in document['events']) and (document['events']['monopoly']['startTime'] > max_date_time):
                        document['event_id'] = dag_config.EVENT_MONOPOLY_ID
                    elif ('trade' in document['events']) and (document['events']['trade']['startTime'] > max_date_time):
                        document['event_id'] = dag_config.EVENT_TIME_MANAGEMENT_ID

                    yield document
                    documents_count += 1
        LoggerService.logger.info(f'Mongo documents count: {documents_count}')
    except Exception as e:
        LoggerService.logger.error(f'Error get data, see detailed information: {str(e)}')
        raise


def extract_matchmaking_data(connection_config: Dict):
    LoggerService.start_extracting(
        connection_config['platform'],
        connection_config['id'],
        dag_config.MONGO_GAME_COLLECTION
    )
    try:
        counter = 0
        max_date_mongo = 0
        data_matchmaking = []
        for row in stream_matchmaking_data(connection_config):
            row_transform = UsersMatchmakingRowTransformer.process(row, connection_config)
            if row_transform['date'] > max_date_mongo:
                max_date_mongo = row_transform['date']
            data_matchmaking.append(row_transform)
            counter += 1

        file_path = file_paths.get_path(connection_config['id'], dag_config.TMP_USERS_MATCHMAKING_COLLECTION_FILE_PATH)
        data_frame = pd.DataFrame(data_matchmaking)
        data_frame.to_parquet(file_path)

        if max_date_mongo == 0:
            return

        file_path_state = file_paths.get_path(
            connection_config['id'],
            dag_config.TMP_USERS_MATCHMAKING_EXTRACT_STATE_FILE_PATH
        )
        file_path_state_header = file_paths.get_header_path(dag_config.TMP_USERS_MATCHMAKING_EXTRACT_STATE_FILE_PATH)

        states = [{
            'connection_id': connection_config['id'],
            'source_name': dag_config.MONGO_GAME_COLLECTION,
            'key_name': f'update_datetime_{connection_config["platform"]}',
            'last_value_int': max_date_mongo
        }]

        header = states[0].keys()

        if not os.path.exists(file_path_state_header):
            with open(file_path_state_header, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, header, delimiter=';')
                writer.writeheader()

        with open(file_path_state, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, header, delimiter=';')
            for state in states:
                writer.writerow(state)

        LoggerService.finish_extracting(counter)
    except Exception as e:
        LoggerService.logger.error(f'Error load data, see detailed information: {str(e)}')
        raise


def move_matchmaking_data():
    conn_main = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    LoggerService.start_move_data(
        dag_config.CH_USERS_MATCHMAKING_EXTRACTED_TABLE,
        dag_config.CH_USERS_MATCHMAKING_TABLE
    )
    try:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'INSERT INTO {conn_main.schema}.{dag_config.CH_USERS_MATCHMAKING_TABLE} SELECT * FROM '
                   f'{dag_config.CH_USERS_MATCHMAKING_EXTRACTED_TABLE}')
    except Exception as e:
        LoggerService.error_move_data(
            dag_config.CH_USERS_MATCHMAKING_TABLE,
            dag_config.CH_USERS_MATCHMAKING_EXTRACTED_TABLE,
            str(e)
        )
        raise


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    extract_matchmaking_data_tasks = [
        PythonOperator(
            task_id=f'extract_matchmaking_data_{row["id"]}',
            python_callable=extract_matchmaking_data,
            op_kwargs={
                'connection_config': row
            },
        )
        for row in config.get_connections(ConfigService.CONN_MONGO_SHARDS)
    ]

    finish_extract_matchmaking_data = EmptyOperator(
        task_id='finish_extract_matchmaking_data'
    )

    load_matchmaking_data_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths.get_path(row['id'], dag_config.TMP_USERS_MATCHMAKING_COLLECTION_FILE_PATH),
            table_name=dag_config.CH_USERS_MATCHMAKING_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'load_matchmaking_data_{row["id"]}'
        )
        for row in config.get_connections(ConfigService.CONN_MONGO_SHARDS)
    ]

    move_matchmaking_data_task = PythonOperator(
        task_id='move_matchmaking_data',
        python_callable=move_matchmaking_data
    )

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_USERS_MATCHMAKING_EXTRACT_STATE_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MONGO_SHARDS)
    )

    load_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_USERS_MATCHMAKING_EXTRACT_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='load_extract_state'
    )

    clear_temp_data_task >> extract_matchmaking_data_tasks >> finish_extract_matchmaking_data \
    >> load_matchmaking_data_tasks >> move_matchmaking_data_task >> concat_extract_state_files_task \
    >> load_extract_state_task

if __name__ == '__main__':
    dag.cli()
