from typing import Dict
import dags.users_matchmaking.config.default as dag_config
from dags.lib.helpers.parquet_data_prepare import prepare_parquet_data


class UsersMatchmakingRowTransformer:
    @staticmethod
    def process(row: Dict, connection_config: Dict) -> Dict:

        row_transform = {
            '_connection_id': connection_config['id'],
            'platform_type': connection_config['platform'],
            'event_id': row['event_id']
        }

        for mongo_field in dag_config.FIELDS_MAP[row['event_id']]:
            ch_config = dag_config.MONGO_GAME_COLLECTION_FIELDS[mongo_field]
            mongo_field_parts = mongo_field.split('.')
            field_val = row[mongo_field_parts.pop(0)] if mongo_field_parts[0] in row else None
            if field_val is not None:
                for field_part in mongo_field_parts:
                    if field_part not in field_val:
                        field_val = None
                        break
                    field_val = field_val[field_part]
            row_transform[ch_config['ch_field']] = field_val

        return prepare_parquet_data(row_transform)
