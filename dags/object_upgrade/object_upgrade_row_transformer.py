from datetime import datetime
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value


class ObjectUpgradeRowTransformer:
    INDEX_INSERT_DATE = 0
    INDEX_DATE = 7
    INDEX_EXTRA = 11

    @staticmethod
    def process(row: list, connection_id: str) -> list:
        extra_data = row[ObjectUpgradeRowTransformer.INDEX_EXTRA].split(':', 2)
        odt = row[ObjectUpgradeRowTransformer.INDEX_INSERT_DATE]
        dt = row[ObjectUpgradeRowTransformer.INDEX_DATE]

        try:
            row[ObjectUpgradeRowTransformer.INDEX_INSERT_DATE] = get_converted_value(val=odt, type_val=datetime)
            row[ObjectUpgradeRowTransformer.INDEX_DATE] = get_converted_value(val=dt, type_val=datetime)
            row[ObjectUpgradeRowTransformer.INDEX_EXTRA] = get_converted_value(val=extra_data[0], type_val=str)
            row.append(get_converted_value(val=extra_data[1], type_val=int))
        except Exception as e:
            row[ObjectUpgradeRowTransformer.INDEX_INSERT_DATE] = odt
            row[ObjectUpgradeRowTransformer.INDEX_DATE] = dt
            row[ObjectUpgradeRowTransformer.INDEX_EXTRA] = get_default_value(type_val=str)
            row.append(get_default_value(type_val=int))

        row.append(connection_id)
        return row
