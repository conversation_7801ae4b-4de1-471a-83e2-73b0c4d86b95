import os
import csv
from datetime import datetime
from airflow import DAG
from airflow.operators.python import PythonOperator
from dags.lib.services.config_service import ConfigService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.services.file_path_service import FilePathService
import dags.object_upgrade.config.default as dag_config
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value
from dags.object_upgrade.object_upgrade_row_transformer import ObjectUpgradeRowTransformer
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.config.default import CH_EXTRACT_STATE_TABLE
from dags.lib.services.platform_service import PlatformService

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {dag_config.TMP_OBJECT_UPGRADE_STATE_FILE_PATH: config.get_connection_ids(ConfigService.CONN_MYSQL_OP_LOGS)}
)


def clear_temp_data_files():
    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def get_extra_state_source_name(platform: str) -> str:
    return f'{dag_config.CH_OP_LOGS_TABLE}_{platform}'


def stream_op_logs(connection_id: str, platform: str):
    try:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        ch_etl = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        extract_state = ExtractStateService(ch_etl)

        max_date = extract_state.get_last_value(connection_id, get_extra_state_source_name(platform),
                                                ExtractStateService.DATE_TYPE, ExtractStateService.DATE_TYPE)
        max_oplog_insert_date = extract_state.get_last_value(connection_id, get_extra_state_source_name(platform),
                                                             'oplog_insert_datetime', ExtractStateService.DATE_TYPE)
        max_date = get_converted_value(val=max_date, type_val=str)
        max_oplog_insert_date = get_converted_value(val=max_oplog_insert_date, type_val=str)
        sql = f'''
            SELECT
                any(_insert_datetime),
                any(_source_name),
                any(_id),
                any(platform_type),
                user_id,
                any(screen),
                any(session_id),
                date,
                any(clan_id),
                any(level),
                any(progress),
                extra
            FROM {dag_config.CH_OP_LOGS_TABLE}
            WHERE platform_type='{platform}' 
                AND date>'{max_date}' AND action={dag_config.ACTION_UPGRADE_ID} 
                AND _insert_datetime>'{max_oplog_insert_date}'
            GROUP BY user_id, date, extra
        '''
        LoggerService.logger.info(sql)
        for row in ch.stream(sql):
            yield row
    except Exception as e:
        LoggerService.logger.error(f'Error get data, see detailed information: {str(e)}')
        raise


def load_upgrade_objects(connection_id: str, platform: str, ):
    LoggerService.start_extracting(
        platform,
        connection_id,
        dag_config.CH_OP_LOGS_TABLE
    )

    try:
        ch_connect_id = config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN)
        ch = ClickhouseService(ch_connect_id)
        file_path_state = file_paths.get_path(connection_id, dag_config.TMP_OBJECT_UPGRADE_STATE_FILE_PATH)
        file_path_state_header = file_paths.get_header_path(dag_config.TMP_OBJECT_UPGRADE_STATE_FILE_PATH)

        counter = 0
        object_upgrade_data = []
        batch_size = config.get_setting(ConfigService.BATCH_SIZE)
        max_date = get_default_value(type_val=datetime)
        max_insert_date = get_default_value(type_val=datetime)

        def insert_data(data: list):
            if len(data) > 0:
                ch.insert(
                    f'INSERT INTO {dag_config.CH_OBJECT_UPGRADE_TABLE} '
                    f'(_oplog_insert_datetime, _source_name, _id, platform_type, user_id, screen, '
                    f'session_id, date, clan_id, level, progress, object_name, level_reached, '
                    f'_connection_id) VALUES ', data
                )
                LoggerService.logger.info(f'Data successfully loaded to {dag_config.CH_OBJECT_UPGRADE_TABLE}, '
                                          f'load {len(data)} rows')

        for row in stream_op_logs(ch_connect_id, platform):
            counter += 1
            row = ObjectUpgradeRowTransformer.process(list(row), connection_id)
            max_date = max(max_date, row[ObjectUpgradeRowTransformer.INDEX_DATE])
            max_insert_date = max(max_insert_date, row[ObjectUpgradeRowTransformer.INDEX_INSERT_DATE])
            object_upgrade_data.append(row)

            if counter >= batch_size and counter % batch_size == 0:
                insert_data(object_upgrade_data)
                object_upgrade_data.clear()

        if counter == 0:
            LoggerService.finish_extracting(counter)
            return

        insert_data(object_upgrade_data)
        state_date = {
            'connection_id': ch_connect_id,
            'source_name': get_extra_state_source_name(platform),
            'key_name': 'date',
            'last_value_date': get_converted_value(val=max_date, type_val=str)
        }

        state_insert_date = {
            'connection_id': ch_connect_id,
            'source_name': get_extra_state_source_name(platform),
            'key_name': 'oplog_insert_datetime',
            'last_value_date': get_converted_value(val=max_insert_date, type_val=str)
        }

        if not os.path.exists(file_path_state_header):
            LoggerService.logger.info(f'Write to file path state header: {file_path_state_header}')
            with open(file_path_state_header, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, state_date.keys(), delimiter=';')
                writer.writeheader()

        LoggerService.logger.info(f'Write to file path state: {file_path_state}')
        with open(file_path_state, 'a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, state_date.keys(), delimiter=';')
            writer.writerow(state_date)
            writer.writerow(state_insert_date)

        LoggerService.finish_extracting(counter)
    except Exception as e:
        LoggerService.logger.error(f'Error load data, see detailed information: {str(e)}')
        raise


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_files_task = PythonOperator(
        task_id='clear_temp_data_files',
        python_callable=clear_temp_data_files
    )

    load_upgrade_objects_tasks = [
        PythonOperator(
            task_id=f'load_upgrade_objects_{row["platform"]}',
            python_callable=load_upgrade_objects,
            op_kwargs={
                'connection_id': row['id'],
                'platform': row['platform']
            },
        )
        for row in PlatformService.expand_mobile_connection(config.get_connections(ConfigService.CONN_MYSQL_OP_LOGS))
    ]

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_extract_state_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_OBJECT_UPGRADE_STATE_FILE_PATH,
        connection_ids=config.get_connection_ids(ConfigService.CONN_MYSQL_OP_LOGS)
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_OBJECT_UPGRADE_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_extract_state'
    )

    clear_temp_data_files_task >> load_upgrade_objects_tasks \
        >> concat_extract_state_files_task >> import_extract_state_task

if __name__ == '__main__':
    dag.cli()
