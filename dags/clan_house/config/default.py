from datetime import timedelta, datetime, timezone

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load object upgrade information',
}

SETTINGS = {}

ACTION_UPGRADE_ID = 171
CH_TABLE_STAGE1_EXTRACTED = 'clan_house_stage1_extracted'
CH_TABLE = 'clan_house'
CH_OPLOGS_TABLE = 'oplogs'
CH_EVENTS_TABLE = 'events'

TMP_STATE_FILE_PATH = 'clan_house_es_[connection].csv'
TMP_STAGE1_STATE_FILE_PATH = 'clan_house_stage1_es_[connection].csv'

RELEASE_DATE = datetime.strptime('2024-02-27 00:00:00', '%Y-%m-%d %H:%M:%S').replace(tzinfo=timezone.utc)
