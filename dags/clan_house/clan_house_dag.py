import os
import csv
from datetime import datetime, timezone
from airflow import DAG
from airflow.operators.python import PythonOperator
from dags.lib.services.config_service import ConfigService
from dags.lib.services.clickhouse_service import ClickhouseService, CSV_DATA_TYPE
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.extract_state_service import ExtractStateService
from dags.lib.services.file_path_service import FilePathService
import dags.clan_house.config.default as dag_config
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value
from dags.clan_house.clan_house_row_transformer import ClanHouseRowTransformer
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from dags.lib.operators.concat_csv_operator import ConcatCSVOperator
from dags.config.default import CH_EXTRACT_STATE_TABLE
from dags.lib.services.platform_service import PlatformService
from airflow.hooks.base import BaseHook
from dags.lib.helpers.date_helper import date_time_to_str

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)

platform_connections = PlatformService.expand_mobile_connection(config.get_connections(ConfigService.CONN_MYSQL_OP_LOGS))
platform_connections_ids = [conn['id'] for conn in platform_connections]
platform_connections_ch = map(lambda x: {'platform': x['platform'], 'id': f'clickhouse_{x["platform"]}'}, platform_connections)
platform_connections_ch_ids = [conn['id'] for conn in platform_connections_ch]
file_paths = FilePathService(
    DAG_ID,
    BASE_PATH,
    {
        dag_config.TMP_STATE_FILE_PATH: platform_connections_ids,
        dag_config.TMP_STAGE1_STATE_FILE_PATH: platform_connections_ch_ids
    }
)


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_TABLE_STAGE1_EXTRACTED)

    for path in file_paths.get_all_paths():
        LoggerService.logger.info('Removing ' + path)
        if os.path.exists(path):
            os.remove(path)


def get_extra_state_source_name(platform: str) -> str:
    return f'{dag_config.CH_OPLOGS_TABLE}_clan_house_{platform}'

def get_extra_state_stage1_source_name(platform: str) -> str:
    return f'events_clan_house_stage1_{platform}'


def stream_op_logs(connection_id: str, platform: str):
    try:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
        ch_etl = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        extract_state = ExtractStateService(ch_etl)

        max_date = extract_state.get_last_value(connection_id, get_extra_state_source_name(platform),
                                                ExtractStateService.DATE_TYPE, ExtractStateService.DATE_TYPE)
        max_oplog_insert_date = extract_state.get_last_value(connection_id, get_extra_state_source_name(platform),
                                                             'oplog_insert_datetime', ExtractStateService.DATE_TYPE)
        max_date = get_converted_value(val=max_date, type_val=str)
        max_oplog_insert_date = get_converted_value(val=max_oplog_insert_date, type_val=str)
        sql = f'''
            SELECT
                any(_insert_datetime),
                any(_source_name),
                any(_id),
                any(platform_type),
                user_id,
                any(session_id),
                date,
                any(clan_id),
                any(level),
                any(progress),
                any(progress_level),
                extra
            FROM {dag_config.CH_OPLOGS_TABLE}
            WHERE platform_type='{platform}' 
                AND date>'{max_date}' AND action={dag_config.ACTION_UPGRADE_ID} 
                AND _insert_datetime>'{max_oplog_insert_date}'
            GROUP BY user_id, date, extra
        '''
        LoggerService.logger.info(sql)
        for row in ch.stream(sql):
            yield row
    except Exception as e:
        LoggerService.logger.error(f'Error get data, see detailed information: {str(e)}')
        raise


def load_data(connection_id: str, platform: str):
    LoggerService.start_extracting(
        platform,
        connection_id,
        dag_config.CH_OPLOGS_TABLE
    )

    try:
        ch_connect_id = config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN)
        ch = ClickhouseService(ch_connect_id)
        file_path_state = file_paths.get_path(connection_id, dag_config.TMP_STATE_FILE_PATH)
        file_path_state_header = file_paths.get_header_path(dag_config.TMP_STATE_FILE_PATH)

        counter = 0
        data = []
        batch_size = config.get_setting(ConfigService.BATCH_SIZE)
        max_date = get_default_value(type_val=datetime)
        max_insert_date = get_default_value(type_val=datetime)

        def insert_data(data: list):
            if len(data) > 0:
                ch.insert(
                    f'INSERT INTO {dag_config.CH_TABLE} '
                    f'(_oplog_insert_datetime, _source_name, _id, platform_type, user_id, '
                    f'session_id, date, clan_id, level, progress, progress_level, level_reached, '
                    f'_connection_id) VALUES ', data
                )
                LoggerService.logger.info(f'Data successfully loaded to {dag_config.CH_TABLE}, '
                                          f'load {len(data)} rows')

        for row in stream_op_logs(ch_connect_id, platform):
            counter += 1
            row = ClanHouseRowTransformer.process(list(row), connection_id)
            max_date = max(max_date, row[ClanHouseRowTransformer.INDEX_DATE])
            max_insert_date = max(max_insert_date, row[ClanHouseRowTransformer.INDEX_INSERT_DATE])
            data.append(row)

            if counter >= batch_size and counter % batch_size == 0:
                insert_data(data)
                data.clear()

        if counter == 0:
            LoggerService.finish_extracting(counter)
            return

        insert_data(data)
        state_date = {
            'connection_id': ch_connect_id,
            'source_name': get_extra_state_source_name(platform),
            'key_name': 'date',
            'last_value_date': get_converted_value(val=max_date, type_val=str)
        }

        state_insert_date = {
            'connection_id': ch_connect_id,
            'source_name': get_extra_state_source_name(platform),
            'key_name': 'oplog_insert_datetime',
            'last_value_date': get_converted_value(val=max_insert_date, type_val=str)
        }

        if not os.path.exists(file_path_state_header):
            LoggerService.logger.info(f'Write to file path state header: {file_path_state_header}')
            with open(file_path_state_header, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, state_date.keys(), delimiter=';')
                writer.writeheader()

        LoggerService.logger.info(f'Write to file path state: {file_path_state}')
        with open(file_path_state, 'a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, state_date.keys(), delimiter=';')
            writer.writerow(state_date)
            writer.writerow(state_insert_date)

        LoggerService.finish_extracting(counter)
    except Exception as e:
        LoggerService.logger.error(f'Error load data, see detailed information: {str(e)}')
        raise

def extract_stage1_data(platform: str):
    # Так как в оплоги не попадает факт самой первой установки хижины - определяем его по таблице events
    conn_id = config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN)
    ch = ClickhouseService(conn_id)
    file_path_state = file_paths.get_path(f'clickhouse_{platform}', dag_config.TMP_STAGE1_STATE_FILE_PATH)
    file_path_state_header = file_paths.get_header_path(dag_config.TMP_STAGE1_STATE_FILE_PATH)

    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch_etl = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    extract_state = ExtractStateService(ch_etl)

    source_name = get_extra_state_stage1_source_name(platform)
    max_date = max(
        extract_state.get_last_value(conn_id, source_name, ExtractStateService.DATE_TYPE, ExtractStateService.DATE_TYPE).replace(tzinfo=timezone.utc),
        dag_config.RELEASE_DATE)

    LoggerService.logger.info(f'Max date is: {max_date}')

    sql = f'''
        INSERT INTO {conn_etl.schema}.{dag_config.CH_TABLE_STAGE1_EXTRACTED} (_connection_id, _oplog_insert_datetime, 
            _source_name, _id, platform_type, user_id, session_id, date, clan_id, level, 
            progress, progress_level, level_reached)
        SELECT '{conn_id}', _insert_datetime, 
            '{source_name}', 0, platform_type, user_id, session_id, date, clan_id, level,
            progress, progress_level, 1
        FROM {dag_config.CH_EVENTS_TABLE} 
        WHERE platform_type='{platform}' AND date > toDateTime('{date_time_to_str(max_date)}') 
        AND eventtype = 'tutorial_step_finish' AND id = 47 AND step_id = '2' 
        '''
    ch.execute(sql)

    max_date = ch_etl.get_max_column_value(
        dag_config.CH_TABLE_STAGE1_EXTRACTED,
        'date',
        {'platform_type': platform})
    LoggerService.logger.info(f'New max date is: {max_date}')

    state_date = {
        'connection_id': conn_id,
        'source_name': source_name,
        'key_name': 'date',
        'last_value_date': get_converted_value(val=max_date, type_val=str)
    }

    if not os.path.exists(file_path_state_header):
        LoggerService.logger.info(f'Write to file path state header: {file_path_state_header}')
        with open(file_path_state_header, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, state_date.keys(), delimiter=';')
            writer.writeheader()

    LoggerService.logger.info(f'Write to file path state: {file_path_state}')
    with open(file_path_state, 'a', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, state_date.keys(), delimiter=';')
        writer.writerow(state_date)


def load_stage1_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.execute(f'''
            INSERT INTO {dag_config.CH_TABLE} SELECT * 
            FROM {conn_etl.schema}.{dag_config.CH_TABLE_STAGE1_EXTRACTED}
        ''')

dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_files_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data
    )

    load_tasks = [
        PythonOperator(
            task_id=f'load_{row["platform"]}',
            python_callable=load_data,
            op_kwargs={
                'connection_id': row['id'],
                'platform': row['platform']
            },
        )
        for row in platform_connections
    ]

    concat_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_es_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_STATE_FILE_PATH,
        connection_ids=platform_connections_ids
    )

    import_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_es'
    )

    extract_stage1_tasks = [
        PythonOperator(
            task_id=f'extract_stage1_{row["platform"]}',
            python_callable=extract_stage1_data,
            op_kwargs={
                'platform': row['platform']
            },
        )
        for row in platform_connections
    ]

    load_stage1_task = PythonOperator(
        task_id=f'load_stage1_data',
        python_callable=load_stage1_data
    )

    concat_stage1_extract_state_files_task = ConcatCSVOperator(
        task_id='concat_stage1_es_files',
        base_path=BASE_PATH,
        path_template=dag_config.TMP_STAGE1_STATE_FILE_PATH,
        connection_ids=platform_connections_ch_ids
    )

    import_stage1_extract_state_task = DataToClickHouseOperator(
        connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
        file_path=file_paths.get_result_path(dag_config.TMP_STAGE1_STATE_FILE_PATH),
        table_name=CH_EXTRACT_STATE_TABLE,
        data_type=CSV_DATA_TYPE,
        task_id='import_stage1_es'
    )

    clear_temp_data_files_task >> load_tasks >> concat_extract_state_files_task >> import_extract_state_task

    clear_temp_data_files_task >> extract_stage1_tasks >> load_stage1_task >> concat_stage1_extract_state_files_task \
        >> import_stage1_extract_state_task

if __name__ == '__main__':
    dag.cli()
