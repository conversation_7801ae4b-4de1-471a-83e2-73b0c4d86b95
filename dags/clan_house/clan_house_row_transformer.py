from datetime import datetime
from dags.lib.helpers.convert_value_helper import get_converted_value, get_default_value


class ClanHouseRowTransformer:
    # TODO: отрефакторить это legacy, по именам обращаться куда надежней
    INDEX_INSERT_DATE = 0
    INDEX_DATE = 6
    INDEX_EXTRA = 11

    @staticmethod
    def process(row: list, connection_id: str) -> list:
        extra_data = row[ClanHouseRowTransformer.INDEX_EXTRA].split(':', 1)
        odt = row[ClanHouseRowTransformer.INDEX_INSERT_DATE]
        dt = row[ClanHouseRowTransformer.INDEX_DATE]

        try:
            row[ClanHouseRowTransformer.INDEX_INSERT_DATE] = get_converted_value(val=odt, type_val=datetime)
            row[ClanHouseRowTransformer.INDEX_DATE] = get_converted_value(val=dt, type_val=datetime)
            row[ClanHouseRowTransformer.INDEX_EXTRA] = get_converted_value(val=extra_data[0], type_val=int)
        except Exception as e:
            row[ClanHouseRowTransformer.INDEX_INSERT_DATE] = odt
            row[ClanHouseRowTransformer.INDEX_DATE] = dt
            row[ClanHouseRowTransformer.INDEX_EXTRA] = get_default_value(type_val=int)

        row.append(connection_id)
        return row
