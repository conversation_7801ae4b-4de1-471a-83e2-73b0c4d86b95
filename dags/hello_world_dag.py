from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator

import logging


def print_log(**kwargs):
    logger = logging.getLogger("airflow.task")
    logger.debug('This is a debug message')
    logger.info('This is an info message')
    logger.warning('This is a warning message')
    logger.error('This is an error message')
    logger.critical('This is a critical message')


with DAG(
    'hello_world',
    default_args={
        'retries': 1,
    },
    description='A simple DAG for checking infrastructure',
    start_date=datetime(2022, 8, 1),
    schedule_interval=timedelta(hours=1),
    tags=['temp'],
    catchup=False,
) as dag:
    t = PythonOperator(
        task_id="print_to_log",
        python_callable=print_log
    )

