from typing import Dict
import dags.events.config.default as dag_config
from datetime import datetime
import pytz
import json
from dags.lib.services.platform_service import PlatformService
from dags.events.event_validator import EventValidator


class EventTransformer:

    # TODO: Удалить здесь и использовать код из lib/
    @staticmethod
    def convert_time_to_utc(date_string_in: str, format_in: str):
        d = datetime.strptime(date_string_in, format_in)
        return d.replace(tzinfo=pytz.timezone('UTC')) - d.utcoffset()

    @staticmethod
    def prepare(event: Dict):
        transformed = {}
        if 'params' not in event:
            return transformed
        params_data = event['params']
        if 'app_build' not in params_data \
                and ('platform' not in params_data
                     or params_data['platform'] not in [PlatformService.PLATFORM_ANDROID,
                                                        PlatformService.PLATFORM_IOS]):
            params_data['app_build'] = 'unknown'
        for param in ['event', 'fluent_time']:
            if param in event:
                params_data[param] = event[param]
        event_type = event['event'] if 'event' in event else None
        params = dag_config.EVENTS_BASE_PARAMS + \
                 (dag_config.EVENTS[event_type]
                  if (event_type is not None) and (event_type in dag_config.EVENTS)
                  else [])
        for param in params:
            if param in params_data:
                transformed[param] = params_data[param]
                continue
            # Возможно параметр лежит глубже в структуре params
            parts = param.split('.')
            data = params_data
            for part in parts:
                if part not in data:
                    data = None
                    break
                data = data[part]
            if data is not None:
                transformed[param] = data
        return transformed

    @staticmethod
    def __set_fields(event: Dict, errors: list):
        params_skip = []
        for error in errors:
            if error['type'] == EventValidator.ERROR_BAD_VALUE:
                params_skip.append(error['details']['name'])
        transformed = {}
        fields_config = dag_config.EVENTS_PARAMS
        params = dag_config.EVENTS_BASE_PARAMS + dag_config.EVENTS[event['event']]
        for param in params:
            if param in event:
                field_config = fields_config[param] if 'ch_field' in fields_config[param] else None
                if field_config is None:
                    # Значит одному и тому же названию параметра на клиенте соответствует несколько колонок в базе
                    for config in fields_config[param]:
                        if event['event'] in config['events']:
                            field_config = config
                            break
                if param in params_skip:
                    transformed[field_config['ch_field']] = None
                    continue
                if (field_config['type'] == 'JSON'):
                    event[param] = json.dumps(event[param])
                # TODO: Не особо очевидно, но чтобы убрать это, надо требовать более строго соответствия
                #  документации на клиенте
                if (type(event[param]) == bool):
                    event[param] = int(event[param])
                transformed[field_config['ch_field']] = event[param]
        return transformed

    @staticmethod
    def process(event: Dict, validation_result: int, file_path: str, errors: list) -> Dict:
        transformed = EventTransformer.__set_fields(event, errors)

        # Не очень нравится, что по сути два раза конвертим - в момент валидации и здесь.
        # С другой стороны по логике валидация не должна трансформировать ивент
        transformed['_server_date'] = EventTransformer.convert_time_to_utc(
            transformed['_server_date'],
            dag_config.EVENTS_PARAMS['fluent_time']['validation']['format']
        )
        transformed['date_obj'] = EventTransformer.convert_time_to_utc(
            transformed['date'],
            dag_config.EVENTS_PARAMS['date']['validation']['format']
        )
        transformed['date'] = datetime.strftime(transformed['date_obj'], '%Y-%m-%d %H:%M:%S')

        transformed['_validation_result'] = validation_result
        transformed['_filename'] = file_path

        if 'environment' in transformed:
            transformed['environment'] = json.dumps(transformed['environment'])

        if 'award' in transformed:
            transformed['award'] = json.dumps(transformed['award'])

        # Костыль для клиента КХ версии 22.2.2.1 - не может импортировать файл, когда все значения колонки в файле
        # пусты
        transformed = {k: v for k, v in transformed.items() if v is not None}

        # Клиент присылает в параметре иногда строку, иногда число
        str_params = ['offers_id', 'str_id', 'award_type', 'id_2']
        for param in str_params:
            if param in transformed:
                transformed[param] = str(transformed[param])

        return transformed

    @staticmethod
    def leave_base_params(event: Dict) -> Dict:
        transformed = {}
        fields_config = dag_config.EVENTS_PARAMS
        for param in dag_config.EVENTS_BASE_PARAMS:
            field_config = fields_config[param] if 'ch_field' in fields_config[param] else None
            if field_config is None:
                # Значит одному и тому же названию параметра на клиенте соответствует несколько колонок в базе
                for config in fields_config[param]:
                    if event['event'] in config['events']:
                        field_config = config
                        break
            param = field_config['ch_field']
            if param in event:
                transformed[param] = event[param]
            pass
        return transformed
