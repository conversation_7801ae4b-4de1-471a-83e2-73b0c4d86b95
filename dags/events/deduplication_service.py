from typing import Dict
import dags.events.config.default as dag_config
import redis
from glob import glob
from dags.events.event_transformer import EventTransformer
from dags.events.event_validator import EventValidator
from datetime import datetime, timedelta
from dags.lib.services.logger_service import LoggerService
from json.decoder import JSONDecodeError
import json
import math


class DeduplicationService:
    BATCH_SIZE = 10000

    @staticmethod
    def get_bucket_value(event: Dict) -> str:
        return f"{event['user_id']}_{event['session_id']}_{event['session_index']}"

    @staticmethod
    def get_bucket_timestamp(date: datetime) -> int:
        date = date - timedelta(seconds=date.timestamp() % dag_config.DEDUPLICATION_BUCKET_PERIOD_SECONDS)
        return math.floor(date.timestamp())

    # TODO: Убрать копипаст с куском из events_dag
    def stream_events(self, file_path):
        with open(file_path, 'r') as file:
            line_number = 1
            for line in file:
                error = None
                parsed_line = {}
                try:
                    parsed_line = json.loads(line.rstrip())
                except JSONDecodeError as e:
                    error = {
                        'type': EventValidator.ERROR_BAD_JSON,
                        'details': {
                            'msg': e.msg,
                            'col': e.colno,
                            'pos': e.pos
                        }
                    }
                yield parsed_line, line, error
                line_number += 1

    def __init__(self, platform: str, connection_config: Dict, base_path: str):
        self.platform = platform
        self.connection = None
        self.connection_config = connection_config
        self.restore_path = f'{base_path}/{dag_config.BACKUP_RESTORE_FOLDER}'

    def __get_connection(self) -> redis.Redis:
        if self.connection is None:
            self.connection = redis.Redis(self.connection_config['host'], self.connection_config['port'])
        return self.connection

    def __get_data_ready_key(self) -> str:
        return dag_config.REDIS_DATA_READY_FLAG_KEY.replace('[platform]', self.platform)

    def __set_data_ready_key(self):
        return self.__get_connection().set(self.__get_data_ready_key(), '1')

    def __get_data_key(self, bucket_timestamp: int = 0) -> str:
        bucket_timestamp = '*' if bucket_timestamp == 0 else str(bucket_timestamp)
        return dag_config.REDIS_DATA_KEY.replace('[platform]', self.platform).replace(
            '[bucket_timestamp]', bucket_timestamp)

    def is_data_ready(self) -> bool:
        value = self.__get_connection().get(self.__get_data_ready_key())
        return (value is not None) and (value.decode('utf-8') == '1')

    def get_duplicated_keys(self, bucket_timestamp: int, keys: list) -> map:
        result = self.__get_connection().execute_command(
            f'SMISMEMBER {self.__get_data_key(bucket_timestamp)} {" ".join(keys)}')
        return map((lambda x: x[1]), filter(lambda x: x[0], zip(result, keys)))

    def fill_data(self):
        buckets = {}
        r = self.__get_connection()
        for file_path in glob(f'{self.restore_path}/*.log'):
            for event, json_string, error in self.stream_events(file_path):
                if error is not None:
                    continue
                event = EventTransformer.prepare(event)
                result, errors = EventValidator.validate(event)
                if result == EventValidator.RESULT_BAD_BASE_PARAMS:
                    continue
                event = EventTransformer.process(event, result, file_path, errors)
                date = event['date_obj']
                bucket_timestamp = math.floor((date - timedelta(
                    seconds=date.timestamp() % dag_config.DEDUPLICATION_BUCKET_PERIOD_SECONDS)).timestamp())
                if bucket_timestamp not in buckets:
                    buckets[bucket_timestamp] = {
                        'key': self.__get_data_key(bucket_timestamp),
                        'values': []
                    }
                buckets[bucket_timestamp]['values'].append(DeduplicationService.get_bucket_value(event))
                if len(buckets[bucket_timestamp]['values']) >= DeduplicationService.BATCH_SIZE:
                    r.sadd(buckets[bucket_timestamp]['key'], *buckets[bucket_timestamp]['values'])
                    buckets[bucket_timestamp]['values'] = []
        for bucket_timestamp, bucket in buckets.items():
            if len(bucket['values']) > 0:
                r.sadd(bucket['key'], *bucket['values'])
        self.__set_data_ready_key()

    def add_data(self, bucket_timestamp, values: set):
        self.__get_connection().sadd(self.__get_data_key(bucket_timestamp), *values)

    def remove_expired_buckets(self):
        r = self.__get_connection()
        timestamps = r.keys(self.__get_data_key())
        if len(timestamps) == 0:
            return
        timestamps = list(map(lambda x: int(x.decode('utf-8').split(':')[3]), timestamps))
        timestamps.sort()
        counts = {}
        now = datetime.now().timestamp()
        for timestamp in timestamps:
            # Мы знаем, что некоторое количество ивентов с клиента приходит со временем в будущем, заведомо известно,
            # что данных в таких бакетах немного, поэтому оставляем их храниться и даже не получаем кол-во значений в
            # них
            if timestamp > now:
                continue
            counts[timestamp] = r.scard(self.__get_data_key(timestamp))

        # Мы знаем, что некоторое количество ивентов с клиента приходит со временем в будущем, таким образом
        # с помощью граничного значения оставляем небольшие по объему бакеты с данными, их наличие в памяти некритично
        edge_count = max(counts.values()) * 0.005

        index = len(timestamps) - 1
        while index >= 0:
            key = timestamps[index]
            if (key in counts) and (counts[key] > edge_count):
                break
            index -= 1

        timestamps_left = timestamps[index - dag_config.DEDUPLICATION_BUCKETS_COUNT:len(timestamps)]
        diff = set(timestamps) - set(timestamps_left)

        # Информационно-отладочное логирование
        format = '%Y-%m-%d %H:%M:%S'
        LoggerService.logger.info('Keys left: ')
        LoggerService.logger.info(
            list(map(lambda x: (
                datetime.fromtimestamp(x).strftime(format), counts[x] if x in counts else '-'), timestamps_left)))
        LoggerService.logger.info('Keys removed: ')
        LoggerService.logger.info(
            list(map(lambda x: (
                datetime.fromtimestamp(x).strftime(format), counts[x] if x in counts else '-'), diff)))

        if len(diff) > 0:
            r.delete(*map(lambda x: self.__get_data_key(x), diff))
