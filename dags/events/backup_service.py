from typing import Dict
import dags.events.config.default as dag_config
import os
import re
from datetime import datetime, timedelta
from shutil import copy2
from glob import glob
import tarfile
from dags.lib.services.logger_service import LoggerService


class BackupService:

    def __init__(self, platform: str, base_path: str):
        self.platform = platform
        self.backup_path = f'{base_path}/{dag_config.BACKUP_FOLDER}'
        self.restore_path = f'{base_path}/{dag_config.BACKUP_RESTORE_FOLDER}'

    def restore(self, edge_timestamp: int):

        if not os.path.exists(self.restore_path):
            os.makedirs(self.restore_path)

        LoggerService.logger.info(
            f'edge: {edge_timestamp}, ({datetime.fromtimestamp(edge_timestamp).strftime("%Y-%m-%d %H:%M:%S")})')

        regexp = re.compile('\.(\d+_\d+)\.')
        for file_path in glob(f'{self.backup_path}/*.log'):
            result = regexp.search(file_path)
            dt = datetime.strptime(result.group(1), '%Y%m%d_%H%M')
            if dt.timestamp() >= edge_timestamp:
                copy2(file_path, self.restore_path)

        dt = datetime.now()
        LoggerService.logger.info(f'tar timestamp: {dt.timestamp()}')
        while dt.timestamp() > edge_timestamp:
            day_hour_string = dt.strftime('%Y-%m-%d_%H')
            file_path = f'{self.backup_path}/{day_hour_string}.tar.gz'
            if os.path.exists(file_path):
                with tarfile.open(file_path) as tar:
                    tar.extractall(self.restore_path)
            dt = dt - timedelta(seconds=3600)

    def clear_restore(self):
        for file_path in glob(f'{self.restore_path}/*.log'):
            os.remove(file_path)
