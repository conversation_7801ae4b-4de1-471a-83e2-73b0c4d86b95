from datetime import timedelta
from dags.lib.services.platform_service import PlatformService

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load events from client',
    'concurrency': 6,
}

SETTINGS = {
    'platforms': [PlatformService.PLATFORM_VK],
    'redis_connection': {
        'host': 'redis',
        'port': 6379
    }
}

FILE_PATHS_PORTIONS_COUNT = 6
FILE_MTIME_SECONDS_PASSED = 180

BACKUP_FOLDER = 'backup'
BACKUP_RESTORE_FOLDER = 'restore'
BACKUP_SECONDS = 259200 # 3 дня

MAX_FILES_FOR_PROCESSING_COUNT = 10000

CH_EVENTS_EXTRACTED_TABLE = 'events_extracted'
CH_EVENTS_TABLE = 'events'
CH_ERRORS_TABLE = 'events_errors'
CH_EVENTS_DUPLICATED_TABLE = 'events_duplicated'
CH_TELEMETRY_TABLE = 'telemetry'
CH_TELEMETRY_EXTRACTED_TABLE = 'telemetry_extracted'

# На самом деле events можно брать в шаблон как имя ДАГа, чтобы не было возможности человеческой ошибки
TMP_EVENTS_FILE_PATH = 'events_[connection].parquet'
TMP_ERRORS_FILE_PATH = 'events_errors_[connection].parquet'
TMP_DUPLICATED_EVENTS_FILE_PATH = 'events_duplicated_[connection].parquet'
TMP_FILES_FILE_PATH = 'events_files_[connection].txt'
TMP_EVENTS_TELEMETRY_FILE_PATH = 'events_telemetry_[connection].parquet'

DEDUPLICATION_BUCKET_PERIOD_SECONDS = 1800  # 3600 (час) должен делиться на это значение без остатка
DEDUPLICATION_BUCKETS_COUNT = 4 # Знаем, что в течение двух часов приходит 90+% дубликатов
REDIS_DATA_READY_FLAG_KEY = 'events:[platform]:ready'
REDIS_DATA_KEY = 'events:[platform]:data:[bucket_timestamp]'

TELEMETRY_EVENT_PARAMS = ['duration', 'mt_avg', 'mt_max', 'gss_count', 'gss_duration', 'gsr_count', 'gsr_duration',
                          'lsr_count', 'lsr_duration', 'wsr_count', 'wsr_duration', 'sr_count', 'sr_count_error',
                          'sr_time', 'sr_time_min', 'sr_time_max', 'rr_count', 'rr_count_error', 'rr_time', 'rr_time_min',
                          'rr_time_max', 'rr_response_size', 'rri_count', 'rri_count_error', 'rri_time', 'rri_time_min',
                          'rri_time_max', 'rri_response_size']

# В доке убраны passing_tile_select, tile_select, passing_tile_removed, tile_linked и tile_change
# В конфиге их пока оставляем на некоторое время, мало ли передумают
EVENTS = {
    'performance': ['vendor', 'model', 'wid', 'memuse',
                    'stats.common.tickCount', 'stats.common.tickTime', 'stats.common.fps', 'stats.common.currentFps',
                    'stats.fullscreen.tickCount', 'stats.fullscreen.tickTime', 'stats.fullscreen.fps',
                    'stats.fullscreen.currentFps', 'logSize', 'duration', 'durationAbs', 'c_textures'],
    'server_response': ['ordernum', 'mintime', 'maxtime', 'medtime', 'period', 'meanvalue', 'requests',
                        'errorrequests'],
    'waiting_screen_shown': ['id', 'durc', 'dur', 'firload', 'conid'],
    'achievement': ['id', 'sid'],
    'chest_open': ['energylevel', 'cashlevel', 'coinslevel', 'moneylevel', 'dur', 'award'],
    'content_loading_finish': ['id', 'durc', 'firload', 'contentsize', 'conid'],
    'environment': ['env'],
    'error_window': ['id', 'type', 'la'],
    'friend_visit': ['frid', 'dflag', 'frlev', 'source', 'like', 'cardcol', 'cardfield', 'bubble'],
    'glades_compare': ['wcrore', 'glevel', 'divline'],
    'glades_upgrade': ['lev'],
    'loading_finish': ['dur', 'url', 'dflag', 'ecount', 'lang'],
    'music': ['state'],
    'sound': ['state'],
    'fullscreen': [],
    'animation': ['state'],
    'object_upgrade': ['lev', 'on'],
    'object_open': ['on'],
    'object_close': ['on'],
    'shop_open': ['type', 'sentry'],
    'shop_click': ['type', 'sentry', 'sid', 'dflag'],
    'factory_upgrade': ['lev'],
    'collection_upgrade': ['type', 'lev', 'strid'],
    'quest_complete': ['type', 'id', 'sid', 'act'],
    'workshop_request': ['strid', 'dflag'],
    'workshop_exchange': ['strid'],
    'workshop_collection': ['id', 'type', 'numindex'],
    'batlpas_award': ['type', 'batseas', 'id', 'award', 'atype'],
    'promo_open': ['sentry', 'pid', 'oids'],

    'wheel': ['snum', 'atype', 'amount'],
    'wheel_gold': ['source', 'snum', 'atype', 'amount'],
    'clan_create': ['cid', 'ftransf', 'lang'],
    'clan_join': ['cid', 'option', 'lang', 'sid'],
    'clan_leave': ['cid', 'option', 'lang'],
    'clan_request': ['rid'],
    'clan_proposal': ['clid'],
    'clan_message': ['message'],
    'clan_change_language': ['lang'],
    'clan_search': ['lang', 'minmem', 'maxmem', 'dflag', 'sresult'],
    'chest_upgrade': ['lev', 'gentype'],
    'chest_boost': ['price', 'numindex', 'award'],
    'minigame_performance': ['mgmode', 'mgnumb', 'fps', 'memuse', 'dur'],
    'tutorial_step_start': ['tid', 'sid'],
    'tutorial_step_finish': ['tid', 'sid', 'dur', 'skip'],
    'card_get': ['type', 'strid'],
    'quest_skip': ['id', 'idbef'],
    'quest_boost': ['id'],
    'object_merge': ['category', 'strid', 'olevel', 'status'],
    'object_change_position': ['category', 'strid', 'olevel', 'pstart', 'pfinish', 'dflag'],
    'object_gather': ['category', 'strid', 'olevel', 'award'],
    'object_activation': ['category', 'strid', 'olevel', 'award', 'numindex'],
    'object_bought': ['category', 'strid', 'olevel', 'ptype', 'price'],
    'basket_gather': ['award', 'mlevel'],
    'merge_level_upgrade': ['lev'],
    'offers_click': ['sentry', 'pid', 'oids', 'type'],
    'promo_event_open': ['type'],
    'toy_purchase': ['id', 'dflag'],
    'terms_of_use_show': [],
    'terms_of_use_accept': [],
    'rate_us_show': [],
    'rate_us_store_transition': [],
    'likes_upgrade': ['lev', 'fvote'],
    'pinata_event_open': ['type'],
    'pinata_done': ['strid', 'atype', 'numindex', 'amount', 'award'],
    'pinata_present': ['strid', 'numindex'],
    'pinata_stage_award': ['numindex', 'award', 'dflag', 'type'],
    'pinata_send_present': ['numindex', 'award'],
    'pinata_booster_get': ['strid', 'award'],
    'pinata_vip_buy': ['type'],
    'friend_get_present_open': ['frid', 'fuid'],
    'friend_get_present_send': ['frid', 'award', 'fuid'],
    'friend_receive_present_get': ['frid', 'award', 'fuid'],
    'dungeon_event_open': ['type'],
    'dungeon_vip_buy': ['type'],
    'push_notifications_accept': ['type', 'id'],
    'push_notifications_window': ['type', 'id'],
    'push_notifications_get': ['type', 'id'],
    'push_notifications_open': ['type'],
    'get_return_quest': ['questnumber'],
    'no_ads_purchase': ['type', 'id'],
    'ad_requested': ['type', 'numindex'],
    'ad_failed': ['type', 'numindex'],
    'ad_closed': ['type', 'numindex'],
    'ad_watched': ['type', 'numindex', 'award'],
    'ad_revenue_watched': ['revenue', 'type'],
    'ad_na_window': ['type', 'numindex', 'id', 'dflag'],
    'ad_notification_done': ['type', 'numindex'],
    'collection_open': ['type', 'value', 'strid', 'amount', 'numindex'],
    'collection_chest_open': ['type', 'numindex', 'strid', 'atype', 'award'],
    'tile_award': ['type', 'strid', 'id', 'value', 'award'],
    'tile_get': ['type', 'strid', 'id', 'value'],
    'tile_linked': ['strid', 'distance', 'amount', 'score', 'mgnumb', 'mgmode'],
    'tile_select': ['strid', 'id', 'mgnumb', 'mgmode'],
    'tile_change': ['id', 'mgnumb', 'mgmode', 'numindex', 'type', 'layout'],
    'minigame_pause': ['id', 'mgnumb', 'mgmode', 'layout', 'type', 'score'],
    'booster_used': ['id', 'mgnumb', 'mgmode', 'type'],
    'passing_booster_used': ['mgnumb', 'numindex', 'id', 'type'],
    'passing_tile_removed': ['mgnumb', 'numindex', 'id', 'amount', 'score', 'tilesleft', 'layout'],
    'passing_tile_select': ['strid', 'mgnumb', 'id', 'tilesleft', 'layout'],
    'normal_game_start': ['id', 'mgnumb', 'timer', 'tiles', 'tilescol', 'tilessel', 'layout'],
    'normal_game_finish': ['id', 'mgnumb', 'tiles', 'tilescol', 'tilessel', 'tilesdis', 'tilesrem', 'score',
                           'scoretime', 'dur', 'timer', 'type', 'jsonstr', 'layout', 'tilesleft', 'amount',
                           'boostersused', 'autoboostersused'],
    'normal_game_resume': ['id', 'mgnumb', 'timer', 'layout', 'tilesleft', 'score'],
    'tourney_game_start': ['mgnumb', 'strid', 'meanvalue', 'jsonstr', 'timer', 'tiles', 'tilescol', 'tilessel', 'layout'],
    'tourney_game_finish': ['mgnumb', 'strid', 'meanvalue', 'tiles', 'tilescol', 'tilessel', 'dur', 'timer', 'type',
                            'jsonstr', 'score', 'tilesleft', 'numindex', 'layout', 'amount', 'id', 'boostersused'],
    'tourney_stage_finish': ['mgnumb', 'strid', 'dur', 'timer', 'jsonstr', 'tilesleft', 'numindex', 'id',
                             'boostersused'],
    'tourney_game_resume': ['mgnumb', 'strid', 'numindex', 'dur', 'timer', 'jsonstr', 'tilesleft', 'id', 'layout'],
    'passing_game_start': ['mgnumb', 'numindex', 'lives', 'type'],
    'passing_game_finish': ['mgnumb', 'numindex', 'lives', 'type', 'dur', 'score', 'amount', 'jsonstr', 'boostersused',
                            'tilesleft', 'tilescol'],
    'passing_stage_finish': ['mgnumb', 'numindex', 'id', 'dur', 'score', 'boostersused', 'tilescol'],
    'passing_game_resume': ['mgnumb', 'numindex', 'id', 'score'],
    'passing_game_failed': ['lives'],
    'regatta_event_open': ['type'],
    'regatta_vip_buy': ['type'],
    'regatta_task_done': ['id', 'award'],
    'regatta_pers_stage_award': ['numindex', 'award', 'vip'],
    'regatta_clan_stage_award': ['numindex', 'award'],
    'guest_open': [],
    'guest_link': ['strid'],
    'guest_success': ['strid'],
    'icon_quest_open': [],
    'icon_quest_success': ['award'],
    'save_progress_open': [],
    'save_progress_link': ['strid'],
    'save_progress_success': ['strid'],
    'gw_quest_open': [],
    'gw_quest_award': ['id', 'type', 'award'],
    'clan_village_open': ['dummy_flag', 'state', 'type'],
    'clan_house_task_done': ['number_index', 'json_str', 'level_value'],
    'clan_castle_stage_reward': ['number_index', 'level_value', 'json_str'],
    'clan_tourney_reward': ['number_index', 'value', 'amount', 'json_str'],
    'banner_ad_shown': [],
    'banner_ad_revenue': [],
    'banner_ad_closed': [],
    'banner_ad_click': [],
    'banner_error_notshown': [],
    'banner_error_notfound': [],
    'banner_error_notavailable': [],
    'time_management_event_open': [],
    'time_management_stage_award': ['numindex', 'award'],
    'clan_house_crops_boost': ['dummy_flag'],
    'environment_graphics_support': ['env'],
    'data_request': ['rid'],
    'elka25_quest_open': [],
    'elka25_quest_link': [],
    'elka25_quest_award': ['id', 'award'],
    'clans_transfer': ['type'],
    'clans_transfer_click': ['type'],
    # Особый вид ивента, попадает в отдельную таблицу
    'telemetry': TELEMETRY_EVENT_PARAMS
}

EVENTS_BASE_PARAMS = ['event', 'fluent_time', 'uid', 'date', 'platform', 'session', 'n', 'level', 'rubies',
                      'energy', 'progress', 'progress_level', 'screen', 'clan_id', 'at', 'app_build', 'plevel',
                      'flevel', 'no_ads', 'tokens']

EVENTS_PARAMS = {
    # common
    'fluent_time': {'ch_field': '_server_date', 'type': 'DateTime', 'ptype': str,
                    'validation': {'format': '%Y-%m-%d %H:%M:%S %z'}},
    'event': {'ch_field': 'eventtype', 'type': 'String', 'ptype': str},
    'date': {'ch_field': 'date', 'type': 'DateTime', 'ptype': str, 'validation': {'format': '%Y%m%dT%H%M%S%z'}},
    'platform': {'ch_field': 'platform_type', 'type': 'String', 'ptype': int},
    'uid': {'ch_field': 'user_id', 'type': 'UInt64', 'ptype': int},
    'session': {'ch_field': 'session_id', 'type': 'String', 'ptype': str},
    'n': {'ch_field': 'session_index', 'type': 'UInt32', 'ptype': int},
    'level': {'ch_field': 'level', 'type': 'UInt32', 'ptype': int},
    'plevel': {'ch_field': 'passing_mode_level', 'type': 'UInt16', 'ptype': int},
    'flevel': {'ch_field': 'factory_level', 'type': 'UInt32', 'ptype': int},
    'rubies': {'ch_field': 'cash', 'type': 'UInt32', 'ptype': int},
    'energy': {'ch_field': 'energy', 'type': 'UInt32', 'ptype': int},
    'progress': {'ch_field': 'progress', 'type': 'UInt32', 'ptype': int},
    'progress_level': {'ch_field': 'progress_level', 'type': 'UInt32', 'ptype': int},
    'screen': {'ch_field': 'screen', 'type': 'UInt32', 'ptype': int},
    'clan_id': {'ch_field': 'clan_id', 'type': 'UInt32', 'ptype': int},
    'at': {'ch_field': 'app_type', 'type': 'UInt32', 'ptype': int},
    'app_build': {'ch_field': 'app_build', 'type': 'String', 'ptype': int},
    'no_ads': {'ch_field': 'no_ads', 'type': 'UInt8', 'ptype': int},
    'tokens': {'ch_field': 'tokens', 'type': 'UInt32', 'ptype': int},
    # uncommon
    'vendor': {'ch_field': 'device_vendor', 'type': 'String', 'ptype': str},
    'model': {'ch_field': 'device_model', 'type': 'String', 'ptype': str},
    'wid': {'ch_field': 'window_name', 'type': 'UInt8', 'ptype': int},
    'memuse': {'ch_field': 'memory_usage', 'type': 'UInt32', 'ptype': int},
    'stats.common.tickCount': {'ch_field': 'common_tick_count', 'type': 'UInt32', 'ptype': int},
    'stats.common.tickTime': {'ch_field': 'common_tick_time', 'type': 'UInt32', 'ptype': int},
    'stats.common.fps': {'ch_field': 'common_fps', 'type': 'Float32', 'ptype': float},
    'stats.common.currentFps': {'ch_field': 'common_current_fps', 'type': 'Float32', 'ptype': float},
    'stats.fullscreen.tickCount': {'ch_field': 'fullscreen_tick_count', 'type': 'UInt32', 'ptype': int},
    'stats.fullscreen.tickTime': {'ch_field': 'fullscreen_tick_time', 'type': 'UInt32', 'ptype': int},
    'stats.fullscreen.fps': {'ch_field': 'fullscreen_fps', 'type': 'Float32', 'ptype': float},
    'stats.fullscreen.currentFps': {'ch_field': 'fullscreen_current_fps', 'type': 'Float32', 'ptype': float},
    'logSize': {'ch_field': 'log_size', 'type': 'UInt32', 'ptype': int},
    'state': {'ch_field': 'state', 'type': 'UInt8', 'ptype': int},
    'id': {'ch_field': 'id', 'type': 'UInt32', 'ptype': int},
    'idbef': {'ch_field': 'id_2', 'type': 'UInt32', 'ptype': int},
    'dur': {'ch_field': 'duration', 'type': 'UInt32', 'ptype': int},
    'duration': [
        {
            'events': ['performance'],
            'ch_field': 'duration',
            'type': 'UInt32',
            'ptype': int
        },
        {
            'events': ['telemetry'],
            'ch_field': 'session_duration',
            'type': 'UInt32',
            'ptype': int
        }
    ],
    'durationAbs': {'ch_field': 'duration_abs', 'type': 'UInt32', 'ptype': int},
    'durc': {'ch_field': 'duration_cont', 'type': 'UInt32', 'ptype': int},
    'firload': {'ch_field': 'dummy_flag', 'type': 'UInt8', 'ptype': int},
    'contentsize': {'ch_field': 'content_size', 'type': 'UInt32', 'ptype': int},
    'conid': {'ch_field': 'content_id', 'type': 'UInt8', 'ptype': int},
    'sid': [
        # Если сделать ключами имена колонок CH, возможно не понадобилась бы поддержка списка тут. Хотя не факт
        {
            'events': ['quest_complete', 'shop_click', 'achievement', 'clan_join'],
            'ch_field': 'slot_id',
            'type': 'UInt8',
            'ptype': int
        },
        {
            'events': ['tutorial_step_start', 'tutorial_step_finish'],
            'ch_field': 'step_id',
            'type': 'String',
            'ptype': str
        },
    ],
    'award': {'ch_field': 'json_str', 'type': 'JSON', 'ptype': str},
    'energylevel': {'ch_field': 'energy_level', 'type': 'UInt16', 'ptype': int},
    'cashlevel': {'ch_field': 'cash_level', 'type': 'UInt16', 'ptype': int},
    'coinslevel': {'ch_field': 'coins_level', 'type': 'UInt16', 'ptype': int},
    'moneylevel': {'ch_field': 'money_level', 'type': 'UInt16', 'ptype': int},
    'env': {'ch_field': 'json_str', 'type': 'JSON', 'ptype': str},
    'type': {'ch_field': 'type', 'type': 'UInt16', 'ptype': int},
    'la': {'ch_field': 'last_action', 'type': 'String', 'ptype': int},
    'frid': {'ch_field': 'id', 'type': 'UInt64', 'ptype': int},
    'dflag': {'ch_field': 'dummy_flag', 'type': 'UInt8', 'ptype': int},
    'frlev': {'ch_field': 'level_value', 'type': 'UInt32', 'ptype': int},
    'source': {'ch_field': 'source', 'type': 'UInt8', 'ptype': int},
    'like': {'ch_field': 'state', 'type': 'UInt8', 'ptype': int},
    'cardcol': {'ch_field': 'value', 'type': 'UInt8', 'ptype': int},
    'cardfield': {'ch_field': 'amount', 'type': 'UInt8', 'ptype': int},
    'wcrore': {'ch_field': 'score', 'type': 'UInt8', 'ptype': int},
    'glevel': {'ch_field': 'level_value', 'type': 'UInt16', 'ptype': int},
    'divline': {'ch_field': 'value', 'type': 'UInt8', 'ptype': int},
    'lev': {'ch_field': 'level_value', 'type': 'UInt16', 'ptype': int},
    'url': {'ch_field': 'static_url', 'type': 'String', 'ptype': str},
    'ecount': {'ch_field': 'number_index', 'type': 'UInt16', 'ptype': int},
    'on': {'ch_field': 'str_id', 'type': 'String', 'ptype': str},
    'ordernum': {'ch_field': 'number_index', 'type': 'UInt16', 'ptype': int},
    'mintime': {'ch_field': 'min_value', 'type': 'UInt16', 'ptype': int},
    'maxtime': {'ch_field': 'max_value', 'type': 'UInt16', 'ptype': int},
    'medtime': {'ch_field': 'med_value', 'type': 'UInt16', 'ptype': int},
    'period': {'ch_field': 'period', 'type': 'UInt16', 'ptype': int},
    'meanvalue': {'ch_field': 'mean_value', 'type': 'UInt16', 'ptype': int},
    'requests': {'ch_field': 'requests', 'type': 'UInt16', 'ptype': int},
    'errorrequests': {'ch_field': 'error_requests', 'type': 'UInt16', 'ptype': int},
    'sentry': {'ch_field': 'source', 'type': 'UInt8', 'ptype': int},
    'act': {'ch_field': 'amount', 'type': 'UInt16', 'ptype': int},
    # {'events': ['quest_complete'], 'ch_field': 'slot_id', 'type': 'UInt8', 'ptype': int},
    'strid': {'ch_field': 'str_id', 'type': 'String', 'ptype': str},
    'c_textures': {'ch_field': 'str_id', 'type': 'String', 'ptype': str},
    'batseas': {'ch_field': 'number_index', 'type': 'UInt16', 'ptype': int},
    'clid': {'ch_field': 'json_str', 'type': 'JSON', 'ptype': int},
    'pid': {'ch_field': 'id', 'type': 'UInt16', 'ptype': int},
    'oids': {'ch_field': 'str_id', 'type': 'String', 'ptype': str},
    'snum': {'ch_field': 'number_index', 'type': 'UInt8', 'ptype': int},
    'atype': {'ch_field': 'award_type', 'type': 'String', 'ptype': int},
    'amount': {'ch_field': 'amount', 'type': 'Uint32', 'ptype': int},
    'cid': {'ch_field': 'id', 'type': 'Uint32', 'ptype': int},
    'ftransf': {'ch_field': 'dummy_flag', 'type': 'Uint8', 'ptype': int},
    'lang': {'ch_field': 'language', 'type': 'String', 'ptype': int},
    'option': {'ch_field': 'source', 'type': 'Uint8', 'ptype': int},
    'rid': {'ch_field': 'str_id', 'type': 'String', 'ptype': int},
    'message': {'ch_field': 'message', 'type': 'String', 'ptype': int},
    'minmem': {'ch_field': 'min_members', 'type': 'Uint8', 'ptype': int},
    'maxmem': {'ch_field': 'max_members', 'type': 'Uint8', 'ptype': int},
    'sresult': {'ch_field': 'json_str', 'type': 'JSON', 'ptype': int},
    'gentype': {'ch_field': 'str_id', 'type': 'String', 'ptype': int},
    'price': {'ch_field': 'amount', 'type': 'Uint32', 'ptype': int},
    'numindex': {'ch_field': 'number_index', 'type': 'Uint32', 'ptype': int},
    'mgnumb': {'ch_field': 'minigame_number', 'type': 'Uint32', 'ptype': int},
    'mgmode': {'ch_field': 'mode', 'type': 'Uint8', 'ptype': int},
    'score': {'ch_field': 'score', 'type': 'Uint16', 'ptype': int},
    'users': {'ch_field': 'user_count', 'type': 'Uint8', 'ptype': int},
    'fps': {'ch_field': 'common_fps', 'type': 'Uint16', 'ptype': int},
    'tid': {'ch_field': 'id', 'type': 'Uint8', 'ptype': int},
    'skip': {'ch_field': 'dummy_flag', 'type': 'UInt8', 'ptype': int},
    'category': {'ch_field': 'type', 'type': 'UInt16', 'ptype': int},
    'olevel': {'ch_field': 'level_value', 'type': 'UInt16', 'ptype': int},
    'status': {'ch_field': 'dummy_flag', 'type': 'UInt8', 'ptype': int},
    'pstart': {'ch_field': 'min_value', 'type': 'String', 'ptype': int},
    'pfinish': {'ch_field': 'max_value', 'type': 'String', 'ptype': int},
    'mlevel': {'ch_field': 'level_value', 'type': 'UInt16', 'ptype': int},
    'ptype': {'ch_field': 'source', 'type': 'UInt8', 'ptype': int},
    'fvote': {'ch_field': 'amount', 'type': 'UInt32', 'ptype': int},
    'bubble': {'ch_field': 'bubble', 'type': 'UInt8', 'ptype': int},
    'fuid': {'ch_field': 'id_2', 'type': 'String', 'ptype': str},
    'questnumber': {'ch_field': 'number_index', 'type': 'UInt32', 'ptype': int},
    'revenue': {'ch_field': 'revenue', 'type': 'Float32', 'ptype': float},
    'distance': {'ch_field': 'distance', 'type': 'UInt32', 'ptype': int},
    'layout': {'ch_field': 'layout', 'type': 'JSON', 'ptype': str},
    'tilesleft': {'ch_field': 'tiles_left', 'type': 'UInt16', 'ptype': int},
    'timer': {'ch_field': 'timer', 'type': 'UInt16', 'ptype': int},
    'tiles': {'ch_field': 'tiles', 'type': 'UInt16', 'ptype': int},
    'tilescol': {'ch_field': 'tiles_collection', 'type': 'UInt16', 'ptype': int},
    'tilessel': {'ch_field': 'tiles_selected', 'type': 'JSON', 'ptype': str},
    'tilesdis': {'ch_field': 'distance', 'type': 'UInt32', 'ptype': int},
    'tilesrem': {'ch_field': 'tiles_removed', 'type': 'UInt32', 'ptype': int},
    'scoretime': {'ch_field': 'score_time', 'type': 'UInt32', 'ptype': int},
    'lives': {'ch_field': 'lives', 'type': 'UInt32', 'ptype': int},
    'boostersused': {'ch_field': 'boosters_used', 'type': 'JSON', 'ptype': str},
    'vip': {'ch_field': 'type', 'type': 'UInt16', 'ptype': int},
    'jsonstr': {'ch_field': 'json_str', 'type': 'JSON', 'ptype': str},
    'value': {'ch_field': 'value', 'type': 'UInt16', 'ptype': int},
    'autoboostersused': {'ch_field': 'auto_boosters_used', 'type': 'UInt32', 'ptype': int},
    'dummy_flag': {'ch_field': 'dummy_flag', 'type': 'UInt8', 'ptype': int},
    'number_index': {'ch_field': 'number_index', 'type': 'UInt16', 'ptype': int},
    'json_str': {'ch_field': 'json_str', 'type': 'JSON', 'ptype': str},
    'level_value': {'ch_field': 'level_value', 'type': 'UInt16', 'ptype': int},
    # telemetry
    'mt_avg': {'ch_field': 'memory_tcache_avg', 'type': 'UInt32', 'ptype': int},
    'mt_max': {'ch_field': 'memory_tcache_max', 'type': 'UInt32', 'ptype': int},
    'gss_count': {'ch_field': 'global_spinner_server_count', 'type': 'UInt32', 'ptype': int},
    'gss_duration': {'ch_field': 'global_spinner_server_duration_total', 'type': 'UInt32', 'ptype': int},
    'gsr_count': {'ch_field': 'global_spinner_resource_count', 'type': 'UInt32', 'ptype': int},
    'gsr_duration': {'ch_field': 'global_spinner_resource_duration_total', 'type': 'UInt32', 'ptype': int},
    'lsr_count': {'ch_field': 'local_spinner_resource_count', 'type': 'UInt32', 'ptype': int},
    'lsr_duration': {'ch_field': 'local_spinner_resource_duration_total', 'type': 'UInt32', 'ptype': int},
    'wsr_count': {'ch_field': 'window_spinner_resource_count', 'type': 'UInt32', 'ptype': int},
    'wsr_duration': {'ch_field': 'window_spinner_resource_duration_total', 'type': 'UInt32', 'ptype': int},
    'sr_count': {'ch_field': 'server_request_count_total', 'type': 'UInt32', 'ptype': int},
    'sr_count_error': {'ch_field': 'server_request_count_error', 'type': 'UInt32', 'ptype': int},
    'sr_time': {'ch_field': 'server_request_time_total', 'type': 'UInt32', 'ptype': int},
    'sr_time_min': {'ch_field': 'server_request_time_min', 'type': 'UInt32', 'ptype': int},
    'sr_time_max': {'ch_field': 'server_request_time_max', 'type': 'UInt32', 'ptype': int},
    'rr_count': {'ch_field': 'resource_request_count_total', 'type': 'UInt32', 'ptype': int},
    'rr_count_error': {'ch_field': 'resource_request_count_error', 'type': 'UInt32', 'ptype': int},
    'rr_time': {'ch_field': 'resource_request_time_total', 'type': 'UInt32', 'ptype': int},
    'rr_time_min': {'ch_field': 'resource_request_time_min', 'type': 'UInt32', 'ptype': int},
    'rr_time_max': {'ch_field': 'resource_request_time_max', 'type': 'UInt32', 'ptype': int},
    'rr_response_size': {'ch_field': 'resource_request_response_size', 'type': 'UInt32', 'ptype': int},
    'rri_count': {'ch_field': 'resource_request_internet_count_total', 'type': 'UInt32', 'ptype': int},
    'rri_count_error': {'ch_field': 'resource_request_internet_count_error', 'type': 'UInt32', 'ptype': int},
    'rri_time': {'ch_field': 'resource_request_internet_time_total', 'type': 'UInt32', 'ptype': int},
    'rri_time_min': {'ch_field': 'resource_request_internet_time_min', 'type': 'UInt32', 'ptype': int},
    'rri_time_max': {'ch_field': 'resource_request_internet_time_max', 'type': 'UInt32', 'ptype': int},
    'rri_response_size': {'ch_field': 'resource_request_internet_response_size', 'type': 'UInt32', 'ptype': int},
}
