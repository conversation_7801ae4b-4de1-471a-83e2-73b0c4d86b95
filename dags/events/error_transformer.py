from typing import Dict
import json
from datetime import datetime
import pytz
import dags.events.config.default as dag_config


class ErrorTransformer:

    # TODO: Удалить здесь и использовать код из lib/
    @staticmethod
    def convert_time_to_utc(date_string_in: str, format_in: str):
        d = datetime.strptime(date_string_in, format_in)
        return datetime.strftime(d.replace(tzinfo=pytz.timezone('UTC')) - d.utcoffset(), '%Y-%m-%d %H:%M:%S')

    @staticmethod
    def process(error: Dict, event: Dict, json_string: str, file_path: str) -> Dict:
        transformed = {}
        transformed['filename'] = file_path
        transformed['json'] = json_string
        transformed['platform_type'] = event['platform'] if 'platform' in event else 'unknown'
        try:
            transformed['date'] = ErrorTransformer.convert_time_to_utc(
                event['date'],
                dag_config.EVENTS_PARAMS['date']['validation']['format']
            ) if 'date' in event else datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S')
        except ValueError:
            transformed['date'] = datetime.strftime(datetime.now(), '%Y-%m-%d %H:%M:%S')

        transformed['eventtype'] = event['event'] if 'event' in event else 'unknown'
        transformed['error_type'] = error['type']
        transformed['error_details'] = json.dumps(error['details'])

        return transformed
