from airflow import DAG
from dags.lib.services.config_service import ConfigService
from dags.lib.services.environment_service import EnvironmentService
from airflow.operators.bash import BashOperator
from airflow.hooks.base import BaseHook
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
import dags.events.config.default as dag_config
from dags.events.file_paths_portions_service import FilePathsPortionsService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService, PARQUET_DATA_TYPE
from json.decoder import JSONDecodeError
import json
from dags.events.event_validator import EventValidator
from dags.events.event_transformer import EventTransformer
from dags.events.error_transformer import ErrorTransformer
import pandas as pd
from dags.lib.services.file_path_service import FilePathService
from glob import glob
import os
import psutil
from dags.lib.operators.data_to_clickhouse_operator import DataToClickHouseOperator
from datetime import datetime
import re
import tarfile
from dags.events.deduplication_service import DeduplicationService
from dags.events.backup_service import BackupService
import codecs

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)
file_paths_service = FilePathService(
    DAG_ID,
    config.get_setting(ConfigService.TMP_FILES_FOLDER),
    {
        dag_config.TMP_EVENTS_FILE_PATH: [],
        dag_config.TMP_ERRORS_FILE_PATH: [],
        dag_config.TMP_FILES_FILE_PATH: [],
        dag_config.TMP_EVENTS_TELEMETRY_FILE_PATH: [],
    }
)

paths_portions = FilePathsPortionsService(
    file_paths_service,
    f'{config.get_setting(ConfigService.EVENTS_FILES_FOLDER)}/[0-9]*/*.log',  # Добавить сразу в путь?
    dag_config.FILE_PATHS_PORTIONS_COUNT
)


def process_memory():
    process = psutil.Process(os.getpid())
    mem_info = process.memory_info()
    return mem_info.rss


def clear_temp_data():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(dag_config.CH_EVENTS_EXTRACTED_TABLE)
    ch.truncate(dag_config.CH_TELEMETRY_EXTRACTED_TABLE)

    for path in glob(file_paths_service.get_glob_pattern()):
        if os.path.exists(path):
            os.remove(path)


def check_deduplication_data(platform: str):
    base_path = config.get_setting(ConfigService.EVENTS_FILES_FOLDER).replace('[platform]', platform)
    deduplication_service = DeduplicationService(platform, config.get_setting('redis_connection'), base_path)
    if (not deduplication_service.is_data_ready()):
        LoggerService.logger.info('Fill deduplication data')
        backup_service = BackupService(platform, base_path)
        backup_service.restore(datetime.now().timestamp() -
                               dag_config.DEDUPLICATION_BUCKET_PERIOD_SECONDS * dag_config.DEDUPLICATION_BUCKETS_COUNT)
        deduplication_service.fill_data()
        backup_service.clear_restore()


def make_file_portions(platform):
    paths_portions.make_portions(platform)

def file_reading_error_handler(e):
    if e is UnicodeDecodeError:
        return f'UnicodeDecodeError {e.msg}', e.end
    raise e

def stream_events(file_path):
    codecs.register_error('file_reading_error_handler', file_reading_error_handler)
    with open(file_path, 'r', errors='file_reading_error_handler') as file:
        line_number = 1
        for line in file:
            error = None
            parsed_line = {}
            try:
                parsed_line = json.loads(line.rstrip())
            except JSONDecodeError as e:
                error = {
                    'type': EventValidator.ERROR_BAD_JSON,
                    'details': {
                        'msg': e.msg,
                        'col': e.colno,
                        'pos': e.pos
                    }
                }
            yield parsed_line, line, error
            line_number += 1


def extract_events(platform, portion_number):
    file_paths = paths_portions.get_portion(platform, portion_number)
    LoggerService.logger.info(f'Files count: {len(file_paths)}')
    if len(file_paths) == 0:
        LoggerService.logger.info(f'No files for processing')
    base_path = config.get_setting(ConfigService.EVENTS_FILES_FOLDER).replace('[platform]', platform)
    deduplication_service = DeduplicationService(platform, config.get_setting('redis_connection'), base_path)
    errors_data = []
    duplicated_data = []
    data = {}
    for file_path, modification_time in file_paths:
        for event, json_string, error in stream_events(file_path):
            if error is not None:
                errors_data.append(ErrorTransformer.process(error, event, json_string, file_path))
                continue
            event = EventTransformer.prepare(event)
            result, errors = EventValidator.validate(event)
            if (len(errors) > 0):
                for error in errors:
                    errors_data.append(ErrorTransformer.process(error, event, json_string, file_path))
            if result == EventValidator.RESULT_BAD_BASE_PARAMS:
                continue
            event = EventTransformer.process(event, result, file_path, errors)
            bucket_timestamp = deduplication_service.get_bucket_timestamp(event['date_obj'])
            del event['date_obj']
            if bucket_timestamp not in data:
                data[bucket_timestamp] = {}
            deduplication_key = DeduplicationService.get_bucket_value(event)
            if deduplication_key in data[bucket_timestamp]:
                duplicated_data.append(event)
                continue
            data[bucket_timestamp][deduplication_key] = event

    deduplicated_data = []
    telemetry_data = []
    for bucket_timestamp, bucket_data in data.items():
        keys = list(bucket_data.keys())
        for i in range(0, len(keys), DeduplicationService.BATCH_SIZE):
            batch_keys = keys[i:i + DeduplicationService.BATCH_SIZE]
            duplicated_keys = deduplication_service.get_duplicated_keys(
                bucket_timestamp,
                batch_keys
            )
            for key in duplicated_keys:
                duplicated_data.append(bucket_data[key])
                del bucket_data[key]
            deduplication_service.add_data(bucket_timestamp, set(batch_keys) - set(duplicated_keys))

        for key, event in bucket_data.items():
            if event['eventtype'] != 'telemetry':
                continue
            telemetry_data.append(event)
            bucket_data[key] = EventTransformer.leave_base_params(event)
        deduplicated_data.extend(bucket_data.values())
        LoggerService.logger.info(
            f'bucket {bucket_timestamp}, keys: {len(keys)}, duplicates: {len(keys) - len(bucket_data.values())}')

    del data

    file_path = file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_EVENTS_FILE_PATH)
    data_frame = pd.DataFrame(deduplicated_data)
    data_frame.to_parquet(file_path)

    file_path = file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_EVENTS_TELEMETRY_FILE_PATH)
    data_frame = pd.DataFrame(telemetry_data)
    data_frame.to_parquet(file_path)

    file_path = file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_ERRORS_FILE_PATH)
    data_frame = pd.DataFrame(errors_data)
    data_frame.to_parquet(file_path)

    file_path = file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_DUPLICATED_EVENTS_FILE_PATH)
    data_frame = pd.DataFrame(duplicated_data)
    data_frame.to_parquet(file_path)


def process_source_files(platform):
    # TODO: Можно будет перенести логику в BackupService, так как работа с бэкапом теперь более чем в одной точке
    # В соответствии с конфигом копируем исходные файлы Fluent в папку бэкапа, а остальные удаляем
    now_timestamp = datetime.now().timestamp()
    backup_path = f'{config.get_setting(ConfigService.EVENTS_FILES_FOLDER).replace("[platform]", platform)}/' \
                  f'{dag_config.BACKUP_FOLDER}'
    if not os.path.exists(backup_path):
        os.makedirs(backup_path)

    # Переносим обработанные файлы в папку бэкапа
    for file_path, modification_time in paths_portions.stream_all(platform):
        if now_timestamp - modification_time <= dag_config.BACKUP_SECONDS:
            os.rename(file_path, f'{backup_path}/{os.path.basename(file_path)}')
        else:
            os.remove(file_path)

    # Архивируем почасово файлы, оставляя несжатыми файлы за наиболее свежий час
    regexp = re.compile('\.(\d+_\d+)\.')
    date_max = None
    files_to_compress = {}
    for file_path in glob(f'{backup_path}/*.log'):
        result = regexp.search(file_path)
        dt = datetime.strptime(result.group(1), '%Y%m%d_%H%M')
        day_hour_string = dt.strftime('%Y-%m-%d_%H')
        if day_hour_string not in files_to_compress:
            files_to_compress[day_hour_string] = []
        files_to_compress[day_hour_string].append(file_path)
        if date_max is None or dt > date_max:
            date_max = dt

    if date_max is not None:
        day_hour_string = date_max.strftime('%Y-%m-%d_%H')
        del files_to_compress[day_hour_string]

    # Добавляем пачки файлов почасово в архив, затем исходные файлы удаляем
    for day_hour_string, files in files_to_compress.items():
        tar_path = f'{backup_path}/{day_hour_string}.tar.gz'
        with tarfile.open(tar_path, "w:gz") as tar:
            for file in files:
                tar.add(file, arcname=os.path.basename(file))
        for file in files:
            os.remove(file)

    # Удаляем слишком старые архивы за час
    for file_path in glob(f'{backup_path}/*.tar.gz'):
        file_stats = os.stat(file_path)
        if now_timestamp - file_stats.st_mtime > dag_config.BACKUP_SECONDS:
            os.remove(file_path)


def clear_deduplication_buckets(platform):
    base_path = config.get_setting(ConfigService.EVENTS_FILES_FOLDER).replace('[platform]', platform)
    deduplication_service = DeduplicationService(platform, config.get_setting('redis_connection'), base_path)
    deduplication_service.remove_expired_buckets()


def load_events():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.execute(f'''
        INSERT INTO {dag_config.CH_EVENTS_TABLE} SELECT * 
        FROM {conn_etl.schema}.{dag_config.CH_EVENTS_EXTRACTED_TABLE}
    ''')

def load_telemetry():
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.execute(f'''
        INSERT INTO {dag_config.CH_TELEMETRY_TABLE} SELECT * 
        FROM {conn_etl.schema}.{dag_config.CH_TELEMETRY_EXTRACTED_TABLE}
    ''')


def remove_empty_folders(platform):
    path = f'{config.get_setting(ConfigService.EVENTS_FILES_FOLDER).replace("[platform]", platform)}/[0-9]*'
    for directory in glob(path):
        if not os.path.isdir(directory):
            continue
        if os.listdir(directory):
            continue
        os.rmdir(directory)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:
    clear_temp_data_task = PythonOperator(
        task_id='clear_temp_data',
        python_callable=clear_temp_data)

    platforms = config.get_setting('platforms')

    check_deduplication_data_tasks = [
        PythonOperator(
            task_id=f'check_deduplication_data_{platform}',
            python_callable=check_deduplication_data,
            op_kwargs={
                'platform': platform
            }
        )
        for platform in platforms
    ]

    make_file_portions_tasks = [
        PythonOperator(
            task_id=f'make_file_portions_{platform}',
            python_callable=make_file_portions,
            op_kwargs={
                'platform': platform
            }
        )
        for platform in platforms
    ]

    start_check_deduplication_data_tasks = EmptyOperator(
        task_id='start_check_deduplication_data'
    )

    start_extract_events_tasks = EmptyOperator(
        task_id='start_extract_events'
    )

    extract_events_tasks = [
        [
            PythonOperator(
                task_id=f'extract_{platform}_{portion_number}',
                python_callable=extract_events,
                op_kwargs={
                    'platform': platform,
                    'portion_number': portion_number
                }
            )
            for portion_number in range(1, dag_config.FILE_PATHS_PORTIONS_COUNT + 1)
        ]
        for platform in config.get_setting('platforms')
    ]

    clear_deduplication_buckets_tasks = [
        PythonOperator(
            task_id=f'clear_deduplication_buckets_{platform}',
            python_callable=clear_deduplication_buckets,
            op_kwargs={
                'platform': platform
            }
        )
        for platform in platforms
    ]

    import_events_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_EVENTS_FILE_PATH),
            table_name=dag_config.CH_EVENTS_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'import_events_{platform}_{portion_number}'
        )
        for platform in platforms
        for portion_number in range(1, dag_config.FILE_PATHS_PORTIONS_COUNT + 1)
    ]

    import_telemetry_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_EVENTS_TELEMETRY_FILE_PATH),
            table_name=dag_config.CH_TELEMETRY_EXTRACTED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'import_telemetry_{platform}_{portion_number}'
        )
        for platform in platforms
        for portion_number in range(1, dag_config.FILE_PATHS_PORTIONS_COUNT + 1)
    ]

    import_errors_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_ERRORS_FILE_PATH),
            table_name=dag_config.CH_ERRORS_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'import_errors_{platform}_{portion_number}'
        )
        for platform in platforms
        for portion_number in range(1, dag_config.FILE_PATHS_PORTIONS_COUNT + 1)
    ]

    import_duplicated_events_tasks = [
        DataToClickHouseOperator(
            connection_id=config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL),
            file_path=file_paths_service.get_path(f'{platform}_{portion_number}', dag_config.TMP_DUPLICATED_EVENTS_FILE_PATH),
            table_name=dag_config.CH_EVENTS_DUPLICATED_TABLE,
            data_type=PARQUET_DATA_TYPE,
            task_id=f'import_events_duplicated_{platform}_{portion_number}'
        )
        for platform in platforms
        for portion_number in range(1, dag_config.FILE_PATHS_PORTIONS_COUNT + 1)
    ]

    start_process_source_files_task = EmptyOperator(
        task_id='start_process_source_files'
    )

    process_source_files_tasks = [
        PythonOperator(
            task_id=f'process_source_files_{platform}',
            python_callable=process_source_files,
            op_kwargs={
                'platform': platform
            }
        )
        for platform in platforms
    ]

    load_events_task = PythonOperator(
        task_id=f'load_events',
        python_callable=load_events,
    )

    load_telemetry_task = PythonOperator(
        task_id=f'load_telemetry',
        python_callable=load_telemetry,
    )

    remove_empty_folders_tasks = [
        PythonOperator(
            task_id=f'remove_empty_folders_{platform}',
            python_callable=remove_empty_folders,
            op_kwargs={
                'platform': platform
            }
        )
        for platform in platforms
    ]

    clear_temp_data_task >> make_file_portions_tasks >> start_check_deduplication_data_tasks >> \
    check_deduplication_data_tasks >> start_extract_events_tasks
    import_task = import_events_tasks.pop(0)
    for tasks in extract_events_tasks:
        prev_task = tasks.pop(0)
        start_extract_events_tasks >> prev_task
        for task in tasks:
            prev_task >> task
            prev_task = task
        prev_task >> import_task
    for task in import_events_tasks:
        import_task >> task
        import_task = task
    for task in import_telemetry_tasks:
        import_task >> task
        import_task = task
    import_task >> load_events_task >> load_telemetry_task >> clear_deduplication_buckets_tasks >> start_process_source_files_task >> \
    process_source_files_tasks
    import_task = import_errors_tasks.pop(0)
    process_source_files_tasks >> import_task
    for task in import_errors_tasks:
        import_task >> task
        import_task = task
    import_events_duplicated_task = import_duplicated_events_tasks.pop(0)
    import_task >> import_events_duplicated_task
    for task in import_duplicated_events_tasks:
        import_events_duplicated_task >> task
        import_events_duplicated_task = task
    import_events_duplicated_task >> remove_empty_folders_tasks

if __name__ == '__main__':
    dag.cli()
