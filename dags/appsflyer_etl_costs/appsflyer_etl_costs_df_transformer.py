import pandas as pd


class AppsFlyerEtlCostsDfTransformer:
    @staticmethod
    def process(df: pd.DataFrame):

        # При переводе аккаунта на новое юр. лицо немного сменились настройки экспорта, появились новые колонки
        for column in ['cost_without_fees', 'original_cost_without_fees', 'fees', 'keyword_id', 'keyword_term', 'os']:
            df.drop(
                column,
                axis=1,
                inplace=True
            )

        df.replace(
            {
                'date': {'None': ''},
                'campaign_id': {'None': ''},
                'campaign': {'None': ''},
                'geo': {'None': ''},
                'media_source': {'None': ''},
                'adset_id': {'None': ''},
                'adset': {'None': ''},
                'ad_id': {'None': ''},
                'ad': {'None': ''},
                'channel': {'None': ''},
                'site_id': {'None': ''},
                'agency': {'None': ''}
            },
            inplace=True
        )

