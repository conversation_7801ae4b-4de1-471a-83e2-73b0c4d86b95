from typing import Dict
import io
import re
import pandas as pd
from datetime import datetime
from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import dags.appsflyer_etl_costs.config.default as dag_config
from dags.lib.helpers.date_helper import str_to_date_time
from dags.appsflyer_etl_costs.appsflyer_etl_costs_df_transformer import AppsFlyerEtlCostsDfTransformer


DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

AWS_COSTS_ETL_BUCKET = config.get_setting(dag_config.AWS_COSTS_ETL_BUCKET_KEY)
APPS_FLYER_APP_ID_ANDROID = config.get_setting(dag_config.APPS_FLYER_APP_ID_ANDROID_KEY)
APPS_FLYER_APP_ID_IOS = config.get_setting(dag_config.APPS_FLYER_APP_ID_IOS_KEY)

dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}


def truncate_etl_table(table_name: str):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    ch.truncate(table_name)
    LoggerService.logger.info(f'Truncated table "{table_name}"')


def get_file_date(filename_in: str) -> datetime:
    return datetime.strptime(re.findall('\d{4}-\d{2}-\d{2}', re.findall(r'dt=\d{4}-\d{2}-\d{2}', filename_in)[0])[0], '%Y-%m-%d').date()


def get_min_max_processed_dates() -> Dict:
    try:
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        sql = f'''
            SELECT
            start_date,
            end_date
            FROM
            {dag_config.CH_AWS_COST_ETL_FILES_SUMMARY_VIEW} x        
        '''
        return ch.select_with_columns_names(sql)[0]
    except Exception as e:
        raise


def load_etl_costs():
    try:
        now = datetime.strptime(datetime.strftime(datetime.now(), '%Y-%m-%d'), '%Y-%m-%d').date()
        start_end_processed_dates = get_min_max_processed_dates()
        release_date = str_to_date_time(dag_config.MIN_START_DATE).date()
        start_date = str_to_date_time(start_end_processed_dates['start_date']).date()
        end_date = str_to_date_time(start_end_processed_dates['end_date']).date()
        LoggerService.logger.info(f'Now = {now}, last processed start_date = {start_date} and end_date = {end_date}')
        s3_hook = S3Hook(aws_conn_id=config.get_connection(ConfigService.CONN_AWS_S3))
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f'system reload dictionary {dag_config.CH_AWS_COST_ETL_FILES_DICT}')
        keys = s3_hook.list_keys(AWS_COSTS_ETL_BUCKET, page_size=dag_config.AWS_PAGE_SIZE)
        for key in keys:
            if key.find('.parquet') > 0:
                file_date = get_file_date(filename_in=key)
                if (file_date >= release_date) and (file_date < now) and (not (start_date <= file_date <= end_date)):
                    sql = f'''
                        SELECT count(1) as flag_exists
                        FROM {dag_config.CH_AWS_COST_ETL_FILES_DICT}
                        WHERE key = sipHash64('{key}')
                    '''
                    if not ch.select_with_columns_names(sql)[0]['flag_exists']:
                        buffer = io.BytesIO()
                        obj = s3_hook.get_key(bucket_name=AWS_COSTS_ETL_BUCKET, key=key)
                        obj.download_fileobj(buffer)
                        df = pd.read_parquet(buffer)
                        df['filename'] = key
                        AppsFlyerEtlCostsDfTransformer.process(df)
                        ln = len(df.index)
                        ch.insert_dataframe(df=df, table_name=dag_config.CH_AWS_COST_ETL_EXTRACTED_TABLE)
                        ch.insert(
                            f'INSERT INTO {dag_config.CH_AWS_COST_ETL_FILES_TABLE} (filename, count_rows) VALUES ',
                            [{'filename': key, 'count_rows': ln}]
                        )
                        LoggerService.logger.info(f'Successful load {ln} rows to '
                                                  f'{dag_config.CH_AWS_COST_ETL_EXTRACTED_TABLE} from {key}')
    except Exception as e:
        raise


def move_etl_costs():
    try:
        to_table = dag_config.CH_AWS_COST_ETL_TABLE
        from_table = dag_config.CH_AWS_COST_ETL_EXTRACTED_TABLE
        LoggerService.start_move_data(from_table, to_table)
        ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
        ch.execute(f"INSERT INTO {to_table} SELECT * FROM {from_table} FINAL "
                   f"WHERE app_id IN ('{APPS_FLYER_APP_ID_ANDROID}', '{APPS_FLYER_APP_ID_IOS}')")
    except Exception as e:
        LoggerService.error_move_data(from_table, to_table, str(e))
        raise


with DAG(**dg) as dag:
    truncate_etl_costs_table_task = PythonOperator(
        task_id=f'truncate_{dag_config.CH_AWS_COST_ETL_EXTRACTED_TABLE}',
        python_callable=truncate_etl_table,
        op_kwargs={
            'table_name': dag_config.CH_AWS_COST_ETL_EXTRACTED_TABLE
        }
    )

    load_etl_costs_task = PythonOperator(
        task_id='load_etl_costs',
        python_callable=load_etl_costs
    )

    move_etl_costs_task = PythonOperator(
        task_id='move_etl_consts',
        python_callable=move_etl_costs
    )

    truncate_etl_costs_table_task >> load_etl_costs_task >> move_etl_costs_task

if __name__ == '__main__':
    dag.cli()
