from datetime import timedelta

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for load etl cost raw data from AWS S3',
}

AWS_COSTS_ETL_BUCKET_KEY = 'aws_costs_etl_bucket'

CH_AWS_COST_ETL_TABLE = 'aws_cost_etl'
CH_AWS_COST_ETL_FILES_TABLE = 'aws_cost_etl_files'
CH_AWS_COST_ETL_FILES_DICT = 'aws_cost_etl_files_dict'
CH_AWS_COST_ETL_FILES_SUMMARY_VIEW = 'aws_cost_etl_files_summary'
CH_AWS_COST_ETL_EXTRACTED_TABLE = 'aws_cost_etl_extracted'

APPS_FLYER_APP_ID_ANDROID_KEY = 'apps_flyer_app_id_android'
APPS_FLYER_APP_ID_IOS_KEY = 'apps_flyer_app_id_ios'

AWS_PAGE_SIZE = 1024
MIN_START_DATE = '2023-10-24'

SETTINGS = {
    'aws_costs_etl_bucket': 'af-xpend-cost-etl-acc-yxaoxwwv-progameslab',
    'apps_flyer_app_id_android': 'com.progameslab.magic.seasons2024.farm.match.collect',
    'apps_flyer_app_id_ios': 'id6463635391'
}

