from airflow import DAG
from dags.lib.services.environment_service import EnvironmentService
from dags.lib.services.config_service import ConfigService
from dags.lib.services.logger_service import LoggerService
from dags.lib.services.clickhouse_service import ClickhouseService
from airflow.operators.python import PythonOperator
from airflow.hooks.base import <PERSON>Hook
from datetime import datetime, timedelta, timezone
import dags.agg_retention.config.default as dag_config
from dags.lib.helpers.date_helper import str_to_date_time, get_current_date
from airflow.exceptions import AirflowSkipException

DAG_ID = EnvironmentService.get_dag_id(__file__)
ENV = EnvironmentService.get_env()
config = ConfigService(ENV, DAG_ID)

BASE_PATH = config.get_setting(ConfigService.TMP_FILES_FOLDER)

def calculate_prev_retention(platform: str):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    min = ch.get_first(f"SELECT MIN(dt) FROM agg_retention WHERE platform_type='{platform}'")[0]

    start_date = str_to_date_time('2023-10-01').date()

    LoggerService.logger.info(f'Min: {min} ({type(min)})')
    if min < start_date:
        return

    date = min - timedelta(days=1)

    sql = dag_config.SQL_RETENTION.format(
        schema_etl=conn_etl.schema,
        platform=platform,
        date=date
    )
    LoggerService.logger.info(sql)
    ch.execute(sql)

def calculate_dau(platform: str):

    date_yesterday = get_current_date() - timedelta(days=1)

    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    max = ch.get_max_column_value(
        'agg_retention',
        'dt',
        {'platform_type': platform},
    )

    LoggerService.logger.info(f'Yesterday: {date_yesterday} ({type(date_yesterday)}), max: {max} ({type(max)})')
    if max >= date_yesterday:
        raise AirflowSkipException

    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))
    sql = dag_config.SQL_DAU.format(
        schema_etl=conn_etl.schema,
        platform=platform,
        date=date_yesterday
    )
    LoggerService.logger.info(sql)
    ch.execute(sql)


def calculate_retention(platform: str):
    ch = ClickhouseService(config.get_connection(ConfigService.CONN_CLICKHOUSE_MAIN))
    conn_etl = BaseHook.get_connection(config.get_connection(ConfigService.CONN_CLICKHOUSE_ETL))

    date_yesterday = get_current_date() - timedelta(days=1)
    sql = dag_config.SQL_RETENTION.format(
        schema_etl=conn_etl.schema,
        platform=platform,
        date=date_yesterday
    )
    LoggerService.logger.info(sql)
    ch.execute(sql)


dg = config.get_dag_arguments() | {
    'dag_id': DAG_ID,
    'tags': [ENV, DAG_ID],
}

with DAG(**dg) as dag:

    calculate_prev_retention_tasks = [
        PythonOperator(
            task_id=f'calculate_prev_retention_{platform}',
            python_callable=calculate_prev_retention,
            op_kwargs={
                'platform': platform
            },
        )
        for platform in config.get_setting(dag_config.PLATFORMS_KEY)
    ]

    calculate_dau_tasks = [
        PythonOperator(
            task_id=f'calculate_dau_{platform}',
            python_callable=calculate_dau,
            op_kwargs={
                'platform': platform
            },
        )
        for platform in config.get_setting(dag_config.PLATFORMS_KEY)
    ]

    calculate_retention_tasks = [
        PythonOperator(
            task_id=f'calculate_retention_{platform}',
            python_callable=calculate_retention,
            op_kwargs={
                'platform': platform
            },
        )
        for platform in config.get_setting(dag_config.PLATFORMS_KEY)
    ]

    calculate_retention_task = calculate_prev_retention_tasks.pop(0)
    for task in calculate_prev_retention_tasks:
        calculate_retention_task >> task
        calculate_retention_task = task

    calculate_dau_task = calculate_dau_tasks.pop(0)
    calculate_retention_task >> calculate_dau_task
    for task in calculate_dau_tasks:
        calculate_dau_task >> task
        calculate_dau_task = task

    calculate_retention_task = calculate_retention_tasks.pop(0)
    calculate_dau_task >> calculate_retention_task
    for task in calculate_retention_tasks:
        calculate_retention_task >> task
        calculate_retention_task = task

if __name__ == '__main__':
    dag.cli()
