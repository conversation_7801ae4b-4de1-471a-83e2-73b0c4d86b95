from datetime import timedelta
from dags.lib.services.platform_service import PlatformService

DAG_ARGS = {
    'schedule_interval': timedelta(days=1),
    'description': 'DAG for calculating aggregated retention data',
}

SETTINGS = {
    'platforms': [PlatformService.PLATFORM_VK, PlatformService.PLATFORM_OK, PlatformService.PLATFORM_IOS,
                  PlatformService.PLATFORM_ANDROID, PlatformService.PLATFORM_WEB]
}

PLATFORMS_KEY = 'platforms'

SQL_DAU = '''
    INSERT INTO {schema_etl}.dau (platform_type, user_id, date_start)
    SELECT DISTINCT platform_type, user_id, toDate(date_start, 'Europe/Moscow') AS dt 
    FROM user_sessions
    WHERE platform_type = '{platform}' AND dt = toDate('{date}') 
'''

SQL_RETENTION = '''
    INSERT INTO agg_retention (platform_type, dt, install_dt, day_number, total_users, active_users, 
        active_10_days_ago, churned_users, reactivated_users)
    WITH
    users AS (
        SELECT DISTINCT user_id
             , toDate(install_time, 'Europe/Moscow') AS install_dt
          FROM users_info
         WHERE platform_type = '{platform}' AND install_dt <= toDate('{date}')
    ),
    dau AS (
        SELECT user_id
          FROM {schema_etl}.dau
         WHERE platform_type = '{platform}' AND date_start = toDate('{date}')
    ),
    dau_10_days_ago AS (
        SELECT user_id
          FROM {schema_etl}.dau
         WHERE platform_type = '{platform}' AND date_start = toDate('{date}') - INTERVAL 10 DAY
    ),
    not_churn AS (
        SELECT user_id
          FROM {schema_etl}.dau
         WHERE platform_type = '{platform}' AND date_start BETWEEN toDate('{date}') - INTERVAL 9 DAY AND toDate('{date}')
    ),
    reactivation AS (
        SELECT user_id
          FROM {schema_etl}.dau
         WHERE platform_type = '{platform}' AND date_start BETWEEN toDate('{date}') - INTERVAL 10 DAY AND toDate('{date}')
      GROUP BY 1
        HAVING MAX(date_start) = toDate('{date}')
           AND COUNT(DISTINCT date_start) = 1
    ),
    activity AS (
        SELECT toDate('{date}') AS dt
             , install_dt
             , SUM(CASE WHEN user_id IN (SELECT user_id FROM dau)
                        THEN 1 
                        ELSE 0 
                        END) AS active_users_
             , COUNT(*) AS total_users 
             , IF(dt=install_dt, total_users, active_users_) AS active_users
             , SUM(CASE WHEN user_id IN (SELECT user_id FROM dau_10_days_ago)
                        THEN 1 
                        ELSE 0 
                        END)  AS active_10_days_ago
             , SUM(CASE WHEN user_id NOT IN (SELECT user_id FROM not_churn) 
                         AND user_id IN (SELECT user_id FROM dau_10_days_ago)
                        THEN 1 
                        ELSE 0 
                        END) AS churned_users
             , SUM(CASE WHEN user_id IN (SELECT user_id FROM reactivation) AND dt > install_dt + INTERVAL 10 DAY
                        THEN 1 
                        ELSE 0 
                        END) AS reactivated_users
          FROM users 
      GROUP BY 1, 2
    ) 
    SELECT '{platform}', dt, install_dt, dateDiff('day', install_dt, dt) AS day_number, total_users, active_users, 
        active_10_days_ago, churned_users, reactivated_users
    FROM activity
'''