-- Вспомогательные таблицы

DROP TABLE IF EXISTS datamarts_etl_dev.appsflyer_cost_extracted;
CREATE TABLE datamarts_etl_dev.appsflyer_cost_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `app_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_id_human` LowCardinality(String) CODEC(LZ4HC(0)),
    `media_source` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `date` Date CODEC(Delta(2), LZ4),
    `agency` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `campaign` String DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `channel` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `geo` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `site_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `timezone` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_account` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `installs` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_attributions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_engagements` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `original_cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `original_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `reported_impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_conversions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ctr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ecpm` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cpi` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ccvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `reported_cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `install_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `click_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `impression_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ecpc` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `video_25p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_50p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_75p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_completions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `campaign_objective` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_cost_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_strategy` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_bid_strategy` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `original_bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0))
)
ENGINE = ReplacingMergeTree
ORDER BY (date, app_id, campaign_id, campaign, geo, media_source, adset_id, adset, ad_id, ad, channel, site_id, agency)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS datamarts_etl_dev.appsflyer_cost;
CREATE TABLE datamarts_etl_dev.appsflyer_cost
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `app_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_id_human` LowCardinality(String) CODEC(LZ4HC(0)),
    `media_source` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `date` Date CODEC(Delta(2), LZ4),
    `agency` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `campaign` String DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `channel` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `geo` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `site_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `timezone` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_account` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `installs` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_attributions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_engagements` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `original_cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `original_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `reported_impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_conversions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ctr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ecpm` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cpi` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ccvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `reported_cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `install_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `click_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `impression_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ecpc` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `video_25p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_50p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_75p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_completions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `campaign_objective` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_cost_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_strategy` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_bid_strategy` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `original_bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0))
)
ENGINE = ReplacingMergeTree
ORDER BY (date, app_id, campaign_id, campaign, geo, media_source, adset_id, adset, ad_id, ad, channel, site_id, agency)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS datamarts_dev.appsflyer_cost;
CREATE VIEW datamarts_dev.appsflyer_cost AS SELECT * FROM datamarts_etl_dev.appsflyer_cost FINAL;
