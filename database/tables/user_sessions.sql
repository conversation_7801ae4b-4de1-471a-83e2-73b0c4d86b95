
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.user_sessions_extracted;
CREATE TABLE elka2024_etl_dev.user_sessions_extracted
(
    `_id` UInt64 CODEC(T64, LZ4),
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `session_number` UInt32 CODEC(T64, LZ4),
    `test_id` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `test_group` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `date_start` DateTime CODEC(DoubleDelta, LZ4),
    `browser_name` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_vendor` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_model` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `referrer_source` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `google_services` Nullable(UInt8) CODEC(T64, LZ4),
    `level` Nullable(UInt32) CODEC(T64, LZ4),
    `factory_level` Nullable(UInt16) CODEC(T64, LZ4),
    `merge_level` Nullable(UInt16) CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `cash` Nullable(UInt32) CODEC(T64, LZ4),
    `energy` Nullable(UInt64) CODEC(T64, LZ4),
    `app_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `server_static` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `os` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `ip` Nullable(String) CODEC(LZ4HC(0)),
    `device_id` Nullable(String) CODEC(LZ4HC(0)),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, date_start)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.user_sessions;
CREATE TABLE elka2024_etl_dev.user_sessions
(
    `_id` UInt64 CODEC(T64, LZ4),
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `session_number` UInt32 CODEC(T64, LZ4),
    `test_id` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `test_group` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `date_start` DateTime CODEC(DoubleDelta, LZ4),
    `date_finish` Nullable(DateTime) CODEC(DoubleDelta, LZ4),
    `browser_name` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_vendor` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_model` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `referrer_source` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `google_services` Nullable(UInt8) CODEC(T64, LZ4),
    `level` Nullable(UInt32) CODEC(T64, LZ4),
    `factory_level` Nullable(UInt16) CODEC(T64, LZ4),
    `merge_level` Nullable(UInt16) CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `cash` Nullable(UInt32) CODEC(T64, LZ4),
    `energy` Nullable(UInt64) CODEC(T64, LZ4),
    `app_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `server_static` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `os` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `ip` Nullable(String) CODEC(LZ4HC(0)),
    `device_id` Nullable(String) CODEC(LZ4HC(0)),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4)
)
ENGINE = ReplacingMergeTree(_insert_datetime)
PARTITION BY (platform_type, toYYYYMM(date_start))
ORDER BY (platform_type, date_start, user_id, session_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.events_last_by_session_part;
CREATE TABLE elka2024_etl_dev.events_last_by_session_part
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1))
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYYYYMM(date))
ORDER BY (platform_type, date)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.user_sessions;
CREATE VIEW elka2024_dev.user_sessions
AS SELECT * FROM elka2024_etl_dev.user_sessions FINAL;