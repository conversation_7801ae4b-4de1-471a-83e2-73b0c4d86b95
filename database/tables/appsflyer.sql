-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_non_organic_in_app_events_report_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_non_organic_in_app_events_report_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(String) DEFAULT NULL CODEC(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_non_organic_installs_report_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_non_organic_installs_report_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(String) DEFAULT NULL CODEC(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_non_organic_uninstalls_report_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_non_organic_uninstalls_report_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(String) DEFAULT NULL CODEC(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_organic_in_app_events_report_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_organic_in_app_events_report_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(String) DEFAULT NULL CODEC(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_organic_installs_report_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_organic_installs_report_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(String) DEFAULT NULL CODEC(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_organic_uninstalls_report_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_organic_uninstalls_report_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(String) DEFAULT NULL CODEC(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_skan_aggregated_performance_report_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_skan_aggregated_performance_report_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `Date` Date,
    `Media Source (pid)` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Campaign (c)` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Campaign ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Site ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Adset` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Adset ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Ad ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Country` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `AF Attribution Flag` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Impressions` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `Clicks` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `CTR` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Click Through Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `View Through Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Null Conversion Value Rate` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Conversion Rate` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Converted Users` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Converted Users/Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Total Revenue` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `Total Cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ROI` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ARPU` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Average eCPI` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_purchase (Unique user)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `af_purchase (Event counter)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `af_skad_revenue (Unique users)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `af_skad_revenue (Event counter)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `app_id` LowCardinality(String)
)
ENGINE = MergeTree
ORDER BY (app_id, Date)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.aws_cost_etl_extracted;
CREATE TABLE elka2024_etl_dev.aws_cost_etl_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `app_id` String CODEC(LZ4HC(0)),
    `media_source` String DEFAULT '' CODEC(LZ4HC(0)),
    `date` String DEFAULT '' CODEC(LZ4HC(0)),
    `agency` String DEFAULT '' CODEC(LZ4HC(0)),
    `campaign` String DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `channel` String DEFAULT '' CODEC(LZ4HC(0)),
    `geo` String DEFAULT '' CODEC(LZ4HC(0)),
    `site_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `timezone` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_account` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `installs` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_attributions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_engagements` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `original_cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `original_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `reported_impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_conversions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ctr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ecpm` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cpi` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ccvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `reported_cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `install_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `click_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `impression_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ecpc` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `video_25p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_50p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_75p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_completions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `campaign_objective` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_strategy` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_bid_strategy` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `original_bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0))
)
ENGINE = ReplacingMergeTree
ORDER BY (date, app_id, campaign_id, campaign, geo, media_source, adset_id, adset, ad_id, ad, channel, site_id, agency)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.aws_cost_etl;
CREATE TABLE elka2024_etl_dev.aws_cost_etl
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `app_id` String CODEC(LZ4HC(0)),
    `media_source` String DEFAULT '' CODEC(LZ4HC(0)),
    `date` String DEFAULT '' CODEC(LZ4HC(0)),
    `agency` String DEFAULT '' CODEC(LZ4HC(0)),
    `campaign` String DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset` String DEFAULT '' CODEC(LZ4HC(0)),
    `adset_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad` String DEFAULT '' CODEC(LZ4HC(0)),
    `ad_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `channel` String DEFAULT '' CODEC(LZ4HC(0)),
    `geo` String DEFAULT '' CODEC(LZ4HC(0)),
    `site_id` String DEFAULT '' CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `timezone` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_account` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `installs` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_attributions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `re_engagements` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `original_cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `original_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `reported_impressions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_clicks` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `reported_conversions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ctr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ecpm` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cpi` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ccvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `cvvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `reported_cvr` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `install_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `click_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `impression_diff` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `ecpc` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `video_25p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_50p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_75p_views` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `video_completions` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `campaign_objective` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_strategy` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_bid_strategy` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `original_bid_amount` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0))
)
ENGINE = ReplacingMergeTree
ORDER BY (date, app_id, campaign_id, campaign, geo, media_source, adset_id, adset, ad_id, ad, channel, site_id, agency)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_cohort_user_acquisition_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_cohort_user_acquisition_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `conversion_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `days_post_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `conversion_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_date` Date CODEC(Delta(2), LZ4),
    `event_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `media_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `channel` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `geo` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `agency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `selected_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `unique_users` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `event_count` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `revenue_usd` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `revenue_selected_currency` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `app_id` String CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0)),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY event_date
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.appsflyer_marketing_campaign_extracted;
CREATE TABLE elka2024_etl_dev.appsflyer_marketing_campaign_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `skad_conversion_value` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `skad_ad_network_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_campaign_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_adset_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_adset_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_ad_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_redownload` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_source_app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_fidelity_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `geo` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `arrival_date` Date,
    `count` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY arrival_date
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.aws_cost_etl_files;
CREATE TABLE elka2024_etl_dev.aws_cost_etl_files
(
    `filename` String,
    `count_rows` UInt64,
    `insert_datetime` DateTime DEFAULT now()
)
ENGINE = MergeTree
ORDER BY filename
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.aws_cost_etl_files_summary;
CREATE VIEW elka2024_etl_dev.aws_cost_etl_files_summary
(
    `start_date` String,
    `end_date` String
) AS
SELECT
    toString(min(toDate(extract(extract(x.filename, 'dt=[\\d]{4}-[\\d]{2}-[\\d]{2}'), '[\\d]{4}-[\\d]{2}-[\\d]{2}')))) AS start_date,
    toString(max(toDate(extract(extract(x.filename, 'dt=[\\d]{4}-[\\d]{2}-[\\d]{2}'), '[\\d]{4}-[\\d]{2}-[\\d]{2}')))) AS end_date
FROM elka2024_etl_dev.aws_cost_etl_files AS x;

CREATE VIEW IF NOT EXISTS elka2024_etl_dev.aws_cost_etl_files_vw
(
    `key` UInt64,
    `value` String
) AS
SELECT
    sipHash64(filename) AS key,
    filename AS value
FROM elka2024_etl_dev.aws_cost_etl_files;

DROP DICTIONARY IF EXISTS elka2024_etl_dev.aws_cost_etl_files_dict;
CREATE DICTIONARY elka2024_etl_dev.aws_cost_etl_files_dict
(
    `key` UInt64,
    `value` String
)
PRIMARY KEY key
SOURCE(CLICKHOUSE(HOST 'localhost' PORT 9000 USER 'user' PASSWORD 'pass' DB 'elka2024_etl_dev' TABLE 'aws_cost_etl_files_vw'))
LIFETIME(MIN 0 MAX 300)
LAYOUT(SPARSE_HASHED());

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.appsflyer_non_organic_in_app_events_report;
CREATE TABLE elka2024_dev.appsflyer_non_organic_in_app_events_report
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_non_organic_installs_report;
CREATE TABLE elka2024_dev.appsflyer_non_organic_installs_report
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_non_organic_uninstalls_report;
CREATE TABLE elka2024_dev.appsflyer_non_organic_uninstalls_report
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_organic_in_app_events_report;
CREATE TABLE elka2024_dev.appsflyer_organic_in_app_events_report
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_organic_installs_report;
CREATE TABLE elka2024_dev.appsflyer_organic_installs_report
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_organic_uninstalls_report;
CREATE TABLE elka2024_dev.appsflyer_organic_uninstalls_report
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_raw_data;
CREATE TABLE elka2024_dev.appsflyer_raw_data
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `campaign` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `channel` String DEFAULT '' CODEC(LZ4HC()),
    `campaign_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `cost_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_retargeting` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `media_source` String DEFAULT '' CODEC(LZ4HC()),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `bundle_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `country_code` String DEFAULT '' CODEC(LZ4HC()),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_category` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_model` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` String DEFAULT '' CODEC(LZ4HC()),
    `event_revenue` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `language` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `operator` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `os_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `platform` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `sdk_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `wifi` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `store_reinstall` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_time` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_revenue_usd` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `event_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_receipt_validated` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `region` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `retargeting_conversation_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `monatization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `impressions` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (campaign, channel, country_code, event_name, event_time, media_source)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_skan_aggregated_performance_report;
CREATE TABLE elka2024_dev.appsflyer_skan_aggregated_performance_report
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `Date` Date,
    `Media Source (pid)` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Campaign (c)` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Campaign ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Site ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Adset` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Adset ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Ad ID` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Country` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `AF Attribution Flag` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Impressions` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `Clicks` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `CTR` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Click Through Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `View Through Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Null Conversion Value Rate` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Conversion Rate` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Converted Users` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Converted Users/Installs` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Total Revenue` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `Total Cost` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `ROI` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ARPU` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `Average eCPI` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `af_purchase (Unique user)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `af_purchase (Event counter)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `af_skad_revenue (Unique users)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `af_skad_revenue (Event counter)` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `app_id` LowCardinality(String)
)
ENGINE = MergeTree
ORDER BY (app_id, Date)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.aws_cost_etl;
CREATE VIEW elka2024_dev.aws_cost_etl AS SELECT * FROM elka2024_etl_dev.aws_cost_etl FINAL;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_cohort_user_acquisition;
CREATE TABLE elka2024_dev.appsflyer_cohort_user_acquisition
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `conversion_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_primary_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `days_post_attribution` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `conversion_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_date` Date CODEC(Delta(2), LZ4),
    `event_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `media_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `channel` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `geo` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `agency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `selected_currency` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `unique_users` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `event_count` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `revenue_usd` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `revenue_selected_currency` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `app_id` String CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0)),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY event_date
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.appsflyer_marketing_campaign;
CREATE TABLE elka2024_dev.appsflyer_marketing_campaign
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `skad_conversion_value` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `skad_ad_network_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_campaign_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_adset_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_adset_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `ad_network_ad_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_version` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_redownload` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_source_app_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `skad_fidelity_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `geo` Nullable(String) DEFAULT NULL CODEC(LZ4HC()),
    `arrival_date` Date,
    `count` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY arrival_date
SETTINGS index_granularity = 8192;