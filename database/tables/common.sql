
DROP TABLE IF EXISTS elka2024_etl_dev.extract_state;
CREATE TABLE elka2024_etl_dev.extract_state
(
    `connection_id` LowCardinality(String) CODEC (LZ4HC(0)),
    `insert_datetime` DateTime DEFAULT now() CODEC (DoubleDelta, ZSTD(1)),
    `source_name` LowCardinality(String) CODEC (LZ4HC(0)),
    `key_name` LowCardinality(String) CODEC (LZ4HC(0)),
    `last_value_int` Nullable(UInt64) CODEC (T64, LZ4),
    `last_value_str` Nullable(String) CODEC (LZ4HC(0)),
    `last_value_date` Nullable(DateTime) DEFAULT null CODEC (DoubleDelta, ZSTD(1))
)
ENGINE = ReplacingMergeTree(insert_datetime)
ORDER BY (connection_id, source_name, key_name)
SETTINGS index_granularity = 8192;