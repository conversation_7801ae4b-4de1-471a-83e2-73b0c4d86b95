
-- Вспомогательные таблицы

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.agg_os_sessions;
CREATE TABLE elka2024_dev.agg_os_sessions
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `dt` Date CODEC(DoubleDelta, LZ4),
    `os` LowCardinality(String) CODEC(LZ4HC(0)),
    `sessions` UInt32 CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYear(dt))
ORDER BY (dt, os)
SETTINGS index_granularity = 8192;

