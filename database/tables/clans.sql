
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_clans_extracted;
CREATE TABLE elka2024_etl_dev.mysql_clans_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `update_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `name` String CODEC(LZ4HC(0)),
    `logo` Nullable(String) CODEC(LZ4HC(0)),
    `description` String CODEC(LZ4HC(0)),
    `user_level` UInt32 CODEC(T64, LZ4),
    `member_count` UInt32 CODEC(T64, LZ4),
    `status` UInt8 CODEC(T64, LZ4),
    `old_clan` UInt8 CODEC(T64, LZ4),
    `rank` UInt32 CODEC(T64, LZ4),
    `install_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `remove_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `user_id_head` UInt32 CODEC(T64, LZ4),
    `moderator_count` UInt32 CODEC(T64, LZ4),
    `language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY (platform_type, clan_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_clans;
CREATE TABLE elka2024_etl_dev.mysql_clans
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_update_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `name` String CODEC(LZ4HC(0)),
    `logo` Nullable(String) CODEC(LZ4HC(0)),
    `description` String CODEC(LZ4HC(0)),
    `user_level` UInt32 CODEC(T64, LZ4),
    `member_count` UInt32 CODEC(T64, LZ4),
    `status` UInt8 CODEC(T64, LZ4),
    `old_clan` UInt8 CODEC(T64, LZ4),
    `rank` UInt32 CODEC(T64, LZ4),
    `install_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `remove_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `user_id_head` UInt32 CODEC(T64, LZ4),
    `moderator_count` UInt32 CODEC(T64, LZ4),
    `language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0))
)
ENGINE = ReplacingMergeTree(_update_time)
ORDER BY (platform_type, clan_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.mongo_clans_extracted;
CREATE TABLE elka2024_etl_dev.mongo_clans_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `update_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `castle_level` Nullable(UInt32) CODEC(T64, LZ4),
    `league` Nullable(UInt8) CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, clan_id)
SETTINGS index_granularity = 8192;

-- Не через MATERIALIZED VIEW для того, чтобы была возможность периодически делать TRUNCATE и
-- заполнять таблицу без дублей с помощью FINAL
DROP TABLE IF EXISTS elka2024_etl_dev.clans;
CREATE TABLE elka2024_etl_dev.clans
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_update_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_update_time_mysql` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_update_time_mongo` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `name` String CODEC(LZ4HC(0)),
    `logo` Nullable(String) CODEC(LZ4HC(0)),
    `description` String CODEC(LZ4HC(0)),
    `user_level` UInt32 CODEC(T64, LZ4),
    `member_count` UInt32 CODEC(T64, LZ4),
    `status` UInt8 CODEC(T64, LZ4),
    `old_clan` UInt8 CODEC(T64, LZ4),
    `rank` UInt32 CODEC(T64, LZ4),
    `install_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `remove_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `user_id_head` UInt32 CODEC(T64, LZ4),
    `moderator_count` UInt32 CODEC(T64, LZ4),
    `language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `castle_level` Nullable(UInt32) CODEC(T64, LZ4),
    `league` Nullable(UInt8) CODEC(T64, LZ4)
)
ENGINE = ReplacingMergeTree(_update_time)
ORDER BY (platform_type, clan_id)
SETTINGS index_granularity = 8192;

-- Основные таблицы

DROP TABLE IF EXISTS elka2024_dev.clans_dynamics;
CREATE TABLE elka2024_dev.clans_dynamics
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_update_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_update_time_mysql` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_update_time_mongo` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `update_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `name` String CODEC(LZ4HC(0)),
    `logo` Nullable(String) CODEC(LZ4HC(0)),
    `description` String CODEC(LZ4HC(0)),
    `user_level` UInt32 CODEC(T64, LZ4),
    `member_count` UInt32 CODEC(T64, LZ4),
    `status` UInt8 CODEC(T64, LZ4),
    `old_clan` UInt8 CODEC(T64, LZ4),
    `rank` UInt32 CODEC(T64, LZ4),
    `install_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `remove_time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `user_id_head` UInt32 CODEC(T64, LZ4),
    `moderator_count` UInt32 CODEC(T64, LZ4),
    `language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `castle_level` Nullable(UInt32) CODEC(T64, LZ4),
    `league` Nullable(UInt8) CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, clan_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.clans;
CREATE VIEW elka2024_dev.clans AS SELECT * FROM elka2024_etl_dev.clans FINAL;