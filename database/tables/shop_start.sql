-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_shop_start_extracted;
CREATE TABLE elka2024_etl_dev.mysql_shop_start_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_id` UInt64 CODEC(T64, LZ4),
    `user_id` UInt64 Codec(T64, LZ4),
    `start_time` DateTime CODEC(DoubleDelta, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` UInt8 Codec(T64, LZ4),
    `shop_type` Nullable(UInt8) Codec(T64, LZ4),
    `segment_id` Nullable(UInt32) Codec(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, start_time)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.shop_start;
CREATE TABLE elka2024_dev.shop_start
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_id` UInt64 CODEC(T64, LZ4),
    `user_id` UInt64 Codec(T64, LZ4),
    `start_time` DateTime CODEC(DoubleDelta, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` UInt8 Codec(T64, LZ4),
    `shop_type` Nullable(UInt8) Codec(T64, LZ4),
    `segment_id` Nullable(UInt32) Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(start_time), platform_type)
ORDER BY (platform_type, start_time)
SETTINGS index_granularity = 8192;