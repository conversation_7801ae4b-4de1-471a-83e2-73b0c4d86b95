-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.glades_popularity_extracted;
CREATE TABLE elka2024_etl_dev.glades_popularity_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` String Codec(LZ4HC()),
    `user_id` UInt64 Codec(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `screen` UInt8 Codec(T64, LZ4),
    `period_id` Nullable(UInt8) Codec(T64, LZ4),
    `stages_sum` Nullable(UInt16) Codec(T64, LZ4),
    `toys_sum` Nullable(UInt16) Codec(T64, LZ4),
    `votes` Nullable(UInt16) Codec(T64, LZ4),
    `shows` Nullable(UInt16) Codec(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, screen, user_id)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.glades_popularity;
CREATE TABLE elka2024_dev.glades_popularity
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` String Codec(LZ4HC()),
    `user_id` UInt64 Codec(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `screen` UInt8 Codec(T64, LZ4),
    `period_id` Nullable(UInt8) Codec(T64, LZ4),
    `stages_sum` Nullable(UInt16) Codec(T64, LZ4),
    `toys_sum` Nullable(UInt16) Codec(T64, LZ4),
    `votes` Nullable(UInt16) Codec(T64, LZ4),
    `shows` Nullable(UInt16) Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type)
ORDER BY (platform_type, screen, user_id)
SETTINGS index_granularity = 8192;