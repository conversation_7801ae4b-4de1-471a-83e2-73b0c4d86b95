
-- В этом файле всякие таблицы из прошлых елок, чтобы не поднимать для этого локально еще +1 Clickhouse
CREATE DATABASE IF NOT EXISTS elka2023_dev;

DROP TABLE IF EXISTS elka2023_dev.users_info;
CREATE TABLE elka2023_dev.users_info
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `first_platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `user_id_platform` String CODEC(LZ4HC(0)),
    `start_screen` Nullable(UInt8) CODEC(T64, LZ4),
    `install_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `login_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `cash` Nullable(UInt32) CODEC(T64, LZ4),
    `energy` Nullable(UInt32) CODEC(T64, LZ4),
    `install_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `current_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `birthdate` Nullable(String) CODEC(LZ4HC(0)),
    `sex` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `email` Nullable(String) CODEC(LZ4HC(0)),
    `locale` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `friends_count` Nullable(UInt32) CODEC(T64, LZ4),
    `referrer` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_data` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_user_id` Nullable(UInt64) CODEC(T64, LZ4),
    `referrer_user_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `clan_role` Nullable(UInt8) CODEC(T64, LZ4),
    `geo_language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `last_name` Nullable(String) CODEC(LZ4HC(0)),
    `first_name` Nullable(String) CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY (platform_type, user_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2023_dev.payments;
CREATE TABLE elka2023_dev.payments
(
    `id` UInt64 CODEC(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` UInt8 CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` Nullable(String) CODEC(LZ4HC(0)),
    `session_index` Nullable(UInt32) CODEC(T64, LZ4),
    `session_number` Nullable(UInt32) CODEC(T64, LZ4),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, LZ4),
    `level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `cash` Nullable(UInt32) CODEC(T64, LZ4),
    `energy` Nullable(UInt32) CODEC(T64, LZ4),
    `status` Nullable(UInt8) CODEC(T64, LZ4),
    `transaction_id` Nullable(String) CODEC(LZ4HC(0)),
    `price` Nullable(Decimal(8, 2)) CODEC(T64, LZ4),
    `item_type` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `item_id` Nullable(UInt32) CODEC(T64, LZ4),
    `item_extra_id` Nullable(UInt8) CODEC(T64, LZ4),
    `award` Nullable(String) CODEC(LZ4HC(0)),
    `gift_id` Nullable(UInt16) CODEC(T64, LZ4),
    `bonus_id` Nullable(UInt16) CODEC(T64, LZ4),
    `shard_name` LowCardinality(String) CODEC(LZ4HC(0)),
    `local_price` Nullable(Decimal(8, 2)) CODEC(T64, LZ4),
    `local_currency` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1))
)
ENGINE = MergeTree
ORDER BY (platform_type, date)
SETTINGS index_granularity = 8192;

CREATE DATABASE IF NOT EXISTS datamarts_dev;
CREATE DATABASE IF NOT EXISTS datamarts_etl_dev;

DROP TABLE IF EXISTS datamarts_dev.cbr_currency;
CREATE TABLE datamarts_dev.cbr_currency
(
    `num_code` LowCardinality(String) CODEC(LZ4HC(0)),
    `char_code` LowCardinality(String) CODEC(LZ4HC(0)),
    `nominal` UInt32 CODEC(T64, LZ4),
    `name` LowCardinality(String) CODEC(LZ4HC(0)),
    `value` Decimal(10, 4) Codec(T64, LZ4),
    `date` Date CODEC(DoubleDelta, ZSTD(1)),
    `insert_datetime` DateTime DEFAULT now()
)
ENGINE = MergeTree
ORDER BY (date, num_code, char_code)
SETTINGS index_granularity = 8192;