-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.glades_pairs;
CREATE TABLE elka2024_dev.glades_pairs
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` String Codec(LZ4HC()),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `screen` UInt8 Codec(T64, LZ4),
    `user_id_1` Nullable(UInt64) Codec(T64, LZ4),
    `user_id_2` Nullable(UInt64) Codec(T64, LZ4),
    `level_id_1` UInt32 Codec(T64, LZ4),
    `level_id_2` UInt32 Codec(T64, LZ4),
    `progress_score_1` Nullable(UInt32) Codec(T64, LZ4),
    `progress_score_2` Nullable(UInt32) Codec(T64, LZ4),
    `stages_sum_1` Nullable(UInt16) Codec(T64, LZ4),
    `stages_sum_2` Nullable(UInt16) Codec(T64, LZ4),
    `toys_sum_1` Nullable(UInt16) Codec(T64, LZ4),
    `toys_sum_2` Nullable(UInt16) Codec(T64, LZ4),
    `votes_1` Nullable(UInt16) Codec(T64, LZ4),
    `votes_2` Nullable(UInt16) Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type)
ORDER BY (platform_type, screen, level_id_1, level_id_2)
SETTINGS index_granularity = 8192;

