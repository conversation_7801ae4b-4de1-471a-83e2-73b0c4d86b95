
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.clans_matchmaking_extracted;
CREATE TABLE elka2024_etl_dev.clans_matchmaking_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `name` String CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `member_count` UInt32 CODEC(T64, LZ4),
    `clans` Nullable(String) CODEC(LZ4HC(0)),
    `event_id` UInt32 CODEC(T64, LZ4),
    `season_id` UInt32 CODEC(T64, LZ4),
    `castle_level` Nullable(UInt32) CODEC(T64, LZ4),
    `league` Nullable(UInt8) CODEC(T64, LZ4),
    `mmr` Nullable(UInt32) CODEC(T64, LZ4)
)
ENGINE = ReplacingMergeTree
ORDER BY (platform_type, clan_id, season_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.clans_matchmaking;
CREATE TABLE elka2024_etl_dev.clans_matchmaking
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `name` String CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `member_count` UInt32 CODEC(T64, LZ4),
    `clans` Nullable(String) CODEC(LZ4HC(0)),
    `event_id` UInt32 CODEC(T64, LZ4),
    `season_id` UInt32 CODEC(T64, LZ4),
    `castle_level` Nullable(UInt32) CODEC(T64, LZ4),
    `league` Nullable(UInt8) CODEC(T64, LZ4),
    `mmr` Nullable(UInt32) CODEC(T64, LZ4)
)
ENGINE = ReplacingMergeTree
ORDER BY (platform_type, clan_id, season_id)
SETTINGS index_granularity = 8192;

-- Основная таблица

DROP TABLE IF EXISTS elka2024_dev.clans_matchmaking;
CREATE VIEW elka2024_dev.clans_matchmaking AS SELECT * FROM elka2024_etl_dev.clans_matchmaking FINAL;