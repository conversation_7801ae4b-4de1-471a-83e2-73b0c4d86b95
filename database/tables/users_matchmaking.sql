
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.users_matchmaking_extracted;
CREATE TABLE elka2024_etl_dev.users_matchmaking_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt32 CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `progress` UInt32 CODEC(T64, LZ4),
    `users` Nullable(String) CODEC(LZ4HC(0)),
    `event_id` UInt32 CODEC(T64, LZ4),
    `season_id` UInt32 CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, user_id, event_id, season_id)
SETTINGS index_granularity = 8192;

-- Основная таблица

DROP TABLE IF EXISTS elka2024_dev.users_matchmaking;
CREATE TABLE elka2024_dev.users_matchmaking
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt32 CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `progress` UInt32 CODEC(T64, LZ4),
    `users` Nullable(String) CODEC(LZ4HC(0)),
    `event_id` UInt32 CODEC(T64, LZ4),
    `season_id` UInt32 CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYYYYMM(date))
ORDER BY (platform_type, user_id, event_id, season_id)
SETTINGS index_granularity = 8192;