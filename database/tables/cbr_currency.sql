
-- Вспомогательные таблицы

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.cbr_currency;
CREATE TABLE elka2024_dev.cbr_currency
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `num_code` LowCardinality(String) CODEC(LZ4HC(0)),
    `char_code` LowCardinality(String) CODEC(LZ4HC(0)),
    `nominal` UInt32 CODEC(T64, LZ4),
    `name` LowCardinality(String) CODEC(LZ4HC(0)),
    `value` Decimal(10, 4) Codec(T64, LZ4),
    `date` Date CODEC(DoubleDelta, ZSTD(1))
)
ENGINE = MergeTree
ORDER BY (date, num_code, char_code)
SETTINGS index_granularity = 8192;