
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.nginx_logs_extracted;
CREATE TABLE elka2024_etl_dev.nginx_logs_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_file` Nullable(String) CODEC(LZ4HC(0)),
    `stream` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    -- _p
    `fluentbit_status` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    -- “time”:“2024-06-26T17:36:41+00:00"
    `time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `remote_addr` Nullable(String) CODEC(LZ4HC(0)),
    `request_id` Nullable(String) CODEC(LZ4HC(0)),
    `remote_user` Nullable(String) CODEC(LZ4HC(0)),
    `bytes_sent` UInt32 CODEC(T64, LZ4),
    `body_bytes_sent` UInt32 CODEC(T64, LZ4),
    -- Потому что с точностью до мс
    `request_time` Decimal(10, 3) CODEC(T64, LZ4),
    `upstream_time` Nullable(Decimal(10, 3)) DEFAULT NULL CODEC(T64, LZ4),
    `status` UInt16 CODEC(T64, LZ4),
    `vhost` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `request_proto` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `path` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `request_query` Nullable(String) CODEC(LZ4HC(0)),
    `request_length` UInt32 CODEC(T64, LZ4),
    `method` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `http_referrer` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `http_user_agent` Nullable(String) CODEC(LZ4HC(0)),
    `ingress_name` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `proxy_upstream_name` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `upstream_status` UInt16 CODEC(T64, LZ4),
    `host` LowCardinality(Nullable(String)) CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY (time)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.nginx_logs;
CREATE TABLE elka2024_dev.nginx_logs
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_file` Nullable(String) CODEC(LZ4HC(0)),
    `stream` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    -- _p
    `fluentbit_status` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    -- “time”:“2024-06-26T17:36:41+00:00"
    `time` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `remote_addr` Nullable(String) CODEC(LZ4HC(0)),
    `request_id` Nullable(String) CODEC(LZ4HC(0)),
    `remote_user` Nullable(String) CODEC(LZ4HC(0)),
    `bytes_sent` UInt32 CODEC(T64, LZ4),
    `body_bytes_sent` UInt32 CODEC(T64, LZ4),
    -- Потому что с точностью до мс
    `request_time` Decimal(10, 3) CODEC(T64, LZ4),
    `upstream_time` Nullable(Decimal(10, 3)) DEFAULT NULL CODEC(T64, LZ4),
    `status` UInt16 CODEC(T64, LZ4),
    `vhost` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `request_proto` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `path` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `request_query` Nullable(String) CODEC(LZ4HC(0)),
    `request_length` UInt32 CODEC(T64, LZ4),
    `method` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `http_referrer` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `http_user_agent` Nullable(String) CODEC(LZ4HC(0)),
    `ingress_name` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `proxy_upstream_name` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `upstream_status` UInt16 CODEC(T64, LZ4),
    `host` LowCardinality(Nullable(String)) CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY (time)
PARTITION BY toYYYYMM(time)
SETTINGS index_granularity = 8192;