
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.events_extracted;
CREATE TABLE elka2024_etl_dev.events_extracted
(
    `_server_date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_filename` String CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_validation_result` UInt8 CODEC(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `session_index` UInt32 CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `screen` UInt8 CODEC(T64, LZ4),
    `level` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `factory_level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` UInt32 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4),
    `cash` UInt32 CODEC(T64, LZ4),
    `energy` UInt32 CODEC(T64, LZ4),
    `app_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `state` Nullable(UInt8) CODEC(T64, LZ4),
    `id` Nullable(UInt32) CODEC(T64, LZ4),
    `duration` Nullable(UInt32) CODEC(T64, LZ4),
    `duration_cont` Nullable(UInt32) CODEC(T64, LZ4),
    `duration_abs` Nullable(UInt32) CODEC(T64, LZ4),
    `content_id` Nullable(UInt8) CODEC(T64, LZ4),
    `content_size` Nullable(UInt32) CODEC(T64, LZ4),
    `energy_level` Nullable(UInt16) CODEC(T64, LZ4),
    `cash_level` Nullable(UInt16) CODEC(T64, LZ4),
    `coins_level` Nullable(UInt16) CODEC(T64, LZ4),
    `money_level` Nullable(UInt16) CODEC(T64, LZ4),
    `type` Nullable(UInt16) CODEC(T64, LZ4),
    `last_action` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `dummy_flag` Nullable(UInt8) CODEC(T64, LZ4),
    `source` Nullable(UInt8) CODEC(T64, LZ4),
    `value` Nullable(UInt32) CODEC(T64, LZ4),
    `score` Nullable(UInt16) CODEC(T64, LZ4),
    `level_value` Nullable(UInt16) CODEC(T64, LZ4),
    `static_url` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_vendor` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_model` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `window_name` Nullable(UInt8) CODEC(T64, LZ4),
    `memory_usage` Nullable(UInt32) CODEC(T64, LZ4),
    `common_tick_count` Nullable(UInt32) CODEC(T64, LZ4),
    `common_tick_time` Nullable(UInt32) CODEC(T64, LZ4),
    `common_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `common_current_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `fullscreen_tick_count` Nullable(UInt32) CODEC(T64, LZ4),
    `fullscreen_tick_time` Nullable(UInt32) CODEC(T64, LZ4),
    `fullscreen_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `fullscreen_current_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `log_size` Nullable(UInt32) CODEC(T64, LZ4),
    `slot_id` Nullable(UInt8) CODEC(T64, LZ4),
    `str_id` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `json_str` Nullable(String) CODEC(LZ4HC(0)),
    `award_type` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `amount` Nullable(UInt32) CODEC(T64, LZ4),
    `language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `message` Nullable(String) CODEC(LZ4HC(0)),
    `min_value` Nullable(UInt32) CODEC(T64, LZ4),
    `max_value` Nullable(UInt32) CODEC(T64, LZ4),
    `med_value` Nullable(UInt32) CODEC(T64, LZ4),
    `number_index` Nullable(UInt32) CODEC(T64, LZ4),
    `minigame_number` Nullable(UInt32) CODEC(T64, LZ4),
    `mode` Nullable(UInt8) CODEC(T64, LZ4),
    `step_id` Nullable(String) CODEC(LZ4HC(0)),
    `id_2` Nullable(UInt32) CODEC(T64, LZ4),
    `period` Nullable(UInt32) CODEC(T64, LZ4),
    `mean_value` Nullable(UInt32) CODEC(T64, LZ4),
    `requests` Nullable(UInt32) CODEC(T64, LZ4),
    `error_requests` Nullable(UInt32) CODEC(T64, LZ4),
    `bubble` Nullable(UInt8) CODEC(T64, LZ4),
    `revenue` Nullable(Float32) CODEC(Gorilla, LZ4),
    `distance` Nullable(UInt32) CODEC(T64, LZ4),
    `layout` Nullable(String) CODEC(LZ4HC(0)),
    `tiles_left` Nullable(UInt16) CODEC(T64, LZ4),
    `timer` Nullable(UInt16) CODEC(T64, LZ4),
    `tiles` Nullable(UInt16) CODEC(T64, LZ4),
    `tiles_collection` Nullable(UInt16) CODEC(T64, LZ4),
    `tiles_selected` Nullable(String) CODEC(LZ4HC(0)),
    `tiles_removed` Nullable(UInt32) CODEC(T64, LZ4),
    `score_time` Nullable(UInt32) CODEC(T64, LZ4),
    `lives` Nullable(UInt32) CODEC(T64, LZ4),
    `boosters_used` Nullable(String) CODEC(LZ4HC(0)),
    `auto_boosters_used` Nullable(UInt32) CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, eventtype, date)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.events_errors;
CREATE TABLE elka2024_etl_dev.events_errors
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0)),
    `insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `error_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `error_details` String CODEC(LZ4HC(0)),
    `json` String CODEC(LZ4HC(0))
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYYYYMM(date))
ORDER BY (platform_type, date, eventtype)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.telemetry_extracted;
CREATE TABLE elka2024_etl_dev.telemetry_extracted
(
    `_server_date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_filename` String CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_validation_result` UInt8 CODEC(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `session_index` UInt32 CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `screen` UInt8 CODEC(T64, LZ4),
    `level` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `factory_level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` UInt32 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4),
    `cash` UInt32 CODEC(T64, LZ4),
    `energy` UInt32 CODEC(T64, LZ4),
    `app_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    --
    `session_duration` Nullable(UInt32) CODEC(T64, LZ4),
    `memory_tcache_avg` Nullable(UInt32) CODEC(T64, LZ4),
    `memory_tcache_max` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_server_count` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_server_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_resource_count` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_resource_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `local_spinner_resource_count` Nullable(UInt32) CODEC(T64, LZ4),
    `local_spinner_resource_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `window_spinner_resource_count` Nullable(UInt32) CODEC(T64, LZ4),
    `window_spinner_resource_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_count_total` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_count_error` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_time_total` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_time_min` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_time_max` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_count_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_count_error` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_time_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_time_min` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_time_max` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_response_size` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_count_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_count_error` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_time_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_time_min` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_time_max` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_response_size` Nullable(UInt32) CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, eventtype, date)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.events;
CREATE TABLE elka2024_dev.events
(
    `_server_date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_filename` String CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_validation_result` UInt8 CODEC(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `session_index` UInt32 CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `screen` UInt8 CODEC(T64, LZ4),
    `level` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `factory_level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` UInt32 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4),
    `cash` UInt32 CODEC(T64, LZ4),
    `energy` UInt32 CODEC(T64, LZ4),
    `app_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `state` Nullable(UInt8) CODEC(T64, LZ4),
    `id` Nullable(UInt32) CODEC(T64, LZ4),
    `duration` Nullable(UInt32) CODEC(T64, LZ4),
    `duration_cont` Nullable(UInt32) CODEC(T64, LZ4),
    `duration_abs` Nullable(UInt32) CODEC(T64, LZ4),
    `content_id` Nullable(UInt8) CODEC(T64, LZ4),
    `content_size` Nullable(UInt32) CODEC(T64, LZ4),
    `energy_level` Nullable(UInt16) CODEC(T64, LZ4),
    `cash_level` Nullable(UInt16) CODEC(T64, LZ4),
    `coins_level` Nullable(UInt16) CODEC(T64, LZ4),
    `money_level` Nullable(UInt16) CODEC(T64, LZ4),
    `type` Nullable(UInt16) CODEC(T64, LZ4),
    `last_action` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `dummy_flag` Nullable(UInt8) CODEC(T64, LZ4),
    `source` Nullable(UInt8) CODEC(T64, LZ4),
    `value` Nullable(UInt32) CODEC(T64, LZ4),
    `score` Nullable(UInt16) CODEC(T64, LZ4),
    `level_value` Nullable(UInt16) CODEC(T64, LZ4),
    `static_url` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_vendor` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `device_model` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `window_name` Nullable(UInt8) CODEC(T64, LZ4),
    `memory_usage` Nullable(UInt32) CODEC(T64, LZ4),
    `common_tick_count` Nullable(UInt32) CODEC(T64, LZ4),
    `common_tick_time` Nullable(UInt32) CODEC(T64, LZ4),
    `common_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `common_current_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `fullscreen_tick_count` Nullable(UInt32) CODEC(T64, LZ4),
    `fullscreen_tick_time` Nullable(UInt32) CODEC(T64, LZ4),
    `fullscreen_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `fullscreen_current_fps` Nullable(Float32) CODEC(Gorilla, LZ4),
    `log_size` Nullable(UInt32) CODEC(T64, LZ4),
    `slot_id` Nullable(UInt8) CODEC(T64, LZ4),
    `str_id` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `json_str` Nullable(String) CODEC(LZ4HC(0)),
    `award_type` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `amount` Nullable(UInt32) CODEC(T64, LZ4),
    `language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `message` Nullable(String) CODEC(LZ4HC(0)),
    `min_value` Nullable(UInt32) CODEC(T64, LZ4),
    `max_value` Nullable(UInt32) CODEC(T64, LZ4),
    `med_value` Nullable(UInt32) CODEC(T64, LZ4),
    `number_index` Nullable(UInt32) CODEC(T64, LZ4),
    `minigame_number` Nullable(UInt32) CODEC(T64, LZ4),
    `mode` Nullable(UInt8) CODEC(T64, LZ4),
    `step_id` Nullable(String) CODEC(LZ4HC(0)),
    `id_2` Nullable(UInt32) CODEC(T64, LZ4),
    `period` Nullable(UInt32) CODEC(T64, LZ4),
    `mean_value` Nullable(UInt32) CODEC(T64, LZ4),
    `requests` Nullable(UInt32) CODEC(T64, LZ4),
    `error_requests` Nullable(UInt32) CODEC(T64, LZ4),
    `bubble` Nullable(UInt8) CODEC(T64, LZ4),
    `revenue` Nullable(Float32) CODEC(Gorilla, LZ4),
    `distance` Nullable(UInt32) CODEC(T64, LZ4),
    `layout` Nullable(String) CODEC(LZ4HC(0)),
    `tiles_left` Nullable(UInt16) CODEC(T64, LZ4),
    `timer` Nullable(UInt16) CODEC(T64, LZ4),
    `tiles` Nullable(UInt16) CODEC(T64, LZ4),
    `tiles_collection` Nullable(UInt16) CODEC(T64, LZ4),
    `tiles_selected` Nullable(String) CODEC(LZ4HC(0)),
    `tiles_removed` Nullable(UInt32) CODEC(T64, LZ4),
    `score_time` Nullable(UInt32) CODEC(T64, LZ4),
    `lives` Nullable(UInt32) CODEC(T64, LZ4),
    `boosters_used` Nullable(String) CODEC(LZ4HC(0)),
    `auto_boosters_used` Nullable(UInt32) CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYYYYMM(date))
ORDER BY (platform_type, eventtype, date)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.telemetry;
CREATE TABLE elka2024_dev.telemetry
(
    `_server_date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_filename` String CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_validation_result` UInt8 CODEC(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `session_index` UInt32 CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` UInt32 CODEC(T64, LZ4),
    `screen` UInt8 CODEC(T64, LZ4),
    `level` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `factory_level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` UInt32 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4),
    `cash` UInt32 CODEC(T64, LZ4),
    `energy` UInt32 CODEC(T64, LZ4),
    `app_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    --
    `session_duration` Nullable(UInt32) CODEC(T64, LZ4),
    `memory_tcache_avg` Nullable(UInt32) CODEC(T64, LZ4),
    `memory_tcache_max` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_server_count` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_server_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_resource_count` Nullable(UInt32) CODEC(T64, LZ4),
    `global_spinner_resource_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `local_spinner_resource_count` Nullable(UInt32) CODEC(T64, LZ4),
    `local_spinner_resource_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `window_spinner_resource_count` Nullable(UInt32) CODEC(T64, LZ4),
    `window_spinner_resource_duration_total` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_count_total` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_count_error` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_time_total` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_time_min` Nullable(UInt32) CODEC(T64, LZ4),
    `server_request_time_max` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_count_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_count_error` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_time_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_time_min` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_time_max` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_response_size` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_count_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_count_error` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_time_total` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_time_min` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_time_max` Nullable(UInt32) CODEC(T64, LZ4),
    `resource_request_internet_response_size` Nullable(UInt32) CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, eventtype, date)
SETTINGS index_granularity = 8192;

-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.events_last_by_session;
CREATE MATERIALIZED VIEW elka2024_etl_dev.events_last_by_session
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` String CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1))
)
ENGINE = ReplacingMergeTree(date)
PARTITION BY (platform_type, toYYYYMM(date))
ORDER BY (platform_type, user_id, session_id)
SETTINGS index_granularity = 8192 AS
SELECT
    platform_type,
    user_id,
    session_id,
    date
FROM elka2024_dev.events;

DROP TABLE IF EXISTS elka2024_etl_dev.events_duplicated;
CREATE TABLE elka2024_etl_dev.events_duplicated AS elka2024_dev.events;