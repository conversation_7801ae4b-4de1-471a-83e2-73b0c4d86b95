-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.users_info;
CREATE TABLE elka2024_etl_dev.users_info
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_update_time` UInt64 CODEC(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `user_id_platform` String CODEC(LZ4HC(0)),
    `old_user_id` Nullable(UInt64) CODEC(T64, LZ4),
    `old_user_platform` Nullable(String) CODEC(LZ4HC(0)),
    `payer` UInt8 CODEC(T64, LZ4),
    `start_screen` UInt8 CODEC(T64, LZ4),
    `install_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `login_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `level` UInt32 CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4),
    `factory_level` Nullable(UInt32) CODEC(T64, LZ4),
    `merge_level` Nullable(UInt32) CODEC(T64, LZ4),
    `cash` Nullable(UInt32) CODEC(T64, LZ4),
    `energy` Nullable(UInt32) CODEC(T64, LZ4),
    `install_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `current_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `birthdate` Nullable(String) CODEC(LZ4HC(0)),
    `sex` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `email` Nullable(String) CODEC(LZ4HC(0)),
    `locale` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `friends_count` Nullable(UInt32) CODEC(T64, LZ4),
    `referrer` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_data` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_user_id` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_user_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `clan_role` Nullable(UInt8) CODEC(T64, LZ4),
    `geo_language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `last_name` Nullable(String) CODEC(LZ4HC(0)),
    `first_name` Nullable(String) CODEC(LZ4HC(0)),
    `first_platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `gdpr_removed` UInt8 CODEC(T64, LZ4),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `guest` UInt8 CODEC(T64, LZ4)
)
ENGINE = ReplacingMergeTree(_insert_datetime)
PARTITION BY platform_type
ORDER BY (platform_type, user_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_perf_users;
CREATE TABLE elka2024_etl_dev.mysql_perf_users
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `user_id_platform` String CODEC(LZ4HC(0)),
    `install_time` Nullable(UInt64) CODEC(T64, LZ4),
    `referrer` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_data` Nullable(String) CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY (_platform, user_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_perf_users_extracted;
CREATE TABLE elka2024_etl_dev.mysql_perf_users_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `user_id_platform` String CODEC(LZ4HC(0)),
    `install_time` Nullable(UInt64) CODEC(T64, LZ4),
    `referrer` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_data` Nullable(String) CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY (_platform, user_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.mongo_game_extracted;
CREATE TABLE elka2024_etl_dev.mongo_game_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `_update_time` UInt64 CODEC(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `user_id_platform` String CODEC(LZ4HC(0)),
    `old_user_id` Nullable(UInt64) CODEC(T64, LZ4),
    `old_user_platform` Nullable(String) CODEC(LZ4HC(0)),
    `payer` UInt8 CODEC(T64, LZ4),
    `start_screen` UInt8 CODEC(T64, LZ4),
    `install_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `login_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `level` UInt32 CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4),
    `factory_level` Nullable(UInt32) CODEC(T64, LZ4),
    `merge_level` Nullable(UInt32) CODEC(T64, LZ4),
    `cash` Nullable(UInt32) CODEC(T64, LZ4),
    `energy` Nullable(UInt32) CODEC(T64, LZ4),
    `install_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `current_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `birthdate` Nullable(String) CODEC(LZ4HC(0)),
    `sex` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `email` Nullable(String) CODEC(LZ4HC(0)),
    `locale` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `friends_count` Nullable(UInt32) CODEC(T64, LZ4),
    `referrer` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_data` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_user_id` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_user_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `clan_role` Nullable(UInt8) CODEC(T64, LZ4),
    `geo_language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `last_name` Nullable(String) CODEC(LZ4HC(0)),
    `first_name` Nullable(String) CODEC(LZ4HC(0)),
    `first_platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `gdpr_removed` UInt8 CODEC(T64, LZ4),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection` UInt16 CODEC(T64, LZ4),
    `standard_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `tournament_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `passing_mode_collection_number` UInt16 CODEC(T64, LZ4),
    `guest` UInt8 CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (_platform, user_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.users_info2023;
CREATE TABLE elka2024_dev.users_info2023
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `first_platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `user_id_platform` String CODEC(LZ4HC(0)),
    `start_screen` Nullable(UInt8) CODEC(T64, LZ4),
    `install_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `login_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `cash` Nullable(UInt32) CODEC(T64, LZ4),
    `energy` Nullable(UInt32) CODEC(T64, LZ4),
    `install_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `current_build` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `birthdate` Nullable(String) CODEC(LZ4HC(0)),
    `sex` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `email` Nullable(String) CODEC(LZ4HC(0)),
    `locale` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `friends_count` Nullable(UInt32) CODEC(T64, LZ4),
    `referrer` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_data` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_user_id` Nullable(String) CODEC(LZ4HC(0)),
    `referrer_user_time` Nullable(DateTime) CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `clan_role` Nullable(UInt8) CODEC(T64, LZ4),
    `geo_language` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_country` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geo_city` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `last_name` Nullable(String) CODEC(LZ4HC(0)),
    `first_name` Nullable(String) CODEC(LZ4HC(0)),
    `insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1))
)
ENGINE = MergeTree()
ORDER BY (platform_type, user_id)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.users_info;
CREATE VIEW elka2024_dev.users_info
AS SELECT * FROM elka2024_etl_dev.users_info FINAL;