-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_promo_start_extracted;
CREATE TABLE elka2024_etl_dev.mysql_promo_start_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_id` UInt64 CODEC(T64, LZ4),
    `user_id` UInt64 Codec(T64, LZ4),
    `start_time` DateTime CODEC(DoubleDelta, LZ4),
    `expire_time` DateTime CODEC(DoubleDelta, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` UInt32 Codec(T64, LZ4),
    `promo_id` UInt64 CODEC(T64, LZ4),
    `offer_id` UInt32 CODEC(T64, LZ4),
    `award` Nullable(String) Codec(LZ4HC()),
    `price` Nullable(String) Codec(LZ4HC()),
    `avg_bill_10_last` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `convert_10_last` Nullable(UInt64) CODEC(T64, LZ4),
    `last_purchase_date` Nullable(UInt64) CODEC(T64, LZ4),
    `avg_bill_last_14` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `purch_last_14` Nullable(UInt64) CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, start_time)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.promo_start;
CREATE TABLE elka2024_dev.promo_start
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_id` UInt64 CODEC(T64, LZ4),
    `user_id` UInt64 Codec(T64, LZ4),
    `start_time` DateTime CODEC(DoubleDelta, LZ4),
    `expire_time` DateTime CODEC(DoubleDelta, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` UInt32 Codec(T64, LZ4),
    `promo_id` UInt64 CODEC(T64, LZ4),
    `offer_id` UInt32 CODEC(T64, LZ4),
    `award` Nullable(String) Codec(LZ4HC()),
    `price` Nullable(String) Codec(LZ4HC()),
    `avg_bill_10_last` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `convert_10_last` Nullable(UInt64) CODEC(T64, LZ4),
    `last_purchase_date` Nullable(UInt64) CODEC(T64, LZ4),
    `avg_bill_last_14` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `purch_last_14` Nullable(UInt64) CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(start_time), platform_type)
ORDER BY (platform_type, start_time)
SETTINGS index_granularity = 8192;