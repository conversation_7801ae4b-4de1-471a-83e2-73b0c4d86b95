-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.http_oplogs_actions_extracted;
CREATE TABLE elka2024_etl_dev.http_oplogs_actions_extracted
(
    `id` Int16 Codec(Delta, LZ4),
    `platform_type` LowCardinality(String) Codec(LZ4HC()),
    `action_name` LowCardinality(String) Codec(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (id, platform_type)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.oplogs_actions;
CREATE TABLE elka2024_dev.oplogs_actions
(
    `id` Int16 Codec(Delta, LZ4),
    `platform_type` LowCardinality(String) Codec(LZ4HC()),
    `action_name` LowCardinality(String) Codec(LZ4HC())
)
ENGINE = ReplacingMergeTree
ORDER BY (id, platform_type)
SETTINGS index_granularity = 8192;