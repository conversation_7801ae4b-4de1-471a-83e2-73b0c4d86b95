
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.cdn_logs_extracted;
CREATE TABLE elka2024_etl_dev.cdn_logs_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_prefix` String CODEC(LZ4HC(0)),
    `_prefix_datetime` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_filename` String CODEC(LZ4HC(0)),
    `remote_addr` Nullable(String) CODEC(LZ4HC(0)),
    -- not used "-"
    `remote_user` Nullable(String) CODEC(LZ4HC(0)),
    `time_local` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `request` Nullable(String) CODEC(LZ4HC(0)),
    `status` UInt16 CODEC(T64, LZ4),
    `body_bytes_sent` UInt32 CODEC(T64, LZ4),
    `http_referer` Nullable(String) CODEC(LZ4HC(0)),
    `http_user_agent` Nullable(String) CODEC(LZ4HC(0)),
    `bytes_sent` UInt32 CODEC(T64, LZ4),
    `edgename` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `scheme` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `host` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    -- Потому что с точностью до мс
    `request_time` Decimal(10, 3) CODEC(T64, LZ4),
    `upstream_response_time_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_response_time` Nullable(Decimal(10, 3)) CODEC(T64, LZ4),
    `request_length` UInt32 CODEC(T64, LZ4),
    `http_range` Nullable(String) CODEC(LZ4HC(0)),
    `http_range_bytes_from` Nullable(UInt32) CODEC(LZ4HC(0)),
    `http_range_bytes_to` Nullable(UInt32) CODEC(LZ4HC(0)),
    `responding_node` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_cache_status` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `upstream_response_length_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_response_length` Nullable(UInt32) CODEC(T64, LZ4),
    `upstream_addr` Nullable(String) CODEC(LZ4HC(0)),
    `gcdn_api_client_id` UInt32 CODEC(T64, LZ4),
    `gcdn_api_resource_id` UInt32 CODEC(T64, LZ4),
    `uid_got` Nullable(String) CODEC(LZ4HC(0)),
    `uid_set` Nullable(String) CODEC(LZ4HC(0)),
    `geoip_country_code` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geoip_city` Nullable(String) CODEC(LZ4HC(0)),
    `shield_type` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `server_addr` Nullable(String) CODEC(LZ4HC(0)),
    `server_port` UInt16 CODEC(T64, LZ4),
    `upstream_status_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_status` Nullable(UInt32) CODEC(T64, LZ4),
    -- not used "-"
    -- Потому что с точностью до мс
    `upstream_connect_time_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_connect_time` Nullable(Decimal(10, 3)) DEFAULT NULL CODEC(T64, LZ4),
    -- Потому что с точностью до мс
    `upstream_header_time_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_header_time` Nullable(Decimal(10, 3)) DEFAULT NULL CODEC(T64, LZ4),
    `shard_addr` Nullable(String) CODEC(LZ4HC(0)),
    `geoip2_data_asnumber` Nullable(UInt64) CODEC(T64, LZ4),
    `connection` UInt64 CODEC(T64, LZ4),
    `connection_requests` UInt32 CODEC(T64, LZ4),
    `request_id` Nullable(String) CODEC(LZ4HC(0)),
    `http_x_forwarded_proto` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `http_x_forwarded_request_id` Nullable(String) CODEC(LZ4HC(0)),
    `ssl_cipher` Nullable(String) CODEC(LZ4HC(0)),
    `ssl_session_id` Nullable(String) CODEC(LZ4HC(0)),
    `ssl_session_reused` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `sent_http_content_type` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `real_tcpinfo_rtt` UInt32 CODEC(T64, LZ4),
    `http_x_forwarded_http_ver` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `vp_enabled` UInt8 CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (time_local)
SETTINGS index_granularity = 8192

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.cdn_logs;
CREATE TABLE elka2024_dev.cdn_logs
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_prefix` String CODEC(LZ4HC(0)),
    `_prefix_datetime` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `_filename` String CODEC(LZ4HC(0)),
    `remote_addr` Nullable(String) CODEC(LZ4HC(0)),
    -- not used "-"
    `remote_user` Nullable(String) CODEC(LZ4HC(0)),
    `time_local` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `request` Nullable(String) CODEC(LZ4HC(0)),
    `status` UInt16 CODEC(T64, LZ4),
    `body_bytes_sent` UInt32 CODEC(T64, LZ4),
    `http_referer` Nullable(String) CODEC(LZ4HC(0)),
    `http_user_agent` Nullable(String) CODEC(LZ4HC(0)),
    `bytes_sent` UInt32 CODEC(T64, LZ4),
    `edgename` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `scheme` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `host` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    -- Потому что с точностью до мс
    `request_time` Decimal(10, 3) CODEC(T64, LZ4),
    `upstream_response_time_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_response_time` Nullable(Decimal(10, 3)) CODEC(T64, LZ4),
    `request_length` UInt32 CODEC(T64, LZ4),
    `http_range` Nullable(String) CODEC(LZ4HC(0)),
    `http_range_bytes_from` Nullable(UInt32) CODEC(LZ4HC(0)),
    `http_range_bytes_to` Nullable(UInt32) CODEC(LZ4HC(0)),
    `responding_node` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_cache_status` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `upstream_response_length_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_response_length` Nullable(UInt32) CODEC(T64, LZ4),
    `upstream_addr` Nullable(String) CODEC(LZ4HC(0)),
    `gcdn_api_client_id` UInt32 CODEC(T64, LZ4),
    `gcdn_api_resource_id` UInt32 CODEC(T64, LZ4),
    `uid_got` Nullable(String) CODEC(LZ4HC(0)),
    `uid_set` Nullable(String) CODEC(LZ4HC(0)),
    `geoip_country_code` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `geoip_city` Nullable(String) CODEC(LZ4HC(0)),
    `shield_type` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `server_addr` Nullable(String) CODEC(LZ4HC(0)),
    `server_port` UInt16 CODEC(T64, LZ4),
    `upstream_status_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_status` Nullable(UInt32) CODEC(T64, LZ4),
    -- not used "-"
    -- Потому что с точностью до мс
    `upstream_connect_time_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_connect_time` Nullable(Decimal(10, 3)) DEFAULT NULL CODEC(T64, LZ4),
    -- Потому что с точностью до мс
    `upstream_header_time_raw` Nullable(String) CODEC(LZ4HC(0)),
    `upstream_header_time` Nullable(Decimal(10, 3)) DEFAULT NULL CODEC(T64, LZ4),
    `shard_addr` Nullable(String) CODEC(LZ4HC(0)),
    `geoip2_data_asnumber` Nullable(UInt64) CODEC(T64, LZ4),
    `connection` UInt64 CODEC(T64, LZ4),
    `connection_requests` UInt32 CODEC(T64, LZ4),
    `request_id` Nullable(String) CODEC(LZ4HC(0)),
    `http_x_forwarded_proto` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `http_x_forwarded_request_id` Nullable(String) CODEC(LZ4HC(0)),
    `ssl_cipher` Nullable(String) CODEC(LZ4HC(0)),
    `ssl_session_id` Nullable(String) CODEC(LZ4HC(0)),
    `ssl_session_reused` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `sent_http_content_type` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `real_tcpinfo_rtt` UInt32 CODEC(T64, LZ4),
    `http_x_forwarded_http_ver` LowCardinality(Nullable(String)) CODEC(LZ4HC(0)),
    `vp_enabled` UInt8 CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (time_local)
PARTITION BY toYYYYMM(time_local)
SETTINGS index_granularity = 8192;