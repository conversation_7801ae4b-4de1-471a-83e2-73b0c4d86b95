
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.dau;
CREATE TABLE elka2024_etl_dev.dau
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `date_start` Date CODEC(DoubleDelta, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYYYYMM(date_start))
ORDER BY (platform_type, date_start)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.agg_retention;
CREATE TABLE elka2024_dev.agg_retention
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `dt` Date CODEC(DoubleDelta, LZ4),
    `install_dt` Date CODEC(DoubleDelta, LZ4),
    `day_number` UInt16 CODEC(T64, LZ4),
    `total_users` UInt32 CODEC(T64, LZ4),
    `active_users` UInt32 CODEC(T64, LZ4),
    `active_10_days_ago` UInt32 CODEC(T64, LZ4),
    `churned_users` UInt32 CODEC(T64, LZ4),
    `reactivated_users` UInt32 CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYear(dt))
ORDER BY (platform_type, dt)
SETTINGS index_granularity = 8192;

