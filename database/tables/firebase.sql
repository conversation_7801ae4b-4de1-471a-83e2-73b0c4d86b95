-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.firebase_analytics_extracted;
CREATE TABLE elka2024_etl_dev.firebase_analytics_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` String Codec(LZ4HC()),
    `event_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_timestamp` Int64 CODEC(T64, LZ4),
    `event_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_params` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_previous_timestamp` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `event_value_in_usd` Nullable(Float32) DEFAULT NULL CODEC(Gorilla, LZ4),
    `event_bundle_sequence_id` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_pseudo_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_properties` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_first_touch_timestamp` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `user_ltv` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `device` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `geo` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `app_info` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `traffic_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `platform` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_dimensions` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ecommerce` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `items` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY event_timestamp
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.firebase_crashlytics_extracted;
CREATE TABLE elka2024_etl_dev.firebase_crashlytics_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `platform` LowCardinality(String) Codec(LZ4HC()),
    `event_id` Nullable(String) default null Codec(LZ4HC()),
    `issue_id` Nullable(String) default null Codec(LZ4HC()),
    `issue_title` Nullable(String) default null Codec(LZ4HC()),
    `issue_subtitle` Nullable(String) default null Codec(LZ4HC()),
    `event_timestamp` DateTime Codec(DoubleDelta, LZ4),
    `received_timestamp` Nullable(DateTime) default null Codec(DoubleDelta, LZ4),
    `device` Nullable(String) default null Codec(LZ4HC()),
    `memory` Nullable(String) default null Codec(LZ4HC()),
    `storage` Nullable(String) default null Codec(LZ4HC()),
    `operating_system` Nullable(String) default null Codec(LZ4HC()),
    `application` Nullable(String) default null Codec(LZ4HC()),
    `user` Nullable(String) default null Codec(LZ4HC()),
    `installation_uuid` Nullable(String) default null Codec(LZ4HC()),
    `crashlytics_sdk_version` Nullable(String) default null Codec(LZ4HC()),
    `app_orientation` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `device_orientation` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `process_state` Nullable(String) default null Codec(LZ4HC()),
    `logs` Nullable(String) default null Codec(LZ4HC()),
    `breadcrumbs` Nullable(String) default null Codec(LZ4HC()),
    `blame_frames` Nullable(String) default null Codec(LZ4HC()),
    `exceptions` Nullable(String) default null Codec(LZ4HC()),
    `errors` Nullable(String) default null Codec(LZ4HC()),
    `threads` Nullable(String) default null Codec(LZ4HC())
)
ENGINE = MergeTree
ORDER BY (platform, event_timestamp)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.firebase_messaging_extracted;
CREATE TABLE elka2024_etl_dev.firebase_messaging_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `event_timestamp` DateTime Codec(DoubleDelta, LZ4),
    `project_number` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `message_id` Nullable(String) default null Codec(LZ4HC()),
    `instance_id` Nullable(String) default null Codec(LZ4HC()),
    `message_type` Nullable(String) default null Codec(LZ4HC()),
    `sdk_platform` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `collapse_key` Nullable(String) default null Codec(LZ4HC()),
    `priority` Nullable(Int32) DEFAULT NULL CODEC(T64, LZ4),
    `ttl` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `bulk_id` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `event` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `analytics_label` Nullable(String) default null Codec(LZ4HC())
)
ENGINE = MergeTree
ORDER BY event_timestamp
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.firebase_analytics;
CREATE TABLE elka2024_dev.firebase_analytics
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` String Codec(LZ4HC()),
    `event_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_timestamp` Int64 CODEC(T64, LZ4),
    `event_name` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_params` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_previous_timestamp` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `event_value_in_usd` Nullable(Float32) DEFAULT NULL CODEC(Gorilla, LZ4),
    `event_bundle_sequence_id` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_pseudo_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_properties` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_first_touch_timestamp` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `user_ltv` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `device` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `geo` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `app_info` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `traffic_source` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `platform` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_dimensions` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ecommerce` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `items` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY event_timestamp
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.firebase_crashlytics;
CREATE TABLE elka2024_dev.firebase_crashlytics
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `platform` LowCardinality(String) Codec(LZ4HC()),
    `event_id` Nullable(String) default null Codec(LZ4HC()),
    `issue_id` Nullable(String) default null Codec(LZ4HC()),
    `issue_title` Nullable(String) default null Codec(LZ4HC()),
    `issue_subtitle` Nullable(String) default null Codec(LZ4HC()),
    `event_timestamp` DateTime Codec(DoubleDelta, LZ4),
    `received_timestamp` Nullable(DateTime) default null Codec(DoubleDelta, LZ4),
    `device` Nullable(String) default null Codec(LZ4HC()),
    `memory` Nullable(String) default null Codec(LZ4HC()),
    `storage` Nullable(String) default null Codec(LZ4HC()),
    `operating_system` Nullable(String) default null Codec(LZ4HC()),
    `application` Nullable(String) default null Codec(LZ4HC()),
    `user` Nullable(String) default null Codec(LZ4HC()),
    `installation_uuid` Nullable(String) default null Codec(LZ4HC()),
    `crashlytics_sdk_version` Nullable(String) default null Codec(LZ4HC()),
    `app_orientation` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `device_orientation` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `process_state` Nullable(String) default null Codec(LZ4HC()),
    `logs` Nullable(String) default null Codec(LZ4HC()),
    `breadcrumbs` Nullable(String) default null Codec(LZ4HC()),
    `blame_frames` Nullable(String) default null Codec(LZ4HC()),
    `exceptions` Nullable(String) default null Codec(LZ4HC()),
    `errors` Nullable(String) default null Codec(LZ4HC()),
    `threads` Nullable(String) default null Codec(LZ4HC())
)
ENGINE = MergeTree
PARTITION BY (platform, toYYYYMM(event_timestamp))
ORDER BY (platform, event_timestamp)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_dev.firebase_messaging;
CREATE TABLE elka2024_dev.firebase_messaging
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `event_timestamp` DateTime Codec(DoubleDelta, LZ4),
    `project_number` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `message_id` Nullable(String) default null Codec(LZ4HC()),
    `instance_id` Nullable(String) default null Codec(LZ4HC()),
    `message_type` Nullable(String) default null Codec(LZ4HC()),
    `sdk_platform` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `collapse_key` Nullable(String) default null Codec(LZ4HC()),
    `priority` Nullable(Int32) DEFAULT NULL CODEC(T64, LZ4),
    `ttl` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `bulk_id` Nullable(Int64) DEFAULT NULL CODEC(T64, LZ4),
    `event` LowCardinality(Nullable(String)) default null Codec(LZ4HC()),
    `analytics_label` Nullable(String) default null Codec(LZ4HC())
)
ENGINE = MergeTree
PARTITION BY toYYYYMM(event_timestamp)
ORDER BY event_timestamp
SETTINGS index_granularity = 8192;