-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_oplogs_extracted;
CREATE TABLE elka2024_etl_dev.mysql_oplogs_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` String Codec(LZ4HC()),
    `_id` UInt64 Codec(T64, LZ4),
    `oplog_type` LowCardinality(String) Codec(LZ4HC()),
    `platform_type` LowCardinality(String) Codec(LZ4HC()),
    `user_id` UInt64 Codec(T64, LZ4),
    `clan_id` Nullable(UInt32) Codec(T64, LZ4),
    `session_id` Nullable(String) Codec(LZ4HC()),
    `session_number` UInt32 Codec(T64, LZ4),
    `date` DateTime Codec(DoubleDelta, ZSTD),
    `app_type` UInt32 Codec(T64, LZ4),
    `screen` Int8 Codec(T64, LZ4),
    `level` UInt32 Codec(T64, LZ4),
    `progress` UInt32 Codec(T64, LZ4),
    `cash` UInt32 Codec(T64, LZ4),
    `energy` UInt64 Codec(T64, LZ4),
    `item_id` String Codec(LZ4HC()),
    `action` UInt16 Codec(T64, LZ4),
    `extra` Nullable(String) Codec(LZ4HC()),
    `amount_change` Int64 Codec(T64, LZ4),
    `amount_current` UInt64 Codec(T64, LZ4),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, date, action)
SETTINGS index_granularity = 8192;

-- Основные таблицы, которые требуются аналитикам

DROP TABLE IF EXISTS elka2024_dev.oplogs;
CREATE TABLE elka2024_dev.oplogs
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` String Codec(LZ4HC()),
    `_id` UInt64 Codec(T64, LZ4),
    `oplog_type` LowCardinality(String) Codec(LZ4HC()),
    `platform_type` LowCardinality(String) Codec(LZ4HC()),
    `user_id` UInt64 Codec(T64, LZ4),
    `clan_id` Nullable(UInt32) Codec(T64, LZ4),
    `session_id` Nullable(String) Codec(LZ4HC()),
    `session_number` UInt32 Codec(T64, LZ4),
    `date` DateTime Codec(DoubleDelta, ZSTD),
    `app_type` UInt32 Codec(T64, LZ4),
    `screen` Int8 Codec(T64, LZ4),
    `level` UInt32 Codec(T64, LZ4),
    `progress` UInt32 Codec(T64, LZ4),
    `cash` UInt32 Codec(T64, LZ4),
    `energy` UInt64 Codec(T64, LZ4),
    `item_id` String Codec(LZ4HC()),
    `action` UInt16 Codec(T64, LZ4),
    `extra` Nullable(String) Codec(LZ4HC()),
    `amount_change` Int64 Codec(T64, LZ4),
    `amount_current` UInt64 Codec(T64, LZ4),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `progress_level` UInt32 CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYYYYMM(date))
ORDER BY (platform_type, date, action)
SETTINGS index_granularity = 8192;