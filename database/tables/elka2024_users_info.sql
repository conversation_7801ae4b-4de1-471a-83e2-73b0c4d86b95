
-- Вспомогательные таблицы

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS datamarts_dev.elka2024_users_info_load;
CREATE TABLE datamarts_dev.elka2024_users_info_load
(
    `_insert_datetime` DateTime DEFAULT now() CODEC (DoubleDelta, ZSTD(1)),
    `platform_type` LowCardinality(String) CODEC (LZ4HC(0)),
    `user_id` UInt64 CODEC (T64, LZ4),
    `user_id_platform` Nullable(String) CODEC (LZ4HC(0)),
    `level` Nullable(UInt32) CODEC (T64, LZ4),
    `login_time` Nullable(DateTime) CODEC (DoubleDelta, ZSTD(1)),
    `install_time` Nullable(DateTime) CODEC (DoubleDelta, ZSTD(1)),
    `referrer` LowCardinality(Nullable(String)) CODEC (LZ4HC(0)),
    `referrer_data` LowCardinality(Nullable(String)) CODEC (LZ4HC(0)),
    `referrer_user_id` Nullable(UInt64) CODEC (T64, LZ4)
)
ENGINE = ReplacingMergeTree(_insert_datetime)
ORDER BY (platform_type, user_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS datamarts_dev.elka2024_users_info;
CREATE VIEW datamarts_dev.elka2024_users_info AS
SELECT * FROM datamarts_dev.elka2024_users_info_load FINAL;