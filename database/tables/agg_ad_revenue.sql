
-- Вспомогательные таблицы

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.agg_ad_revenue;
CREATE TABLE elka2024_dev.agg_ad_revenue
(
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `dt` Date CODEC(DoubleDelta, LZ4),
    `install_dt` Date CODEC(DoubleDelta, LZ4),
    `day_number` UInt16 CODEC(T64, LZ4),
    `geo_country` LowCardinality(String) CODEC(LZ4HC(0)),
    `ads_watched` UInt32 CODEC(T64, LZ4),
    `banner_ads_web` UInt32 CODEC(T64, LZ4),
    `banner_ads_mobile` UInt32 CODEC(T64, LZ4),
    `ad_revenue_usd` Nullable(Float32) CODEC(Gorilla, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYear(dt))
ORDER BY (platform_type, dt)
SETTINGS index_granularity = 8192;

