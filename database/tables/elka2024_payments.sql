
-- Вспомогательные таблицы

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS datamarts_dev.elka2024_payments_load;
CREATE TABLE datamarts_dev.elka2024_payments_load
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC (DoubleDelta, ZSTD(1)),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `id` UInt64 CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `user_id_platform` Nullable(String) CODEC(LZ4HC(0)),
    `payment_date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `price` Decimal(8, 2) Codec(T64, LZ4),
    `currency` LowCardinality(Nullable(String)) Codec(LZ4HC()),
    `local_price` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `status` Nullable(UInt8) DEFAULT NULL CODEC(T64, LZ4)
)
ENGINE = ReplacingMergeTree(_insert_datetime)
ORDER BY (platform_type, user_id, id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS datamarts_dev.elka2024_payments;
CREATE VIEW datamarts_dev.elka2024_payments AS
SELECT * FROM datamarts_dev.elka2024_payments_load FINAL;