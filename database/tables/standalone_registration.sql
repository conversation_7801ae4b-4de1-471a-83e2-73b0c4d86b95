
-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.standalone_registration_extracted;
CREATE TABLE elka2024_etl_dev.standalone_registration_extracted
(
    `_filename` String CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_validation_result` UInt8 CODEC(T64, LZ4),
    `pregame_user_id` String CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `language` LowCardinality(String) CODEC(LZ4HC(0)),
    `referrer_data` String CODEC(LZ4HC(0)),
    `extra` String CODEC(LZ4HC(0)),
    `browser_name` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (eventtype, date)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.standalone_registration_errors;
CREATE TABLE elka2024_etl_dev.standalone_registration_errors
(
    `filename` String CODEC(LZ4HC(0)),
    `insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `error_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `error_details` String CODEC(LZ4HC(0)),
    `json` String CODEC(LZ4HC(0))
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(date))
ORDER BY (date, eventtype)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.standalone_registration;
CREATE TABLE elka2024_dev.standalone_registration
(
    `_filename` String CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_validation_result` UInt8 CODEC(T64, LZ4),
    `pregame_user_id` String CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `eventtype` LowCardinality(String) CODEC(LZ4HC(0)),
    `language` LowCardinality(String) CODEC(LZ4HC(0)),
    `referrer_data` String CODEC(LZ4HC(0)),
    `extra` String CODEC(LZ4HC(0)),
    `browser_name` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` Nullable(UInt8) CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(date))
ORDER BY (eventtype, date)
SETTINGS index_granularity = 8192;
