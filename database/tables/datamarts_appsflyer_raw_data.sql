-- Вспомогательные таблицы

DROP TABLE IF EXISTS datamarts_etl_dev.appsflyer_raw_data_extracted;
CREATE TABLE datamarts_etl_dev.appsflyer_raw_data_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_report_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `channel` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_value` Nullable(Float32) CODEC(Gorilla, LZ4),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_retargeting` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `media_source` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `app_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_id_human` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_version` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `bundle_id` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `country_code` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_category` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `event_revenue` Nullable(Float32) CODEC(Gorilla, LZ4),
    `event_revenue_currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_app_store` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `language` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `operator` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `os_version` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `platform` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `sdk_version` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `wifi` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `store_reinstall` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `attributed_touch_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_revenue_preferred` Nullable(Float32) CODEC(Gorilla, LZ4),
    `event_source` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_primary_attribution` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_receipt_validated` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `match_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `region` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `retargeting_conversation_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `monetization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `impressions` Nullable(UInt32) CODEC(T64, LZ4),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (app_id_human, event_time, event_name, media_source, campaign, country_code)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS datamarts_dev.appsflyer_raw_data;
CREATE TABLE datamarts_dev.appsflyer_raw_data
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_report_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_set` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_set_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `channel` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `campaign_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `cost_value` Nullable(Float32) CODEC(Gorilla, LZ4),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `sub_site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_retargeting` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `media_source` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `original_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `user_agent` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `advertising_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `android_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `app_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_id_human` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_version` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `apps_flyer_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `bundle_id` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `carrier` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `country_code` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `custom_data` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `deeplink_url` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_category` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_model` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `device_download_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_name` LowCardinality(String) DEFAULT '' CODEC(LZ4HC(0)),
    `event_revenue` Nullable(Float32) CODEC(Gorilla, LZ4),
    `event_revenue_currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `event_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `idfa` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `idfv` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `imei` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_app_store` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `ip` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_lat` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `language` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `oaid` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `att` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `operator` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `os_version` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `platform` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `sdk_version` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `wifi` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `store_reinstall` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `amazon_fire_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `city` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `attributed_touch_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `attributed_touch_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `dma` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `event_revenue_preferred` Nullable(Float32) CODEC(Gorilla, LZ4),
    `event_source` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `http_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_primary_attribution` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_receipt_validated` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `match_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `postal_code` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `region` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `retargeting_conversation_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `state` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keyword_match_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `network_account_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `rejected_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `rejected_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `blocked_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `blocked_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `blocked_reason_value` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `google_play_click_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_install_begin_time` DateTime DEFAULT toDateTime('1970-01-01 00:00:00') CODEC(DoubleDelta, ZSTD),
    `google_play_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `google_play_broadcast_referrer` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_unit` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `segment` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `placement` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `monetization_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `impressions` Nullable(UInt32) CODEC(T64, LZ4),
    `mediation_network` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `custom_dimension` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `app_type` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `fraud_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `fraud_sub_reason` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_organic` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `detection_date` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `customer_user_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `attribution_lookback` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `environment` Nullable(Enum8('dev' = 1, 'prod' = 2)) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (toYYYYMM(event_time))
ORDER BY (app_id_human, event_time, event_name, media_source, campaign, country_code)
SETTINGS index_granularity = 8192;