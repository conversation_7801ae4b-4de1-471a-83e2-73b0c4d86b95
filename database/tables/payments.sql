-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.mysql_payments_extracted;
CREATE TABLE elka2024_etl_dev.mysql_payments_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_update_time` UInt64 CODEC(T64, LZ4),
    `id` UInt64 CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` Nullable(String) CODEC(LZ4HC(0)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` UInt32 Codec(T64, LZ4),
    `level` Nullable(UInt32) Codec(T64, LZ4),
    `factory_level` Nullable(UInt32) Codec(T64, LZ4),
    `merge_level` Nullable(UInt32) Codec(T64, LZ4),
    `progress` Nullable(UInt32) Codec(T64, LZ4),
    `cash` Nullable(UInt32) Codec(T64, LZ4),
    `energy` Nullable(UInt64) Codec(T64, LZ4),
    `status` Nullable(UInt8) Codec(T64, LZ4),
    `transaction_id` Nullable(String) Codec(LZ4HC()),
    `price` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `currency` LowCardinality(Nullable(String)) Codec(LZ4HC()),
    `item_type` LowCardinality(Nullable(String)) Codec(LZ4HC()),
    `item_id` Nullable(UInt32) Codec(T64, LZ4),
    `award` Nullable(String) Codec(LZ4HC()),
    `payment_number` Nullable(UInt32) Codec(T64, LZ4),
    `local_price` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `item_extra_id` Nullable(UInt32) Codec(T64, LZ4),
    `gift_id` Nullable(UInt8) Codec(T64, LZ4),
    `bonus_id` Nullable(UInt8) Codec(T64, LZ4),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `xsolla` Nullable(UInt8) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, user_id)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS elka2024_etl_dev.payments;
CREATE TABLE elka2024_etl_dev.payments
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_platform` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_update_time` UInt64 CODEC(T64, LZ4),
    `id` UInt64 CODEC(T64, LZ4),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` Nullable(String) CODEC(LZ4HC(0)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `date` DateTime CODEC(DoubleDelta, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_type` UInt32 Codec(T64, LZ4),
    `level` Nullable(UInt32) Codec(T64, LZ4),
    `factory_level` Nullable(UInt32) Codec(T64, LZ4),
    `merge_level` Nullable(UInt32) Codec(T64, LZ4),
    `progress` Nullable(UInt32) Codec(T64, LZ4),
    `cash` Nullable(UInt32) Codec(T64, LZ4),
    `energy` Nullable(UInt64) Codec(T64, LZ4),
    `status` Nullable(UInt8) Codec(T64, LZ4),
    `transaction_id` Nullable(String) Codec(LZ4HC()),
    `price` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `currency` LowCardinality(Nullable(String)) Codec(LZ4HC()),
    `item_type` LowCardinality(Nullable(String)) Codec(LZ4HC()),
    `item_id` Nullable(UInt32) Codec(T64, LZ4),
    `award` Nullable(String) Codec(LZ4HC()),
    `payment_number` Nullable(UInt32) Codec(T64, LZ4),
    `local_price` Nullable(Decimal(8,2)) Codec(T64, LZ4),
    `item_extra_id` Nullable(UInt32) Codec(T64, LZ4),
    `gift_id` Nullable(UInt8) Codec(T64, LZ4),
    `bonus_id` Nullable(UInt8) Codec(T64, LZ4),
    `no_ads` UInt8 CODEC(T64, LZ4),
    `tokens` UInt32 CODEC(T64, LZ4),
    `passing_mode_level` UInt16 CODEC(T64, LZ4),
    `xsolla` Nullable(UInt8) DEFAULT NULL Codec(T64, LZ4)
)
ENGINE = ReplacingMergeTree(_insert_datetime)
PARTITION BY platform_type
ORDER BY (platform_type, user_id, id)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.payments;
CREATE VIEW elka2024_dev.payments
AS SELECT * FROM elka2024_etl_dev.payments FINAL;