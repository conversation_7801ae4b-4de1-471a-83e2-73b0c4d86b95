-- Вспомогательные таблицы

DROP TABLE IF EXISTS datamarts_etl_dev.extract_state;
CREATE TABLE datamarts_etl_dev.extract_state
(
    `connection_id` LowCardinality(String) CODEC (LZ4HC(0)),
    `insert_datetime` DateTime DEFAULT now() CODEC (DoubleDelta, ZSTD(1)),
    `source_name` LowCardinality(String) CODEC (LZ4HC(0)),
    `key_name` LowCardinality(String) CODEC (LZ4HC(0)),
    `last_value_int` Nullable(UInt64) CODEC (T64, LZ4),
    `last_value_str` Nullable(String) CODEC (LZ4HC(0)),
    `last_value_date` Nullable(DateTime) DEFAULT null CODEC (DoubleDelta, ZSTD(1))
)
ENGINE = ReplacingMergeTree(insert_datetime)
ORDER BY (connection_id, source_name, key_name)
SETTINGS index_granularity = 8192;

DROP TABLE IF EXISTS datamarts_etl_dev.appsflyer_cohort_user_acquisition_extracted;
CREATE TABLE datamarts_etl_dev.appsflyer_cohort_user_acquisition_extracted
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `conversion_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_primary_attribution` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `attributed_touch_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `days_post_attribution` Nullable(UInt16) DEFAULT NULL CODEC(T64, LZ4),
    `conversion_date` Nullable(Date) DEFAULT NULL CODEC(DoubleDelta, ZSTD(1)),
    `event_date` Date CODEC(Delta(2), LZ4),
    `event_name` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `media_source` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `channel` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `geo` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `agency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `selected_currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `unique_users` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `event_count` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `revenue_usd` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `revenue_selected_currency` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `app_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_id_human` LowCardinality(String) CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0)),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0))
)
ENGINE = MergeTree
ORDER BY (app_id_human, event_date)
SETTINGS index_granularity = 8192;

-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS datamarts_dev.appsflyer_cohort_user_acquisition;
CREATE TABLE datamarts_dev.appsflyer_cohort_user_acquisition
(
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `conversion_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `is_primary_attribution` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `attributed_touch_type` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `days_post_attribution` Nullable(UInt16) DEFAULT NULL CODEC(T64, LZ4),
    `conversion_date` Nullable(Date) DEFAULT NULL CODEC(DoubleDelta, ZSTD(1)),
    `event_date` Date CODEC(Delta(2), LZ4),
    `event_name` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `media_source` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `campaign_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `adset_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `ad_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `channel` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `site_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `keywords` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `geo` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `agency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `install_app_store` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0)),
    `selected_currency` LowCardinality(Nullable(String)) DEFAULT NULL CODEC(LZ4HC(0)),
    `unique_users` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `event_count` Nullable(UInt32) DEFAULT NULL CODEC(T64, LZ4),
    `revenue_usd` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `revenue_selected_currency` Nullable(Float64) DEFAULT NULL CODEC(Gorilla, LZ4),
    `app_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `app_id_human` LowCardinality(String) CODEC(LZ4HC(0)),
    `filename` String CODEC(LZ4HC(0)),
    `keyword_id` Nullable(String) DEFAULT NULL CODEC(LZ4HC(0))
)
ENGINE = MergeTree
PARTITION BY toYYYYMM(event_date)
ORDER BY (app_id_human, event_date)
SETTINGS index_granularity = 8192;
