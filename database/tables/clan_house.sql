-- Вспомогательные таблицы

DROP TABLE IF EXISTS elka2024_etl_dev.clan_house_stage1_extracted;
CREATE TABLE elka2024_etl_dev.clan_house_stage1_extracted
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_oplog_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` LowCardinality(String) Codec(LZ4HC()),
    `_id` UInt64 Codec(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` Nullable(String) CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `progress_level` Nullable(UInt32) CODEC(T64, LZ4),
    `level_reached` UInt16 CODEC(T64, LZ4)
)
ENGINE = MergeTree
ORDER BY (platform_type, date)
SETTINGS index_granularity = 8192;


-- Основная таблица, которая требуется аналитикам

DROP TABLE IF EXISTS elka2024_dev.clan_house;
CREATE TABLE elka2024_dev.clan_house
(
    `_connection_id` LowCardinality(String) CODEC(LZ4HC(0)),
    `_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_oplog_insert_datetime` DateTime DEFAULT now() CODEC(DoubleDelta, ZSTD(1)),
    `_source_name` LowCardinality(String) Codec(LZ4HC()),
    `_id` UInt64 Codec(T64, LZ4),
    `platform_type` LowCardinality(String) CODEC(LZ4HC(0)),
    `user_id` UInt64 CODEC(T64, LZ4),
    `session_id` Nullable(String) CODEC(LZ4HC(0)),
    `date` DateTime CODEC(DoubleDelta, ZSTD(1)),
    `clan_id` Nullable(UInt32) CODEC(T64, LZ4),
    `level` Nullable(UInt32) CODEC(T64, LZ4),
    `progress` Nullable(UInt32) CODEC(T64, LZ4),
    `progress_level` Nullable(UInt32) CODEC(T64, LZ4),
    `level_reached` UInt16 CODEC(T64, LZ4)
)
ENGINE = MergeTree
PARTITION BY (platform_type, toYYYYMM(date))
ORDER BY (platform_type, date)
SETTINGS index_granularity = 8192;





