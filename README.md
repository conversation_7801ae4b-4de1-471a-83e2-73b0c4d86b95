# ETL/ELT-процессы для проекта Ëлочка 2024

## Введение

Локальное окружение для разработки поднимается с помощью [Docker Desktop](https://www.docker.com/products/docker-desktop/).

Данный репозиторий является частью проекта "Ëлочка 2024", большинство процессов обработки данных (далее - ETL-процессы)
в качестве источника данных используют базы данных (MySQL, MongoDb), расположенные на бэкенде приложения. Соответственно, 
для локального запуска кода ETL-процессов необходимо наличие запущенного локально бэкенда приложения - 
[Docker-конфигурация бэкенда приложения](https://gitlab.stark.games/elka/backend/php-elka2021-dockerization)

Docker-конфигурации бэкенда приложения и ETL-процессов используют общую сеть с именем `stark_personal_network`

## Быстрый старт

Когда бэкенд приложения успешно запущен, можно приступать к запуску Docker-конфигурации ETL-процессов (данный репозиторий).

Сначала создадим локальные версии `.env`-файлов:
- копируем `/envs/clickhouse.sample.env` под именем `/envs/clickhouse.env`
- копируем `/envs/airflow.sample.env` под именем `/envs/airflow.env`. Для получения реквизитов доступа к Google Cloud,
AWS, Slack API нужно обратиться к своему лиду

Теперь, находясь в терминале в папке проекта, выполняем команду запуска Docker-конфигурации:
```
make up
```
С помощью [GNU Make](https://www.gnu.org/software/make/) реализован удобный запуск часто используемых команд с использованием 
утилиты `docker-compose`, их список можно увидеть с помощью команды
```
make help
```
либо непосредственно в исходном файле `/Makefile`. Наиболее востребованные команды:
- `up` и `stop` - для запуска и остановки сервисов (Airflow, Clickhouse, Redis)
- `build` - для сборки контейнеров после внесения изменений в исходные файлы Docker-конфигурации

После успешного запуска Docker-конфигурации в браузере по адресу `http://127.0.0.1:8081` 
станет (обычно спустя несколько минут) доступен web-интерфейс Apache Airflow, с помощью которого можно запускать ETL-процессы. 

В качестве простейшей проверки работоспособности локального окружения для разработки можно запустить DAG с именем `hello_world`.

## Подробная документация

Прочая информация, касающаяся разработки ETL-процессов, описана в вики проекта - [Data Engineering](https://stark-games.atlassian.net/wiki/spaces/elka2023/pages/7661944881/Data+Engineering)