pipeline {

    agent {
        label 'linux'
    } 

    parameters {

        choice choices: [
                'develop',
                'master'],
                description: 'Please choose target env',
                name: 'TARGET_BRANCH'
    }

    stages {
        stage('Init') {
            steps {
                checkout([$class: 'GitSCM',
                    branches: [[name: TARGET_BRANCH]],
                    extensions: [[$class: 'CleanBeforeCheckout']],
                    userRemoteConfigs: [[credentialsId: 'git-buildservice-ssh',
                        url: 'ssh://**********************:7999/elka/analytics/etl-elka-2024.git']]])
                script {
                    
                    currentBuild.description = "TARGET_BRANCH: ${TARGET_BRANCH}"
                }
            }
        }


        stage('Deploy master') {
            when {
                environment name: 'TARGET_BRANCH', value: 'master'
            }
            steps {
                sshagent(['airflow']) {

                    sh '''
                        rsync -e \"ssh -o StrictHostKeyChecking=no\" -rv --delete dags airflow@***********:/opt/airflow/dags
                        rsync -e \"ssh -o StrictHostKeyChecking=no\" -rv --delete docker/airflow/requirements.txt airflow@***********:/opt/airflow/dags
                        ssh airflow@*********** pip install -r /opt/airflow/dags/requirements.txt


                        rsync -rv --delete -e \"ssh -o StrictHostKeyChecking=no -A -J airflow@*********** \" dags airflow@************:/opt/airflow/dags
                        rsync -rv --delete -e \"ssh -o StrictHostKeyChecking=no -A -J airflow@*********** \" docker/airflow/requirements.txt airflow@************:/opt/airflow/dags
                        ssh -o StrictHostKeyChecking=no -A -J airflow@*********** airflow@************ pip install -r /opt/airflow/dags/requirements.txt

                         rsync -rv --delete -e \"ssh -o StrictHostKeyChecking=no -A -J airflow@*********** \" dags airflow@************:/opt/airflow/dags
                         rsync -rv --delete -e \"ssh -o StrictHostKeyChecking=no -A -J airflow@*********** \" docker/airflow/requirements.txt airflow@************:/opt/airflow/dags
                         ssh -o StrictHostKeyChecking=no -A -J airflow@*********** airflow@************ pip install -r /opt/airflow/dags/requirements.txt

                         rsync -rv --delete -e \"ssh -o StrictHostKeyChecking=no -A -J airflow@*********** \" dags airflow@************:/opt/airflow/dags
                         rsync -rv --delete -e \"ssh -o StrictHostKeyChecking=no -A -J airflow@*********** \" docker/airflow/requirements.txt airflow@************:/opt/airflow/dags
                         ssh -o StrictHostKeyChecking=no -A -J airflow@*********** airflow@************ pip install -r /opt/airflow/dags/requirements.txt


                    '''
                }
            }
        }

        stage('Deploy develop') {
            when {
                environment name: 'TARGET_BRANCH', value: 'develop'
            }

            steps {
                sshagent(['airflow']) { 
                    sh '''
                        
                        rsync -rv --delete dags airflow@**************:/opt/airflow/dags
                        rsync -rv --delete docker/airflow/requirements.txt airflow@**************:/opt/airflow/dags
                        ssh airflow@************** pip install -r /opt/airflow/dags/requirements.txt
                        
                    '''
                }
            }
        }
    }
    
    /*
    post {

        failure {
            slackSend(
                tokenCredentialId: 'slack-notification-token',
                channel: '#elka2023-build-logs',
                botUser: true,
                iconEmoji: ':x:',
                message: "Deploy analytics DAGS has failed",
                color: 'danger',
                username: "Elka builder"
            )
        }
    }
    */
}
