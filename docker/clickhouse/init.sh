#!/bin/bash

sleep 10 &&
while ! clickhouse-client --host clickhouse -q "SHOW databases"; do
    echo waiting for clickhouse up
    sleep 1
done

clickhouse-client --host clickhouse -q "CREATE USER IF NOT EXISTS $DATABASE_USER IDENTIFIED WITH sha256_password BY '$DATABASE_PASSWORD'"
clickhouse-client --host clickhouse -q "ALTER USER $DATABASE_USER DEFAULT ROLE ALL"
clickhouse-client --host clickhouse -q "GRANT ALL ON *.* TO $DATABASE_USER WITH GRANT OPTION"

find /var/clickhouse/schema -type f | while read FILE; do
    clickhouse-client --host clickhouse --user $DATABASE_USER --password $DATABASE_PASSWORD --queries-file $FILE
    echo "clickhouse-client executed file: $FILE"
done

find /var/clickhouse/tables -type f | while read FILE; do
    clickhouse-client --host clickhouse --user $DATABASE_USER --password $DATABASE_PASSWORD --queries-file $FILE
    echo "clickhouse-client executed file: $FILE"
done

CHANGES_SQL_FILE="/var/clickhouse/changes/changes.sql"
clickhouse-client --host clickhouse --user $DATABASE_USER --password $DATABASE_PASSWORD --queries-file $CHANGES_SQL_FILE
echo "clickhouse-client executed file: $CHANGES_SQL_FILE"

echo "Initialization success!"