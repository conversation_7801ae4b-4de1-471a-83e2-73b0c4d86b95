FROM apache/airflow:2.6.3-python3.9

USER root

RUN apt-get update \
    && apt-get install -y \
    apt-transport-https \
    ca-certificates  \
    default-libmysqlclient-dev \
    gcc \
    nano \
    dirmngr

RUN apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv 8919F6BD2B48D754
RUN echo "deb https://packages.clickhouse.com/deb stable main" | sudo tee \
    /etc/apt/sources.list.d/clickhouse.list

RUN apt-get update  \
    && apt-get install -y  \
    clickhouse-common-static=********  \
    clickhouse-client=********

USER airflow
ENV PATH=$PATH:/home/<USER>/.local/bin

RUN pip install --upgrade pip
RUN pip install --upgrade setuptools
COPY requirements.txt /tmp
RUN pip install -r /tmp/requirements.txt

ENTRYPOINT ["bash"]