#
# This file is autogenerated by pip-compile with Python 3.9
# by the following command:
#
#    pip-compile --output-file=requirements.txt docker/airflow/requirements.in
#
aiofiles==23.2.1
    # via gcloud-aio-storage
aiohttp==3.8.5
    # via
    #   apache-airflow-providers-http
    #   gcloud-aio-auth
    #   slackclient
aiosignal==1.3.1
    # via aiohttp
airflow-clickhouse-plugin==0.11.0
    # via -r docker/airflow/requirements.in
alembic==1.12.0
    # via
    #   apache-airflow
    #   sqlalchemy-spanner
anyio==4.0.0
    # via httpcore
apache-airflow==2.6.3
    # via
    #   -r docker/airflow/requirements.in
    #   airflow-clickhouse-plugin
    #   apache-airflow-providers-amazon
    #   apache-airflow-providers-common-sql
    #   apache-airflow-providers-ftp
    #   apache-airflow-providers-google
    #   apache-airflow-providers-http
    #   apache-airflow-providers-imap
    #   apache-airflow-providers-mongo
    #   apache-airflow-providers-mysql
    #   apache-airflow-providers-slack
    #   apache-airflow-providers-sqlite
apache-airflow-providers-amazon==8.6.0
    # via -r docker/airflow/requirements.in
apache-airflow-providers-common-sql==1.7.1
    # via
    #   airflow-clickhouse-plugin
    #   apache-airflow
    #   apache-airflow-providers-amazon
    #   apache-airflow-providers-google
    #   apache-airflow-providers-mysql
    #   apache-airflow-providers-slack
    #   apache-airflow-providers-sqlite
apache-airflow-providers-ftp==3.5.1
    # via apache-airflow
apache-airflow-providers-google==10.7.0
    # via -r docker/airflow/requirements.in
apache-airflow-providers-http==4.5.1
    # via
    #   apache-airflow
    #   apache-airflow-providers-amazon
apache-airflow-providers-imap==3.3.1
    # via apache-airflow
apache-airflow-providers-mongo==3.2.1
    # via -r docker/airflow/requirements.in
apache-airflow-providers-mysql==5.3.0
    # via -r docker/airflow/requirements.in
apache-airflow-providers-slack==8.0.0
    # via -r docker/airflow/requirements.in
apache-airflow-providers-sqlite==3.4.3
    # via apache-airflow
apispec[yaml]==5.2.2
    # via flask-appbuilder
argcomplete==3.1.1
    # via apache-airflow
asgiref==3.7.2
    # via
    #   apache-airflow
    #   apache-airflow-providers-amazon
    #   apache-airflow-providers-google
    #   apache-airflow-providers-http
asn1crypto==1.5.1
    # via scramp
async-timeout==4.0.3
    # via
    #   aiohttp
    #   redis
attrs==23.1.0
    # via
    #   aiohttp
    #   apache-airflow
    #   cattrs
    #   jsonschema
    #   looker-sdk
    #   referencing
babel==2.12.1
    # via flask-babel
backoff==2.2.1
    # via gcloud-aio-auth
beautifulsoup4==4.12.2
    # via redshift-connector
blinker==1.6.2
    # via apache-airflow
boto3==1.28.39
    # via
    #   apache-airflow-providers-amazon
    #   redshift-connector
    #   watchtower
botocore==1.31.39
    # via
    #   apache-airflow-providers-amazon
    #   boto3
    #   redshift-connector
    #   s3transfer
cachelib==0.9.0
    # via
    #   flask-caching
    #   flask-session
cachetools==5.3.1
    # via google-auth
cattrs==23.1.2
    # via
    #   apache-airflow
    #   looker-sdk
certifi==2023.7.22
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.15.1
    # via cryptography
chardet==5.2.0
    # via gcloud-aio-auth
charset-normalizer==3.2.0
    # via
    #   aiohttp
    #   requests
click==8.1.7
    # via
    #   clickclick
    #   flask
    #   flask-appbuilder
clickclick==20.10.2
    # via connexion
clickhouse-cityhash==*******
    # via -r docker/airflow/requirements.in
clickhouse-driver==0.2.6
    # via
    #   -r docker/airflow/requirements.in
    #   airflow-clickhouse-plugin
colorama==0.4.6
    # via flask-appbuilder
colorlog==4.8.0
    # via apache-airflow
configupdater==3.1.1
    # via apache-airflow
connexion[flask]==2.14.2
    # via apache-airflow
cron-descriptor==1.4.0
    # via apache-airflow
croniter==1.4.1
    # via apache-airflow
cryptography==41.0.3
    # via
    #   apache-airflow
    #   gcloud-aio-auth
    #   pyopenssl
db-dtypes==1.1.1
    # via pandas-gbq
decorator==5.1.1
    # via jsonpath-ng
deprecated==1.2.14
    # via
    #   apache-airflow
    #   limits
dill==0.3.7
    # via apache-airflow
dnspython==2.4.2
    # via
    #   apache-airflow-providers-mongo
    #   email-validator
    #   pymongo
docutils==0.20.1
    # via python-daemon
email-validator==1.3.1
    # via flask-appbuilder
exceptiongroup==1.1.3
    # via
    #   anyio
    #   cattrs
flask==2.2.5
    # via
    #   apache-airflow
    #   connexion
    #   flask-appbuilder
    #   flask-babel
    #   flask-caching
    #   flask-jwt-extended
    #   flask-limiter
    #   flask-login
    #   flask-session
    #   flask-sqlalchemy
    #   flask-wtf
flask-appbuilder==4.3.1
    # via apache-airflow
flask-babel==2.0.0
    # via flask-appbuilder
flask-caching==2.0.2
    # via apache-airflow
flask-jwt-extended==4.5.2
    # via flask-appbuilder
flask-limiter==3.5.0
    # via flask-appbuilder
flask-login==0.6.2
    # via
    #   apache-airflow
    #   flask-appbuilder
flask-session==0.5.0
    # via apache-airflow
flask-sqlalchemy==2.5.1
    # via flask-appbuilder
flask-wtf==1.1.1
    # via
    #   apache-airflow
    #   flask-appbuilder
frozenlist==1.4.0
    # via
    #   aiohttp
    #   aiosignal
future==0.18.3
    # via pybigquery
gcloud-aio-auth==4.2.3
    # via
    #   apache-airflow-providers-google
    #   gcloud-aio-bigquery
    #   gcloud-aio-storage
gcloud-aio-bigquery==6.3.0
    # via apache-airflow-providers-google
gcloud-aio-storage==8.3.0
    # via apache-airflow-providers-google
google-ads==21.3.0
    # via apache-airflow-providers-google
google-api-core[grpc]==2.11.1
    # via
    #   apache-airflow-providers-google
    #   google-ads
    #   google-api-python-client
    #   google-cloud-aiplatform
    #   google-cloud-appengine-logging
    #   google-cloud-automl
    #   google-cloud-batch
    #   google-cloud-bigquery
    #   google-cloud-bigquery-datatransfer
    #   google-cloud-bigquery-storage
    #   google-cloud-bigtable
    #   google-cloud-build
    #   google-cloud-compute
    #   google-cloud-container
    #   google-cloud-core
    #   google-cloud-datacatalog
    #   google-cloud-dataflow-client
    #   google-cloud-dataform
    #   google-cloud-dataplex
    #   google-cloud-dataproc
    #   google-cloud-dataproc-metastore
    #   google-cloud-dlp
    #   google-cloud-kms
    #   google-cloud-language
    #   google-cloud-logging
    #   google-cloud-memcache
    #   google-cloud-monitoring
    #   google-cloud-orchestration-airflow
    #   google-cloud-os-login
    #   google-cloud-pubsub
    #   google-cloud-redis
    #   google-cloud-resource-manager
    #   google-cloud-run
    #   google-cloud-secret-manager
    #   google-cloud-spanner
    #   google-cloud-speech
    #   google-cloud-storage
    #   google-cloud-storage-transfer
    #   google-cloud-tasks
    #   google-cloud-texttospeech
    #   google-cloud-translate
    #   google-cloud-videointelligence
    #   google-cloud-vision
    #   google-cloud-workflows
    #   pandas-gbq
    #   pybigquery
    #   sqlalchemy-bigquery
google-api-python-client==2.97.0
    # via apache-airflow-providers-google
google-auth==2.22.0
    # via
    #   apache-airflow-providers-google
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-auth-oauthlib
    #   google-cloud-core
    #   google-cloud-storage
    #   pandas-gbq
    #   pybigquery
    #   pydata-google-auth
    #   sqlalchemy-bigquery
google-auth-httplib2==0.1.0
    # via
    #   apache-airflow-providers-google
    #   google-api-python-client
google-auth-oauthlib==1.0.0
    # via
    #   google-ads
    #   pandas-gbq
    #   pydata-google-auth
google-cloud-aiplatform==1.31.1
    # via apache-airflow-providers-google
google-cloud-appengine-logging==1.3.1
    # via google-cloud-logging
google-cloud-audit-log==0.2.5
    # via google-cloud-logging
google-cloud-automl==2.11.2
    # via apache-airflow-providers-google
google-cloud-batch==0.16.0
    # via apache-airflow-providers-google
google-cloud-bigquery==3.11.4
    # via
    #   google-cloud-aiplatform
    #   pandas-gbq
    #   pybigquery
    #   sqlalchemy-bigquery
google-cloud-bigquery-datatransfer==3.12.0
    # via apache-airflow-providers-google
google-cloud-bigquery-storage==2.22.0
    # via pandas-gbq
google-cloud-bigtable==2.21.0
    # via apache-airflow-providers-google
google-cloud-build==3.20.0
    # via apache-airflow-providers-google
google-cloud-compute==1.14.0
    # via apache-airflow-providers-google
google-cloud-container==2.30.0
    # via apache-airflow-providers-google
google-cloud-core==2.3.3
    # via
    #   google-cloud-bigquery
    #   google-cloud-bigtable
    #   google-cloud-logging
    #   google-cloud-spanner
    #   google-cloud-storage
    #   google-cloud-translate
google-cloud-datacatalog==3.15.0
    # via apache-airflow-providers-google
google-cloud-dataflow-client==0.8.4
    # via apache-airflow-providers-google
google-cloud-dataform==0.5.2
    # via apache-airflow-providers-google
google-cloud-dataplex==1.6.2
    # via apache-airflow-providers-google
google-cloud-dataproc==5.4.3
    # via apache-airflow-providers-google
google-cloud-dataproc-metastore==1.12.0
    # via apache-airflow-providers-google
google-cloud-dlp==3.12.2
    # via apache-airflow-providers-google
google-cloud-kms==2.19.1
    # via apache-airflow-providers-google
google-cloud-language==2.11.0
    # via apache-airflow-providers-google
google-cloud-logging==3.6.0
    # via apache-airflow-providers-google
google-cloud-memcache==1.7.2
    # via apache-airflow-providers-google
google-cloud-monitoring==2.15.1
    # via apache-airflow-providers-google
google-cloud-orchestration-airflow==1.9.1
    # via apache-airflow-providers-google
google-cloud-os-login==2.10.0
    # via apache-airflow-providers-google
google-cloud-pubsub==2.18.3
    # via apache-airflow-providers-google
google-cloud-redis==2.13.1
    # via apache-airflow-providers-google
google-cloud-resource-manager==1.10.3
    # via google-cloud-aiplatform
google-cloud-run==0.9.1
    # via apache-airflow-providers-google
google-cloud-secret-manager==2.16.3
    # via apache-airflow-providers-google
google-cloud-spanner==3.40.1
    # via
    #   apache-airflow-providers-google
    #   sqlalchemy-spanner
google-cloud-speech==2.21.0
    # via apache-airflow-providers-google
google-cloud-storage==2.10.0
    # via
    #   apache-airflow-providers-google
    #   google-cloud-aiplatform
google-cloud-storage-transfer==1.9.1
    # via apache-airflow-providers-google
google-cloud-tasks==2.14.1
    # via apache-airflow-providers-google
google-cloud-texttospeech==2.14.1
    # via apache-airflow-providers-google
google-cloud-translate==3.12.0
    # via apache-airflow-providers-google
google-cloud-videointelligence==2.11.3
    # via apache-airflow-providers-google
google-cloud-vision==3.4.4
    # via apache-airflow-providers-google
google-cloud-workflows==1.11.0
    # via apache-airflow-providers-google
google-crc32c==1.5.0
    # via google-resumable-media
google-re2==1.1
    # via apache-airflow
google-resumable-media==2.5.0
    # via
    #   google-cloud-bigquery
    #   google-cloud-storage
googleapis-common-protos[grpc]==1.60.0
    # via
    #   google-ads
    #   google-api-core
    #   google-cloud-audit-log
    #   grpc-google-iam-v1
    #   grpcio-status
graphviz==0.20.1
    # via apache-airflow
grpc-google-iam-v1==0.12.6
    # via
    #   google-cloud-bigtable
    #   google-cloud-build
    #   google-cloud-datacatalog
    #   google-cloud-dataform
    #   google-cloud-dataplex
    #   google-cloud-dataproc
    #   google-cloud-dataproc-metastore
    #   google-cloud-kms
    #   google-cloud-logging
    #   google-cloud-pubsub
    #   google-cloud-resource-manager
    #   google-cloud-run
    #   google-cloud-secret-manager
    #   google-cloud-spanner
    #   google-cloud-tasks
grpcio==1.57.0
    # via
    #   google-ads
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-gcp
    #   grpcio-status
grpcio-gcp==0.2.2
    # via apache-airflow-providers-google
grpcio-status==1.57.0
    # via
    #   google-ads
    #   google-api-core
    #   google-cloud-pubsub
gunicorn==21.2.0
    # via apache-airflow
h11==0.14.0
    # via httpcore
httpcore==0.17.3
    # via httpx
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
    #   oauth2client
httpx==0.24.1
    # via
    #   apache-airflow
    #   apache-airflow-providers-google
idna==3.4
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
importlib-metadata==6.8.0
    # via
    #   flask
    #   markdown
importlib-resources==6.0.1
    # via limits
inflection==0.5.1
    # via connexion
itsdangerous==2.1.2
    # via
    #   apache-airflow
    #   connexion
    #   flask
    #   flask-wtf
jinja2==3.1.2
    # via
    #   apache-airflow
    #   flask
    #   flask-babel
    #   python-nvd3
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
json-merge-patch==0.2
    # via apache-airflow-providers-google
jsonpath-ng==1.5.3
    # via apache-airflow-providers-amazon
jsonschema==4.19.0
    # via
    #   apache-airflow
    #   connexion
    #   flask-appbuilder
jsonschema-specifications==2023.7.1
    # via jsonschema
lazy-object-proxy==1.9.0
    # via apache-airflow
limits==3.6.0
    # via flask-limiter
linkify-it-py==2.0.2
    # via apache-airflow
lockfile==0.12.2
    # via
    #   apache-airflow
    #   python-daemon
looker-sdk==23.14.1
    # via apache-airflow-providers-google
lxml==4.9.3
    # via redshift-connector
lz4==4.3.2
    # via -r docker/airflow/requirements.in
mako==1.2.4
    # via alembic
markdown==3.4.4
    # via apache-airflow
markdown-it-py==3.0.0
    # via
    #   apache-airflow
    #   mdit-py-plugins
    #   rich
markupsafe==2.1.3
    # via
    #   apache-airflow
    #   jinja2
    #   mako
    #   werkzeug
    #   wtforms
marshmallow==3.20.1
    # via
    #   flask-appbuilder
    #   marshmallow-enum
    #   marshmallow-oneofschema
    #   marshmallow-sqlalchemy
marshmallow-enum==1.5.1
    # via flask-appbuilder
marshmallow-oneofschema==3.0.1
    # via apache-airflow
marshmallow-sqlalchemy==0.26.1
    # via flask-appbuilder
mdit-py-plugins==0.4.0
    # via apache-airflow
mdurl==0.1.2
    # via markdown-it-py
multidict==6.0.4
    # via
    #   aiohttp
    #   yarl
mypy-boto3-appflow==1.28.38
    # via apache-airflow-providers-amazon
mypy-boto3-rds==1.28.36
    # via apache-airflow-providers-amazon
mypy-boto3-redshift-data==1.28.36
    # via apache-airflow-providers-amazon
mypy-boto3-s3==1.28.36
    # via apache-airflow-providers-amazon
mysql-connector-python==8.1.0
    # via apache-airflow-providers-mysql
mysqlclient==2.2.0
    # via apache-airflow-providers-mysql
numpy==1.25.2
    # via
    #   db-dtypes
    #   pandas
    #   pandas-gbq
    #   pandas-schema
    #   pyarrow
oauth2client==4.1.3
    # via -r docker/airflow/requirements.in
oauthlib==3.2.2
    # via requests-oauthlib
ordered-set==4.1.0
    # via flask-limiter
packaging==23.1
    # via
    #   apache-airflow
    #   connexion
    #   db-dtypes
    #   google-cloud-aiplatform
    #   google-cloud-bigquery
    #   gunicorn
    #   limits
    #   marshmallow
    #   pandas-schema
    #   redshift-connector
    #   sqlalchemy-bigquery
    #   sqlalchemy-redshift
pandas==2.1.0
    # via
    #   -r docker/airflow/requirements.in
    #   apache-airflow-providers-google
    #   db-dtypes
    #   pandas-gbq
    #   pandas-schema
pandas-gbq==0.19.2
    # via apache-airflow-providers-google
pandas-schema==0.3.6
    # via -r docker/airflow/requirements.in
pathspec==0.9.0
    # via apache-airflow
pendulum==2.1.2
    # via apache-airflow
pluggy==1.3.0
    # via apache-airflow
ply==3.11
    # via jsonpath-ng
prison==0.2.1
    # via flask-appbuilder
proto-plus==1.22.3
    # via
    #   apache-airflow-providers-google
    #   google-ads
    #   google-cloud-aiplatform
    #   google-cloud-appengine-logging
    #   google-cloud-automl
    #   google-cloud-batch
    #   google-cloud-bigquery
    #   google-cloud-bigquery-datatransfer
    #   google-cloud-bigquery-storage
    #   google-cloud-bigtable
    #   google-cloud-build
    #   google-cloud-compute
    #   google-cloud-container
    #   google-cloud-datacatalog
    #   google-cloud-dataflow-client
    #   google-cloud-dataform
    #   google-cloud-dataplex
    #   google-cloud-dataproc
    #   google-cloud-dataproc-metastore
    #   google-cloud-dlp
    #   google-cloud-kms
    #   google-cloud-language
    #   google-cloud-logging
    #   google-cloud-memcache
    #   google-cloud-monitoring
    #   google-cloud-orchestration-airflow
    #   google-cloud-os-login
    #   google-cloud-pubsub
    #   google-cloud-redis
    #   google-cloud-resource-manager
    #   google-cloud-run
    #   google-cloud-secret-manager
    #   google-cloud-spanner
    #   google-cloud-speech
    #   google-cloud-storage-transfer
    #   google-cloud-tasks
    #   google-cloud-texttospeech
    #   google-cloud-translate
    #   google-cloud-videointelligence
    #   google-cloud-vision
    #   google-cloud-workflows
protobuf==4.21.12
    # via
    #   google-ads
    #   google-api-core
    #   google-cloud-aiplatform
    #   google-cloud-appengine-logging
    #   google-cloud-audit-log
    #   google-cloud-automl
    #   google-cloud-batch
    #   google-cloud-bigquery
    #   google-cloud-bigquery-datatransfer
    #   google-cloud-bigquery-storage
    #   google-cloud-bigtable
    #   google-cloud-build
    #   google-cloud-compute
    #   google-cloud-container
    #   google-cloud-datacatalog
    #   google-cloud-dataflow-client
    #   google-cloud-dataform
    #   google-cloud-dataplex
    #   google-cloud-dataproc
    #   google-cloud-dataproc-metastore
    #   google-cloud-dlp
    #   google-cloud-kms
    #   google-cloud-language
    #   google-cloud-logging
    #   google-cloud-memcache
    #   google-cloud-monitoring
    #   google-cloud-orchestration-airflow
    #   google-cloud-os-login
    #   google-cloud-pubsub
    #   google-cloud-redis
    #   google-cloud-resource-manager
    #   google-cloud-run
    #   google-cloud-secret-manager
    #   google-cloud-spanner
    #   google-cloud-speech
    #   google-cloud-storage-transfer
    #   google-cloud-tasks
    #   google-cloud-texttospeech
    #   google-cloud-translate
    #   google-cloud-videointelligence
    #   google-cloud-vision
    #   google-cloud-workflows
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   mysql-connector-python
    #   proto-plus
psutil==5.9.5
    # via apache-airflow
pyaes==1.6.1
    # via telethon
pyarrow==13.0.0
    # via
    #   -r docker/airflow/requirements.in
    #   db-dtypes
    #   pandas-gbq
pyasn1==0.4.8
    # via
    #   oauth2client
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.2.8
    # via
    #   gcloud-aio-storage
    #   google-auth
    #   oauth2client
pybigquery==0.10.2
    # via -r docker/airflow/requirements.in
pycparser==2.21
    # via cffi
pydantic==1.10.12
    # via apache-airflow
pydata-google-auth==1.8.2
    # via pandas-gbq
pygments==2.16.1
    # via
    #   apache-airflow
    #   rich
pyjwt==2.8.0
    # via
    #   apache-airflow
    #   flask-appbuilder
    #   flask-jwt-extended
    #   gcloud-aio-auth
pymongo==4.5.0
    # via
    #   -r docker/airflow/requirements.in
    #   apache-airflow-providers-mongo
pyopenssl==23.2.0
    # via apache-airflow-providers-google
pyparsing==3.1.1
    # via httplib2
python-daemon==3.0.1
    # via apache-airflow
python-dateutil==2.8.2
    # via
    #   apache-airflow
    #   botocore
    #   croniter
    #   flask-appbuilder
    #   google-cloud-bigquery
    #   pandas
    #   pendulum
python-nvd3==0.15.0
    # via apache-airflow
python-slugify==8.0.1
    # via
    #   apache-airflow
    #   python-nvd3
pytz==2023.3
    # via
    #   -r docker/airflow/requirements.in
    #   clickhouse-driver
    #   flask-babel
    #   pandas
    #   redshift-connector
pytzdata==2020.1
    # via pendulum
pyyaml==6.0.1
    # via
    #   apispec
    #   clickclick
    #   connexion
    #   google-ads
redis==5.0.0
    # via -r docker/airflow/requirements.in
redshift-connector==2.0.913
    # via apache-airflow-providers-amazon
referencing==0.30.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.31.0
    # via
    #   apache-airflow-providers-http
    #   connexion
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-storage
    #   looker-sdk
    #   redshift-connector
    #   requests-oauthlib
    #   requests-toolbelt
requests-oauthlib==1.3.1
    # via google-auth-oauthlib
requests-toolbelt==1.0.0
    # via apache-airflow-providers-http
rfc3339-validator==0.1.4
    # via apache-airflow
rich==13.5.2
    # via
    #   apache-airflow
    #   flask-limiter
    #   rich-argparse
rich-argparse==1.3.0
    # via apache-airflow
rpds-py==0.10.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via
    #   gcloud-aio-storage
    #   google-auth
    #   oauth2client
    #   telethon
s3transfer==0.6.2
    # via boto3
scramp==1.4.4
    # via redshift-connector
setproctitle==1.3.2
    # via apache-airflow
shapely==1.8.5.post1
    # via google-cloud-aiplatform
six==1.16.0
    # via
    #   google-auth
    #   google-auth-httplib2
    #   jsonpath-ng
    #   oauth2client
    #   prison
    #   python-dateutil
    #   rfc3339-validator
slack-sdk==3.21.3
    # via apache-airflow-providers-slack
slackclient==2.9.4
    # via -r docker/airflow/requirements.in
sniffio==1.3.0
    # via
    #   anyio
    #   httpcore
    #   httpx
soupsieve==2.4.1
    # via beautifulsoup4
sqlalchemy==1.4.49
    # via
    #   alembic
    #   apache-airflow
    #   flask-appbuilder
    #   flask-sqlalchemy
    #   marshmallow-sqlalchemy
    #   pybigquery
    #   sqlalchemy-bigquery
    #   sqlalchemy-jsonfield
    #   sqlalchemy-redshift
    #   sqlalchemy-spanner
    #   sqlalchemy-utils
sqlalchemy-bigquery==1.8.0
    # via apache-airflow-providers-google
sqlalchemy-jsonfield==1.0.1.post0
    # via apache-airflow
sqlalchemy-redshift==0.8.14
    # via apache-airflow-providers-amazon
sqlalchemy-spanner==1.6.2
    # via apache-airflow-providers-google
sqlalchemy-utils==0.41.1
    # via flask-appbuilder
sqlparse==0.4.4
    # via
    #   apache-airflow-providers-common-sql
    #   google-cloud-spanner
tabulate==0.9.0
    # via apache-airflow
telethon==1.29.3
    # via -r docker/airflow/requirements.in
tenacity==8.2.3
    # via apache-airflow
termcolor==2.3.0
    # via apache-airflow
text-unidecode==1.3
    # via python-slugify
typing-extensions==4.7.1
    # via
    #   alembic
    #   apache-airflow
    #   asgiref
    #   cattrs
    #   flask-limiter
    #   limits
    #   looker-sdk
    #   mypy-boto3-appflow
    #   mypy-boto3-rds
    #   mypy-boto3-redshift-data
    #   mypy-boto3-s3
    #   pydantic
tzdata==2023.3
    # via pandas
tzlocal==5.0.1
    # via
    #   -r docker/airflow/requirements.in
    #   clickhouse-driver
uc-micro-py==1.0.2
    # via linkify-it-py
unicodecsv==0.14.1
    # via apache-airflow
uritemplate==4.1.1
    # via google-api-python-client
urllib3==1.26.16
    # via
    #   botocore
    #   google-auth
    #   requests
uuid==1.30
    # via -r docker/airflow/requirements.in
watchtower==2.0.1
    # via apache-airflow-providers-amazon
werkzeug==2.2.3
    # via
    #   apache-airflow
    #   connexion
    #   flask
    #   flask-jwt-extended
    #   flask-login
wrapt==1.15.0
    # via deprecated
wtforms==3.0.1
    # via
    #   flask-appbuilder
    #   flask-wtf
yarl==1.9.2
    # via aiohttp
zipp==3.16.2
    # via
    #   importlib-metadata
    #   importlib-resources

# The following packages are considered to be unsafe in a requirements file:
# setuptools
